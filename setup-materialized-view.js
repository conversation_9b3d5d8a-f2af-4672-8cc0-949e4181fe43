#!/usr/bin/env node

// <PERSON><PERSON>t to setup the materialized view for the leaderboard
// This script reads the SQL file and executes it against the database

import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });
dotenv.config({ path: '.env' });

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function setupMaterializedView() {
  const databaseUrl = process.env.DATABASE_URL;
  
  if (!databaseUrl) {
    console.error('❌ DATABASE_URL environment variable is not set');
    console.error('Make sure you have a .env.local file with DATABASE_URL configured');
    process.exit(1);
  }

  console.log('🔗 Connecting to database...');
  
  const sql = postgres(databaseUrl);
  
  try {
    // Read the SQL setup file
    const sqlFilePath = join(__dirname, 'setup_materialized_view.sql');
    const setupSQL = readFileSync(sqlFilePath, 'utf8');
    
    console.log('📄 Executing materialized view setup...');
    
    // Split the SQL into individual statements and execute them
    const statements = setupSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
    
    for (const statement of statements) {
      if (statement.trim()) {
        try {
          await sql.unsafe(statement);
          console.log('✅ Executed:', statement.substring(0, 50) + '...');
        } catch (error) {
          // Some statements might fail if objects already exist, that's okay
          if (error.message.includes('already exists')) {
            console.log('ℹ️  Skipped (already exists):', statement.substring(0, 50) + '...');
          } else {
            console.error('❌ Error executing statement:', statement.substring(0, 50) + '...');
            console.error('Error:', error.message);
          }
        }
      }
    }
    
    // Test the materialized view
    console.log('🧪 Testing materialized view...');
    const result = await sql`SELECT COUNT(*) as count FROM user_points_leaderboard_mv`;
    console.log(`✅ Materialized view created successfully! Found ${result[0].count} users in leaderboard.`);
    
  } catch (error) {
    console.error('❌ Failed to setup materialized view:', error.message);
    process.exit(1);
  } finally {
    await sql.end();
  }
}

setupMaterializedView();