# Automatic Logout Implementation

This document describes the implementation of automatic user logout and redirection upon session expiration or 401 responses.

## Overview

The automatic logout system provides a seamless user experience by:
- Monitoring session state using `better-auth` React hooks
- Intercepting 401 responses from API calls
- Automatically signing out users when sessions expire
- Redirecting users to the login page
- Providing consistent error handling across the application

## Implementation Components

### 1. Session Monitor (`app/lib/auth/session-monitor.tsx`)

**Purpose**: Monitors user session state and handles automatic logout.

**Key Features**:
- Uses `useSession` hook from `better-auth/react` to monitor session state
- Detects session errors and expiration
- Automatically signs out users and redirects to login page
- Handles session expiration events from global fetch interceptor

**Components**:
- `SessionMonitor`: Main component that monitors session state
- `GlobalSessionHandler`: Handles global session expiration events
- `setupGlobalFetchInterceptor`: Intercepts fetch requests to detect 401 responses

### 2. Auth Provider (`app/lib/auth/auth-provider.tsx`)

**Purpose**: Wraps the application with authentication context and session monitoring.

**Features**:
- Sets up global fetch interceptor on component mount
- Integrates `SessionMonitor` and `GlobalSessionHandler`
- Provides authentication context to the entire application

### 3. Enhanced Error Handling

**Updated Files**:
- `app/lib/error-handling.ts`: Enhanced router error handling with session expiration events
- `app/lib/data-fetching.ts`: Enhanced API error handling with 401 response detection

**Features**:
- Dispatches `session-expired` custom events on 401 responses
- Integrates with existing error handling infrastructure
- Provides consistent error handling across router and API calls

## Integration

The system is integrated into the application through:

1. **Providers Setup** (`app/lib/providers.tsx`):
   ```tsx
   <QueryProvider>
     <AuthProvider>
       <ThemeProvider>
         {children}
       </ThemeProvider>
     </AuthProvider>
   </QueryProvider>
   ```

2. **Global Fetch Interceptor**:
   - Automatically intercepts all fetch requests
   - Detects 401 responses and dispatches session expiration events
   - Works with both direct fetch calls and React Query requests

3. **Session State Monitoring**:
   - Continuously monitors session state using `better-auth` hooks
   - Handles session errors and expiration automatically
   - Provides seamless logout and redirection

## Security Features

1. **Automatic Session Cleanup**:
   - Calls `signOut()` from `better-auth` to properly clean up session
   - Clears client-side session data
   - Invalidates authentication tokens

2. **Secure Redirection**:
   - Uses React Router's `useNavigate` for client-side navigation
   - Redirects to `/login` page consistently
   - Prevents unauthorized access to protected routes

3. **Global Coverage**:
   - Intercepts all HTTP requests through global fetch interceptor
   - Handles 401 responses from any API endpoint
   - Works with both server-side and client-side requests

## Testing

### Manual Testing

1. **Test Session Expiration**:
   ```bash
   # Login first
   curl -s -X POST http://localhost:5173/api/auth/sign-in/email \
     -H "Content-Type: application/json" \
     -c /tmp/cookies.txt \
     -d '{"email": "<EMAIL>", "password": "12341234"}'
   
   # Test 401 response
   curl -s http://localhost:5173/api/test-401 -w "\nHTTP Status: %{http_code}\n"
   ```

2. **Browser Testing**:
   - Open browser console on the application
   - Load and run the test script: `test-session-functionality.js`
   - Or open the test page: `test-session-monitor.html`

3. **Test Script Usage**:
   ```javascript
   // In browser console
   window.sessionTests.runAllTests();
   
   // Or run individual tests
   window.sessionTests.test401Response();
   window.sessionTests.testSessionExpiredEvent();
   ```

### Automated Testing

The implementation includes:
- Test API endpoint (`/api/test-401`) that always returns 401
- Comprehensive test script for browser console testing
- Test HTML page for interactive testing

## Configuration

### Session Settings

Session configuration is handled by `better-auth` in `app/lib/auth.server.ts`:
- Session expiry: 7 days
- Session update age: 1 day

### Error Handling

Error codes and handling are defined in `app/lib/error-handling.ts`:
- `UNAUTHORIZED`: 401 responses
- `SESSION_EXPIRED`: Session expiration
- `FORBIDDEN`: 403 responses

## Troubleshooting

### Common Issues

1. **Session not expiring automatically**:
   - Check browser console for JavaScript errors
   - Verify that `AuthProvider` is properly wrapped around the app
   - Ensure `better-auth` client is configured correctly

2. **401 responses not triggering logout**:
   - Verify global fetch interceptor is set up
   - Check that session expired events are being dispatched
   - Ensure event listeners are properly attached

3. **Redirection not working**:
   - Check React Router configuration
   - Verify `useNavigate` hook is available
   - Ensure `/login` route exists and is accessible

### Debug Commands

```bash
# Check if server is running
curl -s http://localhost:5173/ -w "\nHTTP Status: %{http_code}\n"

# Test authentication endpoint
curl -s http://localhost:5173/api/auth/sign-in/email \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "12341234"}'

# Test 401 response
curl -s http://localhost:5173/api/test-401 -w "\nHTTP Status: %{http_code}\n"
```

### Browser Console Debugging

```javascript
// Check if session monitor is loaded
console.log('Original fetch:', window.originalFetch);

// Test session expired event
window.dispatchEvent(new CustomEvent('session-expired', {
  detail: { reason: 'Manual test', source: 'debug' }
}));

// Check current session state (if useSession hook is available)
// This would need to be checked within a React component
```

## Future Enhancements

1. **Session Warning**: Show warning before session expires
2. **Retry Logic**: Automatic retry for failed requests after token refresh
3. **Offline Handling**: Handle network connectivity issues
4. **Session Extension**: Allow users to extend sessions
5. **Multiple Tab Sync**: Synchronize logout across browser tabs

## Dependencies

- `better-auth/react`: Session management and authentication
- `react-router`: Navigation and routing
- `@tanstack/react-query`: API state management (optional integration)

The implementation is designed to be:
- **Secure**: Proper session cleanup and token invalidation
- **Seamless**: Automatic handling without user intervention
- **Robust**: Comprehensive error handling and edge case coverage
- **Testable**: Includes testing utilities and documentation