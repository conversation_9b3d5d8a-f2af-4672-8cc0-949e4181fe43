import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { AuthProvider } from '~/lib/auth/auth-provider';

function Child() {
  return <div data-testid="child">ok</div>;
}

test('AuthProvider renders children', async () => {
  render(
    <AuthProvider>
      <Child />
    </AuthProvider>
  );

  await waitFor(() => {
    expect(screen.getByTestId('child')).toBeInTheDocument();
  });
});

