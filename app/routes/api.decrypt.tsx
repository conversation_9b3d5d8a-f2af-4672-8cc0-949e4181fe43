import type { ActionFunctionArgs } from "react-router";
import { z } from "zod";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { decryptQuestionAnswers, decryptAnswerData } from "~/lib/crypto.server";

const schema = z.object({
  data: z.any(), // The encrypted data to decrypt
  type: z.enum(["question", "raw"]).optional().default("raw"), // Type of decryption
  quizId: z.string().optional(),
});

export async function action({ request }: ActionFunctionArgs) {
  try {
    // Require authentication for security
    await requireAuthSession(request);
    
    const input = schema.parse(await request.json());
    
    let decryptedData;
    
    if (input.type === "question") {
      // Use the existing question decryption function
      decryptedData = await decryptQuestionAnswers(input.data, { quizId: input.quizId });
    } else {
      // Raw decryption for general encrypted strings
      if (typeof input.data !== "string") {
        return Response.json(
          { success: false, error: "Raw decryption requires a string input" },
          { status: 400 }
        );
      }
      
      try {
        // Use the existing decryptAnswerData function
        decryptedData = await decryptAnswerData(input.data, { quizId: input.quizId });
      } catch (error) {
        return Response.json(
          { 
            success: false, 
            error: "Failed to decrypt data",
            details: error instanceof Error ? error.message : "Unknown error"
          },
          { status: 400 }
        );
      }
    }
    
    return Response.json({
      success: true,
      data: {
        original: input.data,
        decrypted: decryptedData,
        type: input.type
      }
    });
    
  } catch (error) {
    console.error("Decryption endpoint error:", error);
    
    if (error instanceof z.ZodError) {
      return Response.json(
        { success: false, error: "Invalid input", details: error.errors },
        { status: 400 }
      );
    }
    
    return Response.json(
      { 
        success: false, 
        error: "Internal server error",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

// GET method for simple health check
export async function loader() {
  return Response.json({
    endpoint: "decrypt",
    description: "POST endpoint to decrypt encrypted data",
    usage: {
      method: "POST",
      body: {
        data: "encrypted_string_or_object",
        type: "question | raw (default: raw)"
      }
    }
  });
}