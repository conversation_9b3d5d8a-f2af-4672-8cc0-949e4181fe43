import type { LoaderFunctionArgs, MetaFunction } from "react-router";
import { useState, useMemo, useEffect, useCallback, useRef } from "react";
import { Link } from "react-router";
import {
  Plus,
  BookOpen,
  AlertCircle,
  Zap,
  Loader2,
  Refresh<PERSON>w
} from "lucide-react";

import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardTitle } from "~/components/ui/card";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { Badge } from "~/components/ui/badge";
import { 
  useGetQuizzesInfinite,
  useGetQuizAttempts
} from "~/lib/hooks/use-quiz-api";
import type { QuizWithProgress, GroupedQuizzes, QuizzesResponse } from "~/routes/api.quizzes";
import { AdvancedQuizFilters } from "~/components/quiz/AdvancedQuizFilters";
import { QuizCard } from "~/components/quiz/QuizCard";
import { EnhancedGroupedQuizView } from "~/components/quiz/EnhancedGroupedQuizView";
import type { QuizzesFilters } from "~/routes/api.quizzes";
import type { QuizFilters as ComponentQuizFilters } from "~/components/quiz/AdvancedQuizFilters";
import { StickyFooter } from "~/components/shared/StickyFooter";
import { ContentSkeleton } from "~/components/shared/ContentSkeleton";
import { ViewControls } from "~/components/shared/ViewControls";

export const meta: MetaFunction = () => {
  return [
    { title: "Quizzes - Kwaci Learning" },
    { name: "description", content: "Manage and take your learning quizzes" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Server-side authentication validation
  const { requireAuth } = await import('~/lib/auth/supabase-server');
  const authContext = await requireAuth(request);
  
  return {
    user: {
      id: authContext.user!.id,
      email: authContext.user!.email,
    },
  };
}

// Quiz filters interface  
interface QuizFilters {
  search: string;
  difficulty: 'all' | 'easy' | 'medium' | 'hard';
  completionStatus: 'all' | 'completed' | 'in-progress' | 'not-started';
  learningContentId?: string;
  limit: number;
}

export default function QuizzesPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [groupedView, setGroupedView] = useState(false);
  const [filters, setFilters] = useState<QuizFilters>({
    search: '',
    difficulty: 'all',
    completionStatus: 'all',
    limit: 12,
  });
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(filters.search);
    }, 500);

    return () => clearTimeout(timer);
  }, [filters.search]);

  // Memoize filters to prevent infinite re-renders
  const memoizedFilters = useMemo(() => ({
    search: debouncedSearch || undefined,
    difficulty: filters.difficulty !== 'all' ? filters.difficulty : undefined,
    completionStatus: filters.completionStatus,
    learningContentId: filters.learningContentId,
    limit: filters.limit,
    includePublic: true,
    grouped: groupedView,
  }), [debouncedSearch, filters.difficulty, filters.completionStatus, filters.learningContentId, filters.limit, groupedView]);

  // React Query infinite hooks
  const { 
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
    refetch
  } = useGetQuizzesInfinite(memoizedFilters);

  const { 
    data: attemptsData 
  } = useGetQuizAttempts({ limit: 100 });

  const handleFiltersChange = (newFilters: ComponentQuizFilters) => {
    setFilters({ ...newFilters, limit: filters.limit });
  };

  const handleClearFilters = () => {
    setFilters({
      search: '',
      difficulty: 'all',
      completionStatus: 'all',
      limit: 12,
    });
  };

  // Flatten all pages into single array
  const quizzes = useMemo((): QuizWithProgress[] => {
    if (!data?.pages) return [];
    return data.pages.flatMap((page: QuizzesResponse) => page.quizzes || []);
  }, [data]);
  
  const groupedQuizzes = useMemo((): GroupedQuizzes[] => {
    if (!data?.pages || !groupedView) return [];
    return data.pages.flatMap((page: QuizzesResponse) => page.grouped || []);
  }, [data, groupedView]);
  
  const hasFilters = filters.search || filters.difficulty !== 'all' || filters.completionStatus !== 'all';

  // Setup infinite scroll observer
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasNextPage && !isFetchingNextPage && !isLoading) {
          fetchNextPage();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current && hasNextPage) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [fetchNextPage, hasNextPage, isFetchingNextPage, isLoading]);

  return (
    <DashboardLayout>
      <div className="relative py-8 px-6 pb-16">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Quizzes
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Test your knowledge and track your progress
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Link to="/dashboard/learn">
              <Button variant="outline" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Create Content
              </Button>
            </Link>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="mb-6">
          <AdvancedQuizFilters
            filters={{
              search: filters.search,
              difficulty: filters.difficulty,
              completionStatus: filters.completionStatus,
              learningContentId: filters.learningContentId,
            }}
            onFiltersChange={handleFiltersChange}
            onClearFilters={handleClearFilters}
          />
        </div>



        {/* View Controls */}
        <div className="flex items-center justify-between mb-6">
          <ViewControls
            viewMode={viewMode}
            onViewModeChange={setViewMode}
          />

          {/* Route-specific option stays here */}
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={groupedView}
                onChange={(e) => setGroupedView(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                Group by content
              </span>
            </label>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Loading State */}
          {isLoading && (
            <ContentSkeleton
              viewMode={viewMode}
              variant={viewMode === 'grid' ? 'card' : 'list'}
              count={6}
              gridClasses="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
            />
          )}

          {/* Error State */}
          {isError && (
            <Alert className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>Failed to load quizzes. Please try again.</span>
                <Button variant="outline" size="sm" onClick={() => refetch()}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Retry
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Empty State */}
          {!isLoading && !isError && quizzes.length === 0 && (
            <Card className="text-center py-12">
              <CardContent>
                <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <CardTitle className="mb-2">
                  {hasFilters ? 'No quizzes match your filters' : 'No quizzes found'}
                </CardTitle>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {hasFilters 
                    ? 'Try adjusting your filters or search terms.'
                    : 'Get started by creating learning content first, then generate quizzes to test your knowledge.'
                  }
                </p>
                <div className="flex items-center justify-center gap-2">
                  {hasFilters && (
                    <Button variant="outline" onClick={handleClearFilters}>
                      Clear Filters
                    </Button>
                  )}
                  <Link to="/dashboard/learn">
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Content
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Quiz Content */}
          {!isLoading && !isError && quizzes.length > 0 && (
            <>
              {groupedView && groupedQuizzes.length > 0 ? (
                <EnhancedGroupedQuizView
                  groupedQuizzes={groupedQuizzes}
                  viewMode={viewMode}
                />
              ) : (
                <div className={
                  viewMode === 'grid'
                    ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
                    : "space-y-4"
                }>
                  {quizzes.map((quiz: any) => (
                    <QuizCard
                      key={quiz.id}
                      quiz={quiz}
                      viewMode={viewMode}
                    />
                  ))}
                </div>
              )}

                {/* Infinite Scroll Trigger */}
                {hasNextPage && (
                  <div ref={loadMoreRef} className="text-center mt-8 py-4">
                    {isFetchingNextPage && (
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>Loading more quizzes...</span>
                      </div>
                    )}
                  </div>
                )}
            </>
          )}
        </div>

        {/* Sticky Footer with Stats */}
        {!isLoading && quizzes.length > 0 && (
          <StickyFooter
            offsetClassName="md:left-64"
            items={[
              {
                key: 'total',
                content: (
                  <div className="flex items-center gap-1.5">
                    <BookOpen className="h-3 w-3" />
                    <span>
                      Total: <span className="font-medium text-gray-700 dark:text-gray-300">{quizzes.length}</span>
                    </span>
                  </div>
                )
              },
              {
                key: 'completed',
                content: (
                  <div className="flex items-center gap-1.5">
                    <div className="h-2 w-2 bg-green-500 rounded-full" />
                    <span>
                      Completed: <span className="font-medium text-gray-700 dark:text-gray-300">{quizzes.filter((q: any) => q.progress?.status === 'completed').length}</span>
                    </span>
                  </div>
                )
              },
              {
                key: 'in-progress',
                content: (
                  <div className="flex items-center gap-1.5">
                    <div className="h-2 w-2 bg-blue-500 rounded-full" />
                    <span>
                      In Progress: <span className="font-medium text-gray-700 dark:text-gray-300">{quizzes.filter((q: any) => q.progress?.status === 'in-progress').length}</span>
                    </span>
                  </div>
                )
              },
              {
                key: 'not-started',
                content: (
                  <div className="flex items-center gap-1.5">
                    <div className="h-2 w-2 bg-gray-400 rounded-full" />
                    <span>
                      Not Started: <span className="font-medium text-gray-700 dark:text-gray-300">{quizzes.filter((q: any) => q.progress?.status === 'not-started').length}</span>
                    </span>
                  </div>
                )
              },
            ]}
          />
        )}
      </div>
    </DashboardLayout>
  );
}
