/**
 * Test page for AI and RAG services
 */

import type { LoaderFunctionArgs } from 'react-router';
// json helper removed - using Response.json instead
import { useLoaderData } from 'react-router';
import { SimpleServiceHealth, SimpleChunkingService } from '~/lib/ai-rag.simple.server';
import { apiClient } from '~/lib/data-fetching';

export async function loader({ request }: LoaderFunctionArgs) {
  // Client-side auth handled via AuthProvider; do not redirect here to avoid loops
  // Return mock data for testing - actual AI service calls will be made client-side
  return Response.json({
    success: true,
    serviceStatus: {
      status: 'healthy',
      services: {
        contentGeneration: true,
        vectorSearch: true,
        chunking: true,
        embedding: true
      },
      errors: [],
      timestamp: new Date().toISOString()
    },
    chunkingTest: {
      chunks: [
        "This is a sample learning content about artificial intelligence.",
        "AI is a fascinating field that combines computer science with cognitive psychology.",
        "Machine learning is a subset of AI that focuses on algorithms that can learn from data."
      ],
      metadata: {
        totalChunks: 3,
        method: 'sentence-based'
      }
    }
  });
}

export default function AITestPage() {
  const data = useLoaderData<typeof loader>() as any;

  return (
    <div className="max-w-4xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">AI & RAG Services Test</h1>
      
      <div className="space-y-6">
        {/* Service Status */}
        <div className="border rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-3">Service Status</h2>
          <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
            data?.serviceStatus?.status === 'ready' 
              ? 'bg-green-100 text-green-800' 
              : 'bg-yellow-100 text-yellow-800'
          }`}>
            {data?.serviceStatus?.status || 'unknown'}
          </div>
          
          <div className="mt-4 space-y-2">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${
                data?.serviceStatus?.ai?.contentGeneration ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span>Content Generation: {data?.serviceStatus?.ai?.contentGeneration ? 'Ready' : 'Not Configured'}</span>
            </div>
            
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${
                data?.serviceStatus?.ai?.embedding ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span>Embedding Service: {data?.serviceStatus?.ai?.embedding ? 'Ready' : 'Not Configured'}</span>
            </div>
          </div>
          
          {data?.serviceStatus?.errors && data.serviceStatus.errors.length > 0 && (
            <div className="mt-4">
              <h3 className="font-medium text-red-600 mb-2">Configuration Issues:</h3>
              <ul className="text-sm text-red-600 space-y-1">
                {data.serviceStatus.errors.map((error: any, index: any) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>

        {/* Chunking Test */}
        <div className="border rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-3">Content Chunking Test</h2>
          
          {data?.chunkingTest?.success ? (
            <div>
              <p className="text-green-600 mb-3">✓ Chunking service working correctly</p>
              <div className="bg-muted p-3 rounded">
                <h3 className="font-medium mb-2">Generated Chunks ({data.chunkingTest.data?.length || 0}):</h3>
                <div className="space-y-2">
                  {data.chunkingTest.data?.map((chunk: any, index: any) => (
                    <div key={index} className="text-sm border-l-4 border-blue-400 pl-3 py-1">
                      <div className="font-medium">Chunk {index + 1} ({chunk.tokens} tokens):</div>
                      <div className="text-muted-foreground">{chunk.text}</div>
                    </div>
                  )) || []}
                </div>
              </div>
            </div>
          ) : (
            <div>
              <p className="text-red-600 mb-2">✗ Chunking test failed</p>
              {data?.chunkingTest?.error && (
                <p className="text-sm text-red-600">{data.chunkingTest.error}</p>
              )}
            </div>
          )}
        </div>

        {/* API Endpoints */}
        <div className="border rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-3">Available API Endpoints</h2>
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-mono bg-muted px-2 py-1 rounded">GET /api/ai-rag/status</span>
              <span className="ml-2 text-muted-foreground">Check service health</span>
            </div>
            <div>
              <span className="font-mono bg-muted px-2 py-1 rounded">POST /api/content/generate</span>
              <span className="ml-2 text-muted-foreground">Generate learning content (Enhanced)</span>
            </div>
          </div>
        </div>

        {/* Enhanced Content Generation Test */}
        <div className="border rounded-lg p-4">
          <h2 className="text-xl font-semibold mb-3">Enhanced Content Generation Test</h2>
          <p className="text-sm text-muted-foreground mb-4">
            Test the enhanced content generation with learning-monorepo feature parity
          </p>
          <button
            onClick={async () => {
              try {
                const result = await apiClient.generateContent({
                  topic: 'How does photosynthesis work?',
                  learningLevel: 'beginner',
                  preferredContentTypes: ['paragraph', 'bulletList', 'infoBox'],
                  additionalContext: 'Focus on the basic process and key components'
                });
                console.log('Enhanced content generation result:', result);
                alert(result.success ? 'Content generated successfully! Check console for details.' : `Error: ${result.error}`);
              } catch (error) {
                console.error('Content generation test failed:', error);
                alert('Test failed - check console for details');
              }
            }}
            className="bg-success-600 text-success-foreground px-4 py-2 rounded hover:bg-success-700"
          >
            Test Enhanced Content Generation
          </button>
        </div>

        {/* Next Steps */}
        <div className="border rounded-lg p-4 bg-primary/10">
          <h2 className="text-xl font-semibold mb-3">Next Steps</h2>
          <div className="space-y-2 text-sm">
            <p>1. Configure environment variables for API keys</p>
            <p>2. Set up Cloudflare Vectorize for vector storage</p>
            <p>3. Implement content indexing workflow</p>
            <p>4. Add vector search endpoints</p>
            <p>5. Integrate with learning content creation UI</p>
          </div>
        </div>
      </div>
    </div>
  );
}