import { type LoaderFunctionArgs } from "react-router";
import { db } from "~/db";
import { quizAttempt, learningProgress, learningContent } from "~/db/schema";
import { eq, sql, and } from "drizzle-orm";
import { requireAuthSession } from "~/lib/auth/supabase-server";

export interface QuizScoreBreakdown {
  scoreRanges: {
    "90+": { count: number; points: number; items: Array<{ title: string; score: number; points: number }> };
    "60-69": { count: number; points: number; items: Array<{ title: string; score: number; points: number }> };
    "<60": { count: number; points: number; items: Array<{ title: string; score: number; points: number }> };
  };
  total: {
    count: number;
    points: number;
    average: number;
  };
}

export interface LearningProgressBreakdown {
  durationBonus: {
    "15+": { count: number; points: number; items: Array<{ title: string; duration: number; level: string; basePoints: number; bonusPoints: number; totalPoints: number }> };
    "10-14": { count: number; points: number; items: Array<{ title: string; duration: number; level: string; basePoints: number; bonusPoints: number; totalPoints: number }> };
    "5-9": { count: number; points: number; items: Array<{ title: string; duration: number; level: string; basePoints: number; bonusPoints: number; totalPoints: number }> };
    "<5": { count: number; points: number; items: Array<{ title: string; duration: number; level: string; basePoints: number; bonusPoints: number; totalPoints: number }> };
  };
  levelBreakdown: {
    [level: string]: {
      count: number;
      points: number;
      basePoints: number;
      bonusPoints: number;
      average: number;
    };
  };
}

export interface QuizDataItem {
  quizTitle: string;
  score: any; // JSON object with percentage property
  points: number;
}

export interface LearningDataItem {
  contentId: string;
  title: string;
  learningLevel: string | null;
  estimatedReadingTime: number | null;
  basePoints: number;
  durationBonus: number;
  totalPoints: number;
}

export interface PointsBreakdownResponse {
  success: boolean;
  data: {
    quiz: QuizScoreBreakdown;
    learning: LearningProgressBreakdown;
  };
}

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const authSession = await requireAuthSession(request);
    const userId = authSession.user.id;

    // Get quiz score breakdown
    const quizData = await db
      .select({
        quizTitle: sql<string>`q.title`,
        score: quizAttempt.score,
        points: sql<number>`
          CASE 
            WHEN (${quizAttempt.score}->>'percentage')::numeric >= 90 THEN 100
            WHEN (${quizAttempt.score}->>'percentage')::numeric >= 80 THEN 80
            WHEN (${quizAttempt.score}->>'percentage')::numeric >= 70 THEN 60
            WHEN (${quizAttempt.score}->>'percentage')::numeric >= 60 THEN 40
            ELSE 20
          END
        `
      })
      .from(quizAttempt)
      .innerJoin(sql`quiz q`, sql`${quizAttempt.quizId} = q.id`)
      .where(
        and(
          eq(quizAttempt.userId, userId),
          eq(quizAttempt.isCompleted, true)
        )
      );

    // Get learning progress breakdown
    const learningData = await db
      .select({
        contentId: learningProgress.contentId,
        title: learningContent.title,
        learningLevel: learningContent.learningLevel,
        estimatedReadingTime: learningContent.estimatedReadingTime,
        basePoints: sql<number>`
          CASE 
            WHEN ${learningContent.learningLevel} = 'beginner' THEN 30
            WHEN ${learningContent.learningLevel} = 'intermediate' THEN 50
            WHEN ${learningContent.learningLevel} = 'advanced' THEN 70
            ELSE 30
          END
        `,
        durationBonus: sql<number>`
          CASE 
            WHEN ${learningContent.estimatedReadingTime} >= 15 THEN 15
            WHEN ${learningContent.estimatedReadingTime} >= 10 THEN 10
            WHEN ${learningContent.estimatedReadingTime} >= 5 THEN 5
            ELSE 0
          END
        `,
        totalPoints: sql<number>`
          CASE 
            WHEN ${learningContent.learningLevel} = 'beginner' THEN 30
            WHEN ${learningContent.learningLevel} = 'intermediate' THEN 50
            WHEN ${learningContent.learningLevel} = 'advanced' THEN 70
            ELSE 30
          END +
          CASE 
            WHEN ${learningContent.estimatedReadingTime} >= 15 THEN 15
            WHEN ${learningContent.estimatedReadingTime} >= 10 THEN 10
            WHEN ${learningContent.estimatedReadingTime} >= 5 THEN 5
            ELSE 0
          END
        `
      })
      .from(learningProgress)
      .innerJoin(learningContent, eq(learningProgress.contentId, learningContent.id))
      .where(
        and(
          eq(learningProgress.userId, userId),
          eq(learningProgress.isCompleted, true)
        )
      );

    // Process quiz score breakdown
    const quizBreakdown: QuizScoreBreakdown = {
      scoreRanges: {
        "90+": { count: 0, points: 0, items: [] },
        "60-69": { count: 0, points: 0, items: [] },
        "<60": { count: 0, points: 0, items: [] }
      },
      total: { count: 0, points: 0, average: 0 }
    };

    quizData.forEach((item: QuizDataItem) => {
      const percentage = (item.score as any)?.percentage || 0;
      const points = item.points;
      const quizItem = {
        title: item.quizTitle,
        score: percentage,
        points: points
      };

      if (percentage >= 90) {
        quizBreakdown.scoreRanges["90+"].count++;
        quizBreakdown.scoreRanges["90+"].points += points;
        quizBreakdown.scoreRanges["90+"].items.push(quizItem);
      } else if (percentage >= 60) {
        quizBreakdown.scoreRanges["60-69"].count++;
        quizBreakdown.scoreRanges["60-69"].points += points;
        quizBreakdown.scoreRanges["60-69"].items.push(quizItem);
      } else {
        quizBreakdown.scoreRanges["<60"].count++;
        quizBreakdown.scoreRanges["<60"].points += points;
        quizBreakdown.scoreRanges["<60"].items.push(quizItem);
      }

      quizBreakdown.total.count++;
      quizBreakdown.total.points += points;
    });

    quizBreakdown.total.average = quizBreakdown.total.count > 0 
      ? Math.round(quizBreakdown.total.points / quizBreakdown.total.count)
      : 0;

    // Process learning progress breakdown
    const learningBreakdown: LearningProgressBreakdown = {
      durationBonus: {
        "15+": { count: 0, points: 0, items: [] },
        "10-14": { count: 0, points: 0, items: [] },
        "5-9": { count: 0, points: 0, items: [] },
        "<5": { count: 0, points: 0, items: [] }
      },
      levelBreakdown: {}
    };

    learningData.forEach((item: LearningDataItem) => {
      const duration = item.estimatedReadingTime || 0;
      const level = item.learningLevel || 'beginner';
      const learningItem = {
        title: item.title,
        duration: duration,
        level: level,
        basePoints: item.basePoints,
        bonusPoints: item.durationBonus,
        totalPoints: item.totalPoints
      };

      // Duration bonus categorization
      if (duration >= 15) {
        learningBreakdown.durationBonus["15+"].count++;
        learningBreakdown.durationBonus["15+"].points += item.durationBonus;
        learningBreakdown.durationBonus["15+"].items.push(learningItem);
      } else if (duration >= 10) {
        learningBreakdown.durationBonus["10-14"].count++;
        learningBreakdown.durationBonus["10-14"].points += item.durationBonus;
        learningBreakdown.durationBonus["10-14"].items.push(learningItem);
      } else if (duration >= 5) {
        learningBreakdown.durationBonus["5-9"].count++;
        learningBreakdown.durationBonus["5-9"].points += item.durationBonus;
        learningBreakdown.durationBonus["5-9"].items.push(learningItem);
      } else {
        learningBreakdown.durationBonus["<5"].count++;
        learningBreakdown.durationBonus["<5"].points += item.durationBonus;
        learningBreakdown.durationBonus["<5"].items.push(learningItem);
      }

      // Level breakdown
      if (!learningBreakdown.levelBreakdown[level]) {
        learningBreakdown.levelBreakdown[level] = {
          count: 0,
          points: 0,
          basePoints: 0,
          bonusPoints: 0,
          average: 0
        };
      }

      learningBreakdown.levelBreakdown[level].count++;
      learningBreakdown.levelBreakdown[level].points += item.totalPoints;
      learningBreakdown.levelBreakdown[level].basePoints += item.basePoints;
      learningBreakdown.levelBreakdown[level].bonusPoints += item.durationBonus;
    });

    // Calculate averages for level breakdown
    Object.keys(learningBreakdown.levelBreakdown).forEach(level => {
      const data = learningBreakdown.levelBreakdown[level];
      data.average = data.count > 0 ? Math.round(data.points / data.count) : 0;
    });

    const response: PointsBreakdownResponse = {
      success: true,
      data: {
        quiz: quizBreakdown,
        learning: learningBreakdown
      }
    };

    return Response.json(response);

  } catch (error) {
    console.error("Error fetching points breakdown:", error);
    return Response.json(
      { success: false, error: "Failed to fetch points breakdown" },
      { status: 500 }
    );
  }
}