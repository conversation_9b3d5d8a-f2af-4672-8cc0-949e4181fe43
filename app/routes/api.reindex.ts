/**
 * Content Reindexing API Endpoint
 * 
 * This endpoint provides content reindexing functionality for the RAG system.
 * It allows reindexing specific content or all content in the system.
 */

import type { ActionFunctionArgs } from 'react-router';
import { createLearningContentIndexingService } from '~/lib/rag/services/learning-content-indexing-service';
import { db } from '~/db/connection';
import { learningContent } from '~/db/schema';
import { eq } from 'drizzle-orm';
import { requireAuthSession } from '~/lib/auth/supabase-server';
import { getAuthenticatedUser } from '~/lib/auth/middleware';
import { log } from '~/lib/logger';
import { z } from 'zod';

// Request validation schema
const ReindexRequestSchema = z.object({
  contentId: z.string().uuid('Invalid content ID').optional(),
  reindexAll: z.boolean().optional().default(false),
});

export async function action({ request, context }: ActionFunctionArgs) {
  const requestId = crypto.randomUUID().substring(0, 8);
  
  try {
    // Authenticate user
    let user;
    try {
      const authSession = await requireAuthSession(request);
      user = getAuthenticatedUser(authSession);
    } catch (authError) {
      log.error(`[${requestId}] ❌ Authentication failed`, {
        error: authError instanceof Error ? authError.message : String(authError),
      });
      return Response.json(
        { success: false, error: 'Authentication failed' },
        { status: 401 }
      );
    }

    if (!user) {
      log.error(`[${requestId}] ❌ No user found after authentication`);
      return Response.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const validation = ReindexRequestSchema.safeParse(body);

    if (!validation.success) {
      return Response.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const { contentId, reindexAll } = validation.data;

    log.info(`[${requestId}] 🔄 Reindex Request Started`, {
      userId: user.id,
      contentId,
      reindexAll,
      timestamp: new Date().toISOString(),
    });

    // Get environment variables from Cloudflare Workers context
    const env = (context as any)?.cloudflare?.env;
    const vectorizeIndex = env?.VECTORIZE_INDEX;

    if (!vectorizeIndex) {
      log.error(`[${requestId}] ❌ Vectorize index not available`);
      return Response.json(
        { success: false, error: 'Vectorize index not configured' },
        { status: 500 }
      );
    }

    if (!env?.VOYAGE_API_KEY) {
      log.error(`[${requestId}] ❌ Voyage API key not available`);
      return Response.json(
        { success: false, error: 'Voyage API key not configured' },
        { status: 500 }
      );
    }

    log.info(`[${requestId}] 🔧 Environment Check Passed`, {
      hasVectorizeIndex: !!vectorizeIndex,
      hasVoyageKey: !!env?.VOYAGE_API_KEY,
    });

    // Create indexing service
    const indexingService = createLearningContentIndexingService(vectorizeIndex, env);

    let contentToReindex;
    let reindexResults = [];

    if (reindexAll) {
      // Reindex all content
      log.info(`[${requestId}] 📊 Reindexing all content`);
      
      contentToReindex = await db.select().from(learningContent);
      
      log.info(`[${requestId}] 📋 Found ${contentToReindex.length} content items to reindex`);

      // Process in batches of 5 to avoid overwhelming the system
      const batchSize = 5;
      let successCount = 0;
      let failureCount = 0;

      for (let i = 0; i < contentToReindex.length; i += batchSize) {
        const batch = contentToReindex.slice(i, i + batchSize);
        
        log.info(`[${requestId}] 🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(contentToReindex.length / batchSize)}`);

        const batchPromises = batch.map(async (content: any) => {
          try {
            const result = await indexingService.updateContentIndex(content as any);
            
            if (result.success) {
              successCount++;
              log.success(`[${requestId}] ✅ Reindexed: ${content.title} (${result.chunksIndexed} chunks)`);
              return {
                contentId: content.id,
                title: content.title,
                success: true,
                chunksIndexed: result.chunksIndexed,
                processingTime: result.processingTimeMs
              };
            } else {
              failureCount++;
              log.error(`[${requestId}] ❌ Failed to reindex: ${content.title}`, result.error);
              return {
                contentId: content.id,
                title: content.title,
                success: false,
                error: result.error
              };
            }
          } catch (error) {
            failureCount++;
            log.error(`[${requestId}] ❌ Failed to reindex: ${content.title}`, error);
            return {
              contentId: content.id,
              title: content.title,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            };
          }
        });

        const batchResults = await Promise.all(batchPromises);
        reindexResults.push(...batchResults);

        // Add delay between batches
        if (i + batchSize < contentToReindex.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      log.success(`[${requestId}] 🎉 Batch reindexing completed`, {
        total: contentToReindex.length,
        successful: successCount,
        failed: failureCount
      });

      return Response.json({
        success: true,
        message: 'Batch reindexing completed',
        results: {
          total: contentToReindex.length,
          successful: successCount,
          failed: failureCount,
          details: reindexResults
        }
      });

    } else if (contentId) {
      // Reindex specific content
      log.info(`[${requestId}] 🎯 Reindexing specific content: ${contentId}`);

      const [content] = await db.select().from(learningContent).where(eq(learningContent.id, contentId));

      if (!content) {
        return Response.json(
          { success: false, error: 'Content not found' },
          { status: 404 }
        );
      }

      const result = await indexingService.updateContentIndex(content as any);

      if (result.success) {
        log.success(`[${requestId}] ✅ Content reindexed successfully`, {
          contentId,
          title: content.title,
          chunksIndexed: result.chunksIndexed,
          processingTime: result.processingTimeMs
        });

        return Response.json({
          success: true,
          message: 'Content reindexed successfully',
          result: {
            contentId,
            title: content.title,
            chunksIndexed: result.chunksIndexed,
            processingTime: result.processingTimeMs
          }
        });
      } else {
        log.error(`[${requestId}] ❌ Content reindexing failed`, {
          contentId,
          error: result.error
        });

        return Response.json(
          { success: false, error: result.error },
          { status: 500 }
        );
      }

    } else {
      return Response.json(
        { success: false, error: 'Either contentId or reindexAll must be specified' },
        { status: 400 }
      );
    }

  } catch (error) {
    log.error(`[${requestId}] ❌ Reindex API error`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    return Response.json(
      { 
        success: false, 
        error: 'Internal server error',
      },
      { status: 500 }
    );
  }
}
