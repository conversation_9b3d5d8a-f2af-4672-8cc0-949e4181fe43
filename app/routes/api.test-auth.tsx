import type { LoaderFunctionArgs, ActionFunctionArgs } from "react-router";
import { requireAuthSession } from "~/lib/auth/supabase-server";

/**
 * Test endpoint to verify authentication and session handling
 */
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const authSession = await requireAuthSession(request);
    
    return Response.json({
      success: true,
      message: "Authentication successful",
      user: {
        id: authSession.user.id,
        email: authSession.user.email,
        emailVerified: authSession.user.email_confirmed_at != null,
      },
      session: {
        expiresAt: (authSession as any).expires_at ? new Date((authSession as any).expires_at * 1000).toISOString() : null,
        expires_at_iso: (authSession as any).expires_at ? new Date((authSession as any).expires_at * 1000).toISOString() : null,
        accessToken: (authSession as any).access_token ? '[REDACTED]' : null,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    // This should be handled by the enhanced error handling in requireAuthSession
    throw error;
  }
}

/**
 * Test action to verify POST authentication
 */
export async function action({ request }: ActionFunctionArgs) {
  try {
    const authSession = await requireAuthSession(request);
    
    return Response.json({
      success: true,
      message: "POST authentication successful",
      method: request.method,
      user: {
        id: authSession.user.id,
        email: authSession.user.email,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    throw error;
  }
}