import type { LoaderFunctionArgs, MetaFunction } from "react-router";
import {
  useLoaderData,
  useNavigate,
  usePara<PERSON>,
  useSearchParams,
  Link,
} from "react-router";

import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowLeft, Brain } from "lucide-react";
import { useEffect, useMemo, useState, useCallback } from "react";
import { QuizContainer } from "~/components/quiz/QuizContainer";
import { QuizResults } from "~/components/quiz/QuizResults";
import { QuizReview } from "~/components/quiz/QuizReview";
import {
  useGetQuizWithAttempts,
  useGetCurrentAttempt,
  useGetAttemptHistory,
  useStartAttempt,
  useSubmitAnswer,
  useSaveProgress,
  useCompleteAttempt,
} from "~/lib/hooks/use-quiz-api";
import { useQueryClient } from "@tanstack/react-query";
import { useBreadcrumbTitle } from "~/lib/hooks/use-breadcrumbs";
import { useDecryptedQuizData } from "~/hooks/use-quiz-decryption";

export const meta: MetaFunction = () => [{ title: "Quiz - Kwaci Learning" }];

export async function loader({ request }: LoaderFunctionArgs) {
  // Server-side authentication validation
  const { requireAuth } = await import('~/lib/auth/supabase-server');
  const authContext = await requireAuth(request);
  
  return {
    user: {
      id: authContext.user!.id,
      email: authContext.user!.email,
    },
  };
}

export default function QuizTakingPage() {
  const params = useParams();
  const navigate = useNavigate();
  const [search, setSearch] = useSearchParams();
  const queryClient = useQueryClient();
  const quizId = params.quizId as string;

  const isReviewMode =
    search.get("review") === "true" || search.get("mode") === "review";
  const isRetakeMode = search.get("retake") === "true";
  const reviewView = search.get("view") || "results"; // 'results' or 'questions'
  const attemptIdParam = search.get("attemptId") || undefined;

  const [currentAttempt, setCurrentAttempt] = useState<any>(null);
  const [isQuizStarted, setIsQuizStarted] = useState(false);
  const [isRetaking, setIsRetaking] = useState(isRetakeMode);
  const [isCompletingQuiz, setIsCompletingQuiz] = useState(false);
  const [isStartingQuiz, setIsStartingQuiz] = useState(false);
  const [hasProcessedRetake, setHasProcessedRetake] = useState(false);

  // Update isRetaking state when URL parameter changes
  useEffect(() => {
    setIsRetaking(isRetakeMode);

    // Reset quiz state when entering retake mode
    if (isRetakeMode && !hasProcessedRetake) {
      setCurrentAttempt(null);
      setIsQuizStarted(false);
      setHasProcessedRetake(true);
    }
  }, [isRetakeMode, isReviewMode, hasProcessedRetake]);

  const quizQuery = useGetQuizWithAttempts(quizId);
  const currentAttemptQuery = useGetCurrentAttempt(quizId);
  const historyQuery = useGetAttemptHistory(quizId, { limit: 25 });

  // Set dynamic breadcrumb title
  useBreadcrumbTitle(quizQuery.data?.quiz?.title || "Quiz");

  const startAttempt = useStartAttempt();
  const submitAnswer = useSubmitAnswer();
  const saveProgress = useSaveProgress();
  const completeAttempt = useCompleteAttempt();

  const rawQuiz = quizQuery.data?.quiz as any;
  const { quiz: decryptedQuiz, isDecrypting, decryptionError, decryptAnswers } = useDecryptedQuizData(rawQuiz);
  const quiz = decryptedQuiz;
  const attempts = historyQuery.data?.attempts || [];
  const existingAttempt = currentAttemptQuery.data?.attempt as any;
  const existingProgress = currentAttemptQuery.data?.progress as any;
  
  const foundAttempt = attemptIdParam
    ? attempts.find((attempt: any) => attempt.id === attemptIdParam)
    : attempts.find((attempt: any) => attempt.isCompleted) || attempts[0];
  
  // If there's a newer attempt (either current or in history) that supersedes the found attempt,
  // or if there's no current attempt when reviewing a specific attemptId (user has retaken),
  // treat the old attempt as invalid
  let reviewAttempt = foundAttempt;
  
  // If we're looking for a specific attemptId and it matches the current attempt,
  // use the current attempt (this handles the case where quiz was just completed
  // but history hasn't been refetched yet)
  if (attemptIdParam && currentAttempt && currentAttempt.id === attemptIdParam) {
    reviewAttempt = currentAttempt;
  }
  if (foundAttempt && attemptIdParam) {
    // If there's no current attempt, it means the user has likely retaken the quiz
    // and the old attempt is no longer valid
    if (!existingAttempt) {
      reviewAttempt = null; // Show "No Quiz Attempts Found" for superseded attempts
    } else {
      // Check if there's a newer attempt in history
      const newerAttempt = attempts.find((attempt: any) => 
        attempt.id !== foundAttempt.id && 
        new Date(attempt.createdAt) > new Date(foundAttempt.createdAt)
      );
      
      // Check if there's a newer current attempt
      const newerCurrentAttempt = existingAttempt && 
        new Date(existingAttempt.createdAt) > new Date(foundAttempt.createdAt);
      
      // If there's any newer attempt, invalidate the old one
      if (newerAttempt || newerCurrentAttempt) {
        reviewAttempt = null; // Show "No Quiz Attempts Found" for superseded attempts
      }
    }
  }

  // Update currentAttempt when getCurrentAttempt data changes (for time sync)
  useEffect(() => {
    if (
      existingAttempt &&
      currentAttempt &&
      existingAttempt.id === currentAttempt.id
    ) {
      // Only sync if the existing attempt is more recent or has completion data
      // This prevents overwriting completion state with stale data
      const shouldSync =
        !currentAttempt.isCompleted || // Always sync if current is not completed
        (existingAttempt.isCompleted && existingAttempt.score && 
         (!currentAttempt.isCompleted || !currentAttempt.score)); // Or if existing has completion data but current doesn't

      if (shouldSync) {
        setCurrentAttempt(existingAttempt);
      }
    }
  }, [existingAttempt, currentAttempt]);

  // Auto-start review mode if we have a completed attempt
  useEffect(() => {
    if (isReviewMode && reviewAttempt && !isQuizStarted) {
      setCurrentAttempt(reviewAttempt);
      // Only start quiz interface if view=questions, otherwise show results
      if (reviewView === "questions") {
        setIsQuizStarted(true);
      }
    }
  }, [isReviewMode, reviewAttempt, isQuizStarted, reviewView]);

  // Clear retake URL parameter only after a new attempt is started
  useEffect(() => {
    if (isRetakeMode && currentAttempt && isQuizStarted) {
      // Clear the retake parameter once we've successfully started a new attempt
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete("retake");
      window.history.replaceState({}, "", newUrl.toString());
      // Reset the processed retake flag
      setHasProcessedRetake(false);
    }
  }, [isRetakeMode, currentAttempt, isQuizStarted]);

  // Auto-resume existing incomplete attempt
  useEffect(() => {
    // Auto-resume incomplete attempts, but NOT during retakes
    if (
      !isReviewMode &&
      !isRetaking &&
      !isRetakeMode && // Also check the URL parameter directly
      !hasProcessedRetake && // Don't auto-resume if we're processing a retake
      existingAttempt &&
      !existingAttempt.isCompleted &&
      !isQuizStarted &&
      !currentAttempt
    ) {
      setCurrentAttempt(existingAttempt);
      setIsQuizStarted(true);
    }
  }, [
    isReviewMode,
    isRetaking,
    isRetakeMode,
    hasProcessedRetake,
    existingAttempt,
    isQuizStarted,
    currentAttempt,
  ]);

  // Actions
  const handleStartQuiz = () => {
    if (!quiz) return;

    setIsStartingQuiz(true);

    // If we're in retake mode, always create a new attempt
    if (isRetaking) {
      startAttempt.mutate(
        { quizId: quiz.id },
        {
          onSuccess: (attemptData: any) => {
            console.log('Start attempt success:', attemptData);
            
            // Clear retaking flag when new attempt is successfully started
            setIsRetaking(false);
            setIsStartingQuiz(false);

            // The API returns the attempt object directly after ApiClient processing
            if (attemptData && attemptData.id) {
              setCurrentAttempt(attemptData);
              setIsQuizStarted(true);
              console.log('Quiz started with attemptId:', attemptData.id);
            } else {
              console.error('No valid attempt data found in response:', attemptData);
            }
          },
          onError: (error: any) => {
            console.error(`Failed to start quiz: ${error.message}`);
            // Clear retaking flag on error too
            setIsRetaking(false);
            setIsStartingQuiz(false);
          },
        }
      );
      return;
    }

    // If there's an existing completed attempt, treat this as a retake
    if (existingAttempt && existingAttempt.isCompleted) {
      // Set retaking flag and create new attempt
      setIsRetaking(true);
      startAttempt.mutate(
        { quizId: quiz.id },
        {
          onSuccess: (attemptData: any) => {
            console.log('Start attempt success (retake):', attemptData);
            
            // Clear retaking flag when new attempt is successfully started
            setIsRetaking(false);
            setIsStartingQuiz(false);

            // The API returns the attempt object directly after ApiClient processing
            if (attemptData && attemptData.id) {
              setCurrentAttempt(attemptData);
              setIsQuizStarted(true);
              console.log('Quiz started with attemptId:', attemptData.id);
            } else {
              console.error('No valid attempt data found in response:', attemptData);
            }
          },
          onError: (error: any) => {
            console.error(`Failed to start quiz: ${error.message}`);
            // Clear retaking flag on error too
            setIsRetaking(false);
            setIsStartingQuiz(false);
          },
        }
      );
      return;
    }

    // If there's an existing incomplete attempt, resume it instead of creating new one
    if (existingAttempt && !existingAttempt.isCompleted) {
      setCurrentAttempt(existingAttempt);
      setIsQuizStarted(true);
      setIsStartingQuiz(false);
    } else {
      startAttempt.mutate(
        { quizId },
        {
          onSuccess: (attemptData: any) => {
            // The API returns the attempt object directly after ApiClient processing
            if (attemptData && attemptData.id) {
              setCurrentAttempt(attemptData);
              setIsQuizStarted(true);
              setIsStartingQuiz(false);
            } else {
              console.error('No valid attempt data found in response:', attemptData);
              setIsStartingQuiz(false);
            }
          },
          onError: (error: any) => {
            console.error(`Failed to start quiz: ${error.message}`);
            setIsStartingQuiz(false);
          },
        }
      );
    }
  };

  const handleAnswerSubmit = (
    questionId: string,
    answer: any,
    timeSpent: number
  ) => {
    if (!currentAttempt || !currentAttempt.id) {
      console.warn('Cannot submit answer: no attempt available');
      return;
    }

    submitAnswer.mutate({
      attemptId: currentAttempt.id,
      questionId,
      answer,
      timeSpent,
      quizId,
      isTemporary: false,
    }, {
      onSuccess: (data) => {
        // Update quiz questions with answer data from response
        if (data?.questionAnswerData && quiz) {
          const answerData = data.questionAnswerData;
          const questionIndex = quiz.questions.findIndex((q: any) => q.id === answerData.questionId);
          
          if (questionIndex !== -1) {
            // Decrypt answer fields before merging
            decryptAnswers(answerData.answerFields, quiz?.id || answerData.quizId || '').then((decryptedAnswerFields) => {
              // Merge answer fields into the question object
              quiz.questions[questionIndex] = {
                ...quiz.questions[questionIndex],
                ...decryptedAnswerFields,
                isCorrect: answerData.isCorrect
              } as any;
            });
          }
        }

        // Save progress after successful answer submission
        const currentQuestionIndex = quiz?.questions.findIndex((q: any) => q.id === questionId) ?? -1;
        if (currentQuestionIndex !== -1) {
          handleSaveProgress({
            attemptId: currentAttempt.id,
            currentQuestionIndex,
            timeSpentOnCurrentQuestion: timeSpent,
            totalSessionTime: (currentAttempt.totalTimeSpent || 0) + timeSpent,
          }).catch(error => {
            console.warn('Failed to save progress after answer submission:', error);
          });
        }
      },
      onError: (error) => {
        console.error('Failed to submit answer:', error);
      }
    });
  };

  const handleSaveProgress = async (data: {
    attemptId: string;
    currentQuestionIndex: number;
    timeSpentOnCurrentQuestion: number;
    totalSessionTime: number;
    bookmarkedQuestions?: string[];
  }) => {
    // Don't save progress if no attemptId (quiz not started yet)
    if (!data.attemptId || !currentAttempt?.id) {
      console.warn('Cannot save progress: no attempt ID available');
      return Promise.resolve();
    }

    return new Promise<void>((resolve, reject) => {
      saveProgress.mutate(
        { ...data, quizId },
        {
          onSuccess: () => resolve(),
          onError: (error) => reject(error),
        }
      );
    });
  };

  const handleQuizComplete = useCallback(() => {
    // Don't complete quiz if no attempt exists
    if (!currentAttempt || !currentAttempt.id) {
      console.warn('Cannot complete quiz: no attempt available');
      return;
    }

    setIsCompletingQuiz(true);

    completeAttempt.mutate(
      { attemptId: currentAttempt.id, quizId },
      {
        onSuccess: (data: any) => {
          // Update attempt with completion data
          const updatedAttempt = {
            ...currentAttempt,
            isCompleted: true,
            completedAt: new Date().toISOString(),
            score: data.score,
            questionResults: data.questionResults,
          };

          setCurrentAttempt(updatedAttempt);
          setIsRetaking(false);
          setIsCompletingQuiz(false);
          
          // Navigate to review mode to show results
          navigate(`/dashboard/quiz/${quizId}?mode=review&view=results&attemptId=${updatedAttempt.id}`);
        },
        onError: (error: any) => {
          console.error(`Failed to complete quiz: ${error.message}`);
          setIsCompletingQuiz(false);
        },
      }
    );
  }, [currentAttempt?.id, quizId, completeAttempt.mutate, navigate]);

  const handleQuizExit = () => {
    // Force invalidate quiz list cache before navigating to ensure fresh data
    queryClient.invalidateQueries({ queryKey: ['quizzes.getAll'] });
    queryClient.invalidateQueries({ queryKey: ['quizzes.getAllInfinite'] });
    navigate("/dashboard/quizzes");
  };

  const handleRetake = () => {
    if (!quiz) return;

    // Navigate to the quiz preview screen with retake parameter
    // This ensures the quiz page knows it's a retake and shows the preview screen
    navigate(`/dashboard/quiz/${quiz.id}?retake=true`);
  };

  const handleReviewAnswers = () => {
    if (!quiz) return;
    // If already in review mode, switch to questions view
    if (isReviewMode) {
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set("view", "questions");
      navigate(newUrl.pathname + newUrl.search);
    } else {
      // Navigate to review mode with questions view
      navigate(`/dashboard/quiz/${quiz.id}?mode=review&view=questions`);
    }
  };

  // 7. Loading state
  if (
    quizQuery.isLoading ||
    (isReviewMode && historyQuery.isLoading) ||
    (!isReviewMode && currentAttemptQuery.isLoading) ||
    isDecrypting
  ) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">
            {isDecrypting ? 'Decrypting quiz data...' : 'Loading quiz...'}
          </p>
        </div>
      </DashboardLayout>
    );
  }

  // 6. Error state
  if (quizQuery.isError) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Quiz Not Found
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            {quizQuery.error?.message?.includes("NOT_FOUND")
              ? "The quiz you're looking for doesn't exist or has been removed."
              : quizQuery.error?.message?.includes("FORBIDDEN")
                ? "You don't have permission to access this quiz."
                : `Error loading quiz: ${quizQuery.error?.message}`}
          </p>
        </div>
      </DashboardLayout>
    );
  }

  // 6.1. Decryption error state
  if (decryptionError) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Decryption Error
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Failed to decrypt quiz data. Please try refreshing the page.
          </p>
          <Button onClick={() => window.location.reload()}>
            Refresh Page
          </Button>
        </div>
      </DashboardLayout>
    );
  }

  // 5. Quiz not found
  if (!quiz) {
    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Quiz Not Available
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            This quiz is not available at the moment.
          </p>
        </div>
      </DashboardLayout>
    );
  }

  // 4. Review mode but no completed attempts
  if (isReviewMode && !historyQuery.isLoading && !reviewAttempt) {
    // Show loading if quiz is being completed
    if (isCompletingQuiz) {
      return (
        <DashboardLayout>
          <div className="max-w-4xl mx-auto space-y-6">
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-8 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Processing Quiz Results
              </h2>
              <p className="text-gray-600 dark:text-gray-300">
                Please wait while we prepare your quiz results...
              </p>
            </div>
          </div>
        </DashboardLayout>
      );
    }

    return (
      <DashboardLayout>
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            No Quiz Attempts Found
          </h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            You haven&apos;t completed this quiz yet. Take the quiz first to
            review your answers.
          </p>
          <div className="space-x-4">
            <Link to={`/dashboard/quiz/${quizId}`}>
              <Button>
                <Brain className="h-4 w-4 mr-2" />
                Take Quiz
              </Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // 3. Review mode with results view (default)
  if (isReviewMode && reviewAttempt && reviewView === "results") {
    // Show loading state if quiz is being completed
    if (isCompletingQuiz) {
      return (
        <DashboardLayout>
          <div className="max-w-4xl mx-auto space-y-6">
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-8 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Processing Quiz Results
              </h2>
              <p className="text-gray-600 dark:text-gray-300">
                Please wait while we prepare your quiz results...
              </p>
            </div>
          </div>
        </DashboardLayout>
      );
    }

    // Check if the attempt has a valid score before showing results
    if (!reviewAttempt.score || typeof reviewAttempt.score !== 'object' || reviewAttempt.score.totalQuestions === undefined) {
      // If the attempt is completed but score is not available yet, show loading instead of error
      if (reviewAttempt.isCompleted) {
        return (
          <DashboardLayout>
            <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600 dark:text-gray-400">
                  Loading Quiz Results...
                </p>
              </div>
            </div>
          </DashboardLayout>
        );
      }
       
      // If attempt is not completed and no score, show error
      return (
        <DashboardLayout>
          <div className="max-w-4xl mx-auto space-y-6">
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-8 text-center">
              <AlertCircle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Results Not Available
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                The quiz results are not available for this attempt. The quiz may not have been completed properly.
              </p>
              <div className="space-x-4">
                <Button onClick={handleReviewAnswers}>
                  Review Answers
                </Button>
                <Button variant="outline" onClick={handleQuizExit}>
                  Back to Quiz
                </Button>
              </div>
            </div>
          </div>
        </DashboardLayout>
      );
    }

    return (
      <DashboardLayout>
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Quiz Results */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-8">
            <QuizResults
              quiz={quiz}
              attempt={reviewAttempt}
              score={reviewAttempt.score}
              questionResults={reviewAttempt.questionResults || []}
              onRetake={quiz.allowRetakes ? handleRetake : undefined}
              onReview={handleReviewAnswers}
              onExit={handleQuizExit}
              isRetaking={isRetaking}
            />
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // 2. Review mode with questions view
  if (isReviewMode && reviewAttempt && reviewView === "questions") {
    return (
      <DashboardLayout>
        <QuizReview
          quiz={quiz}
          attempt={reviewAttempt}
          questionResults={reviewAttempt.questionResults || []}
          onBackToResults={() => {
            const newUrl = new URL(window.location.href);
            newUrl.searchParams.set("view", "results");
            navigate(newUrl.pathname + newUrl.search);
          }}
          onExit={handleQuizExit}
        />
      </DashboardLayout>
    );
  }

  // 1. Quiz taking interface (including review mode with questions view)
  // Show quiz interface if quiz is started and we have a valid attempt
  // Don't show if we're still processing a retake (hasProcessedRetake but not yet started)
  // But allow showing if we're actively starting a quiz (isStartingQuiz)
  if (isQuizStarted && currentAttempt && currentAttempt.id && (!(hasProcessedRetake && !isQuizStarted) || isStartingQuiz)) {
    // Don't render QuizContainer until quiz is decrypted and available
    if (!quiz || isDecrypting) {
      return (
        <DashboardLayout>
          <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-400">
                {isDecrypting ? "Decrypting quiz data..." : "Loading quiz..."}
              </p>
            </div>
          </div>
        </DashboardLayout>
      );
    }

    return (
      <DashboardLayout>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <QuizContainer
            key={currentAttempt?.id || "no-attempt"}
            quiz={quiz}
            attempt={currentAttempt}
            progress={existingProgress || undefined}
            onAnswerSubmit={isReviewMode ? () => undefined : handleAnswerSubmit}
            onQuizComplete={isReviewMode ? () => undefined : handleQuizComplete}
            onQuizExit={handleQuizExit}
            onSaveProgress={isReviewMode ? undefined : handleSaveProgress}
            onRetake={handleRetake}
            onReviewAnswers={handleReviewAnswers}
            isSubmittingAnswer={submitAnswer.isPending}
            isCompletingQuiz={completeAttempt.isPending}
            isRetaking={isRetaking}
          />
        </div>
      </DashboardLayout>
    );
  }

  // 8. Show loading state when starting quiz
  if (isStartingQuiz) {
    return (
      <DashboardLayout>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">
              Starting Quiz...
            </p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // 9. Quiz preview/start screen (show if not in review mode OR if in retake mode but quiz not started yet)
  // But don't show if we're actively starting a quiz
  if ((!isReviewMode || (isRetakeMode && !isQuizStarted) || (hasProcessedRetake && !isQuizStarted)) && !isStartingQuiz) {
    return (
      <DashboardLayout>
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Quiz Preview */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-8">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full mb-4">
                <Brain className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {quiz.title}
              </h1>
              {quiz.description && (
                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                  {quiz.description}
                </p>
              )}
            </div>

            {/* Quiz Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                  {Array.isArray(quiz.questions) ? quiz.questions.length : 0}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  Questions
                </div>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
                  {quiz.estimatedDuration}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  Minutes
                </div>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">
                  {quiz.totalPoints}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">
                  Points
                </div>
              </div>
            </div>

            {/* Quiz Info */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-8">
              <h3 className="font-semibold text-blue-900 dark:text-blue-200 mb-2">
                Before you start:
              </h3>
              <ul className="text-blue-800 dark:text-blue-300 text-sm space-y-1">
                <li>• Make sure you have a stable internet connection</li>
                <li>• You can navigate between questions freely</li>
                {quiz.timeLimit && (
                  <li>• Time limit: {quiz.timeLimit} minutes</li>
                )}
                {quiz.allowRetakes && (
                  <li>• You can retake this quiz if needed</li>
                )}
                <li>• Your progress will be saved automatically</li>
              </ul>
            </div>

            {/* Start Button */}
            <div className="text-center">
              <Button
                size="lg"
                onClick={handleStartQuiz}
                disabled={startAttempt.isPending || isStartingQuiz}
                className="px-8 py-3"
              >
                {(startAttempt.isPending || isStartingQuiz) ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Starting Quiz...
                  </>
                ) : (
                  <>
                    <Brain className="h-5 w-5 mr-2" />
                    Start Quiz
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // 10. Fallback for review mode (shouldn't reach here normally)
  return (
    <DashboardLayout>
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-300">Loading review...</p>
      </div>
    </DashboardLayout>
  );
}
