/**
 * Chat API route for RAG-powered conversations
 * 
 * This route handles chat requests with retrieval-augmented generation,
 * providing contextual responses based on learning content.
 */

import type { ActionFunctionArgs } from 'react-router';
import { createRagChatService } from '~/lib/rag/services/rag-chat-service';
import { db } from '~/db/connection';
import { requireAuthSession } from '~/lib/auth/supabase-server';
import { getAuthenticatedUser } from '~/lib/auth/middleware';
import { log } from '~/lib/logger';
import { z } from 'zod';

// Request validation schema
const ChatRequestSchema = z.object({
  message: z.string().min(1, 'Message cannot be empty').max(2000, 'Message too long'),
  conversationId: z.string().optional(),
  learningContentId: z.string().uuid('Invalid learning content ID'),
});

/**
 * Handle chat POST requests
 */
export async function action({ request, context }: ActionFunctionArgs) {
  const requestId = `chat-${Date.now()}-${Math.random().toString(36).substring(7)}`;

  // CRITICAL DEBUG: Add logging at the very beginning of the action
  console.log(`[${requestId}] CRITICAL: Action function started`);
  console.log(`[${requestId}] CRITICAL: Request method:`, request.method);
  console.log(`[${requestId}] CRITICAL: Request URL:`, request.url);
  console.log(`[${requestId}] CRITICAL: Context exists:`, !!context);
  console.log(`[${requestId}] CRITICAL: Context value:`, context);

  try {
    // Authentication check
    const authSession = await requireAuthSession(request);
    const user = getAuthenticatedUser(authSession);

    // Validate request method
    if (request.method !== 'POST') {
      return Response.json(
        { success: false, error: 'Method not allowed' },
        { status: 405 }
      );
    }

    // Parse and validate request body
    let requestData;
    try {
      requestData = await request.json();
    } catch (error) {
      return Response.json(
        { success: false, error: 'Invalid JSON request body' },
        { status: 400 }
      );
    }

    const validation = ChatRequestSchema.safeParse(requestData);
    if (!validation.success) {
      return Response.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: validation.error.issues.map(issue => issue.message)
        },
        { status: 400 }
      );
    }

    const { message, conversationId, learningContentId } = validation.data;

    log.info(`[${requestId}] 🚀 RAG Chat Pipeline Started`, {
      userId: user.id,
      learningContentId,
      conversationId,
      messageLength: message.length,
      message: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
      timestamp: new Date().toISOString(),
    });

    // Initialize services

    // Get environment variables from Cloudflare Workers context
    // In Cloudflare Workers with Remix, the environment should be available through context.cloudflare.env
    const env = (context as any)?.cloudflare?.env;
    const vectorizeIndex = env?.VECTORIZE_INDEX;

    log.info(`[${requestId}] 🔧 Environment Check`, {
      hasVectorizeIndex: !!vectorizeIndex,
      hasOpenRouterKey: !!env?.OPENROUTER_API_KEY,
      hasVoyageKey: !!env?.VOYAGE_API_KEY,
      environmentKeys: Object.keys(env || {}).filter(key => !key.includes('SECRET')),
    });

    // CRITICAL DEBUG: Force logging to appear
    console.log(`[${requestId}] CRITICAL DEBUG START - Chat API reached`);
    console.log(`[${requestId}] Context type:`, typeof context);
    console.log(`[${requestId}] Context keys:`, context ? Object.keys(context) : 'null');
    console.log(`[${requestId}] Cloudflare context:`, (context as any)?.cloudflare ? 'exists' : 'missing');
    console.log(`[${requestId}] Environment object:`, env ? 'exists' : 'missing');
    console.log(`[${requestId}] Environment keys:`, env ? Object.keys(env) : 'none');
    console.log(`[${requestId}] OpenRouter key exists:`, !!env?.['OPENROUTER_API_KEY']);
    console.log(`[${requestId}] OpenRouter key length:`, env?.['OPENROUTER_API_KEY'] ? env['OPENROUTER_API_KEY'].length : 0);
    console.log(`[${requestId}] CRITICAL DEBUG END`);

    // Also try log.info to see if it appears
    log.info(`[${requestId}] Chat API route reached - Environment debug`, {
      hasContext: !!context,
      hasCloudflare: !!(context as any)?.cloudflare,
      hasEnv: !!env,
      envKeys: env ? Object.keys(env) : [],
      hasOpenRouterKey: !!env?.['OPENROUTER_API_KEY'],
      hasVectorizeIndex: !!vectorizeIndex,
      openRouterKeyLength: env?.['OPENROUTER_API_KEY'] ? env['OPENROUTER_API_KEY'].length : 0,
    });

    // Environment is available, create chat service
    console.log(`[${requestId}] Environment available, has OPENROUTER_API_KEY:`, !!env?.OPENROUTER_API_KEY);

    console.log(`[${requestId}] SUCCESS: Environment and API key available, creating chat service`);
    const chatService = createRagChatService(db, vectorizeIndex, env);

    // Process chat request
    const chatResponse = await chatService.chat({
      message,
      conversationId,
      learningContentId,
      userId: user.id,
    });

    if (chatResponse.success) {
      log.success(`[${requestId}] 🎉 RAG Chat Pipeline Completed Successfully`, {
        conversationId: chatResponse.conversationId,
        responseLength: chatResponse.message?.content.length || 0,
        sourcesCount: chatResponse.message?.sources?.length || 0,
        processingTime: chatResponse.processingTime,
        messagePreview: chatResponse.message?.content.substring(0, 150) + '...',
        sources: chatResponse.message?.sources?.map(source => ({
          stepTitle: source.stepTitle,
          score: source.score,
          contentId: source.contentId,
          stepId: source.stepId
        })),
        timestamp: new Date().toISOString(),
      });
    } else {
      log.error(`[${requestId}] ❌ RAG Chat Pipeline Failed`, {
        error: chatResponse.error,
        userId: user.id,
        learningContentId,
        timestamp: new Date().toISOString(),
      });
    }

    return Response.json(chatResponse);

  } catch (error) {
    log.error(`[${requestId}] Chat API error`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    return Response.json(
      { 
        success: false, 
        error: 'Internal server error',
        conversationId: '',
      },
      { status: 500 }
    );
  }
}

/**
 * Handle GET requests for conversation retrieval
 */
export async function loader({ request, context }: ActionFunctionArgs) {
  const requestId = `chat-get-${Date.now()}-${Math.random().toString(36).substring(7)}`;
  
  try {
    // Authentication check
    const authSession = await requireAuthSession(request);
    const user = getAuthenticatedUser(authSession);

    const url = new URL(request.url);
    const conversationId = url.searchParams.get('conversationId');
    const learningContentId = url.searchParams.get('learningContentId');

    // If conversationId is provided, get specific conversation
    if (conversationId) {
      log.info(`[${requestId}] Getting conversation`, {
        conversationId,
        userId: user.id,
      });


      const vectorizeIndex = (context as any)?.cloudflare?.env?.VECTORIZE_INDEX;
      const env = (context as any)?.cloudflare?.env;
      const chatService = createRagChatService(db, vectorizeIndex, env);

      const conversation = await chatService.getConversation(conversationId, user.id);

      if (!conversation) {
        return Response.json(
          { success: false, error: 'Conversation not found' },
          { status: 404 }
        );
      }

      return Response.json({
        success: true,
        conversation,
      });
    }

    // If learningContentId is provided, get conversations for that content
    if (learningContentId) {
      log.info(`[${requestId}] Getting conversations for content`, {
        learningContentId,
        userId: user.id,
      });


      const vectorizeIndex = (context as any)?.cloudflare?.env?.VECTORIZE_INDEX;
      const env = (context as any)?.cloudflare?.env;
      const chatService = createRagChatService(db, vectorizeIndex, env);

      const conversations = await chatService.getUserConversations(user.id, learningContentId);

      return Response.json({
        success: true,
        conversations,
      });
    }

    return Response.json(
      { success: false, error: 'Either conversationId or learningContentId is required' },
      { status: 400 }
    );

  } catch (error) {
    log.error(`[${requestId}] Chat GET API error`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    return Response.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}