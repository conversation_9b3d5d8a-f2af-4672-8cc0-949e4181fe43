import type { LoaderFunctionArgs } from "react-router";
import { checkRagHealth, getRagServices } from "~/lib/startup/rag-setup";
import { SERVICES_CONFIG } from "~/lib/rag/config/services-config";

/**
 * RAG System Health Check API
 * GET /api/health/rag
 */
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Perform comprehensive health check
    const healthStatus = await checkRagHealth();
    const ragServices = getRagServices();

    // Additional service availability check
    const serviceStatus = {
      initialized: ragServices !== null,
      embeddingService: ragServices?.embeddingService ? 'available' : 'unavailable',
      vectorIndexingService: ragServices?.vectorIndexingService ? 'available' : 'unavailable',
      ragSearchService: ragServices?.ragSearchService ? 'available' : 'unavailable',
    };

    // Configuration summary (without sensitive data)
    const configSummary = {
      embeddingModel: SERVICES_CONFIG.defaultEmbedding.model,
      embeddingDimensions: SERVICES_CONFIG.defaultEmbedding.dimensions,
      embeddingProvider: SERVICES_CONFIG.defaultEmbedding.provider,
      vectorIndexName: SERVICES_CONFIG.vectorizeIndexName,
      chunkingStrategy: SERVICES_CONFIG.chunking.defaultStrategy,
      maxChunkSize: SERVICES_CONFIG.chunking.maxChunkSize,
    };

    const response = {
      timestamp: new Date().toISOString(),
      status: healthStatus.isHealthy ? 'healthy' : 'unhealthy',
      services: {
        ...healthStatus.services,
        details: serviceStatus,
      },
      configuration: {
        ...healthStatus.configuration,
        summary: configSummary,
      },
      errors: healthStatus.errors,
      version: '1.0.0',
    };

    return Response.json(response, {
      status: healthStatus.isHealthy ? 200 : 503,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  } catch (error) {
    console.error('RAG health check failed:', error);
    
    return Response.json(
      {
        timestamp: new Date().toISOString(),
        status: 'error',
        error: 'Health check failed',
        message: error instanceof Error ? error.message : 'Unknown error',
        version: '1.0.0',
      },
      { 
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );
  }
}