import type { ActionFunctionArgs, LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { db } from "~/db";
import { quiz } from "~/db/schema/quiz";
import { eq } from "drizzle-orm";
import { deriveQuizKeyBytes } from "~/lib/crypto.server";
import { webcrypto } from "node:crypto";
import { withDatabaseRetry, sanitizeDbParameter } from "~/lib/db-retry.server";

const crypto = webcrypto as Crypto;

const RequestSchema = z.object({
  clientPublicKeyJwk: z.any(),
});

// Validation schema for quiz ID
const quizIdSchema = z.string().min(1, "Quiz ID is required");

export async function action({ request, params }: ActionFunctionArgs) {
  const { id } = params;

  // Validate quiz ID
  const quizIdValidation = quizIdSchema.safeParse(id);
  if (!quizIdValidation.success) {
    return Response.json({
      success: false,
      error: "Invalid quiz ID format",
      details: quizIdValidation.error.errors
    }, { status: 400 });
  }

  // Properly decode URL parameter and sanitize for Cloudflare Workers + Supabase HTTP driver
  const validQuizId = sanitizeDbParameter(quizIdValidation.data);

  // Log the quiz ID for debugging (only in development)
  if (process.env.NODE_ENV !== 'production') {
    console.log('🔍 Quiz Key - Quiz ID for database query:', JSON.stringify(validQuizId));
  }

  try {
    // Ensure authenticated
    const auth = await requireAuthSession(request);

    // Verify user has access to quiz with database retry
    const rows = await withDatabaseRetry(() =>
      db
        .select({ id: quiz.id, isPublic: quiz.isPublic, createdBy: quiz.createdBy })
        .from(quiz)
        .where(eq(quiz.id, validQuizId))
        .limit(1)
    ) as Array<{
      id: string;
      isPublic: boolean;
      createdBy: string;
    }>;

    if (rows.length === 0) return Response.json({ success: false, error: "Quiz not found" }, { status: 404 });
    const q = rows[0];
    if (!q.isPublic && q.createdBy !== auth.user.id) {
      return Response.json({ success: false, error: "Access denied" }, { status: 403 });
    }

    const body = await request.json().catch(() => ({}));
    const input = RequestSchema.safeParse(body);
    if (!input.success) {
      return Response.json({ success: false, error: "Invalid request body" }, { status: 400 });
    }
    // 1) Import client ephemeral public key
    const clientPubKey = await crypto.subtle.importKey(
      "jwk",
      input.data.clientPublicKeyJwk,
      { name: "ECDH", namedCurve: "P-256" },
      false,
      []
    );

    // 2) Generate server ephemeral ECDH key pair
    const serverKeyPair = await crypto.subtle.generateKey(
      { name: "ECDH", namedCurve: "P-256" },
      true,
      ["deriveKey", "deriveBits"]
    );

    // 3) Derive shared secret and KEK using HKDF-SHA256
    const sharedSecret = await crypto.subtle.deriveBits(
      { name: "ECDH", public: clientPubKey },
      serverKeyPair.privateKey,
      256
    );

    const hkdfSalt = new TextEncoder().encode(`quiz-kek-salt:${validQuizId}`);
    const hkdfInfo = new TextEncoder().encode("kwaci-learning-quiz-kek");

    const ikmKey = await crypto.subtle.importKey(
      "raw",
      sharedSecret,
      { name: "HKDF" },
      false,
      ["deriveKey"]
    );

    const kek = await crypto.subtle.deriveKey(
      { name: "HKDF", salt: hkdfSalt, info: hkdfInfo, hash: "SHA-256" },
      ikmKey,
      { name: "AES-GCM", length: 256 },
      true,
      ["encrypt", "decrypt"]
    );

    // 4) Get per-quiz content key bytes
    const contentKeyBytes = await deriveQuizKeyBytes(validQuizId);

    // 5) Wrap (encrypt) the content key bytes with KEK using AES-GCM
    const iv = crypto.getRandomValues(new Uint8Array(12));
    const wrapped = await crypto.subtle.encrypt(
      { name: "AES-GCM", iv },
      kek,
      contentKeyBytes as unknown as BufferSource
    );

    const serverPublicKeyJwk = await crypto.subtle.exportKey("jwk", serverKeyPair.publicKey);

    const b64 = (buf: ArrayBuffer | Uint8Array) =>
      Buffer.from(buf instanceof ArrayBuffer ? new Uint8Array(buf) : buf).toString("base64");

    return Response.json({
      success: true,
      data: {
        alg: "AES-GCM",
        iv: b64(iv),
        wrappedKey: b64(wrapped),
        serverPublicKeyJwk,
        expiresAt: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutes
      },
    });
  } catch (error) {
    // If it's a Response (like 401 from requireAuthSession), re-throw it
    if (error instanceof Response) {
      throw error;
    }

    console.error("Failed to produce wrapped quiz key:", error);
    return Response.json({ success: false, error: "Key exchange failed" }, { status: 500 });
  }
}

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Optional health/info endpoint
    await requireAuthSession(request);
    return Response.json({ success: true });
  } catch (error) {
    // If it's a Response (like 401 from requireAuthSession), re-throw it
    if (error instanceof Response) {
      throw error;
    }

    console.error("Error in quiz-key loader:", error);
    return Response.json({ success: false, error: "Internal server error" }, { status: 500 });
  }
}

