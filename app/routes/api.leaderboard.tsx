import type { LoaderFunctionArgs } from "react-router";
import { sql } from "drizzle-orm";
import { db } from "~/db";
import type { UserLeaderboard } from "~/db/schema/leaderboard";
import { requireAuthSession } from "~/lib/auth/supabase-server";

export async function loader({ request }: LoaderFunctionArgs) {
  const timestamp = new Date().toISOString();
  const url = new URL(request.url);

  console.log(`[${timestamp}] [LEADERBOARD] Request received:`, {
    method: request.method,
    url: url.toString(),
    searchParams: Object.fromEntries(url.searchParams),
    hasAuthHeader: !!request.headers.get('Authorization'),
    hasCookies: !!request.headers.get('Cookie'),
    userAgent: request.headers.get('User-Agent')?.substring(0, 100),
  });

  try {
    // Require auth to access leaderboard
    console.log(`[${timestamp}] [LEADERBOARD] Calling requireAuthSession...`);
    const authSession = await requireAuthSession(request);

    console.log(`[${timestamp}] [LEADERBOARD] Auth session obtained:`, {
      hasUser: !!authSession?.user,
      userId: authSession?.user?.id,
      userEmail: authSession?.user?.email,
      hasSession: !!authSession?.session,
    });

    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;

    console.log(`[${timestamp}] [LEADERBOARD] Query parameters:`, {
      page,
      limit,
      offset,
    });

    // Get all users with their points from the materialized view and actual completion counts
    // This is much faster than the previous complex aggregation queries
    console.log(`[${timestamp}] [LEADERBOARD] Executing database query...`);
    const queryResult = await db.execute(sql`
      SELECT
        mv.user_id as "userId",
        mv.user_name as "userName",
        mv.user_email as "userEmail",
        NULL as "userAvatar",
        mv.total_points as "totalPoints",
        mv.quiz_points as "quizPoints",
        mv.progress_points as "progressPoints",
        mv.analytics_points as "analyticsPoints",
        mv.rank,
        mv.last_updated as "lastUpdated",
        -- Get actual completion counts from respective tables
        (
          SELECT COUNT(DISTINCT lp.content_id)
          FROM learning_progress lp
          WHERE lp.user_id = mv.user_id
          AND lp.is_completed = true
        ) as "completedContent",
        (
          SELECT COUNT(*)
          FROM quiz_attempt qa
          WHERE qa.user_id = mv.user_id
          AND qa.completed_at IS NOT NULL
        ) as "completedQuizzes",
        (
          SELECT COUNT(DISTINCT lca.session_id)
          FROM learning_content_analytics lca
          WHERE lca.user_id = mv.user_id
          AND lca.session_id IS NOT NULL
        ) as "totalSessions"
      FROM user_points_leaderboard_mv mv
      ORDER BY mv.rank
    `);

    console.log(`[${timestamp}] [LEADERBOARD] Raw query result:`, {
      isArray: Array.isArray(queryResult),
      hasRowsProperty: !!(queryResult as any)?.rows,
      rowsIsArray: Array.isArray((queryResult as any)?.rows),
      queryResultType: typeof queryResult,
      queryResultKeys: queryResult && typeof queryResult === 'object' ? Object.keys(queryResult) : 'N/A',
      rowsLength: Array.isArray((queryResult as any)?.rows) ? (queryResult as any).rows.length : 'N/A',
    });

    // Support both drivers: drizzle may return either an array or an object with rows
    const rawRows = Array.isArray((queryResult as any)?.rows)
      ? (queryResult as any).rows
      : Array.isArray(queryResult)
        ? (queryResult as any)
        : [] as any[];

    // If no data, return an empty payload (not a crash)
    if (!Array.isArray(rawRows)) {
      console.warn(`[${timestamp}] [LEADERBOARD] Unexpected query result shape`);
    }

    const safeRows: any[] = Array.isArray(rawRows) ? rawRows : [];

    console.log(`[${timestamp}] [LEADERBOARD] Processed rows:`, {
      safeRowsLength: safeRows.length,
      firstRowSample: safeRows.length > 0 ? {
        userId: safeRows[0]?.userId,
        userName: safeRows[0]?.userName,
        totalPoints: safeRows[0]?.totalPoints,
        rank: safeRows[0]?.rank,
      } : 'No rows',
    });

    // Convert raw SQL results to typed objects
    const usersWithRanks = safeRows.map((row: any) => ({
      userId: row.userId,
      userName: row.userName,
      userEmail: row.userEmail,
      userAvatar: row.userAvatar,
      totalPoints: row.totalPoints,
      quizPoints: row.quizPoints,
      progressPoints: row.progressPoints,
      analyticsPoints: row.analyticsPoints,
      rank: row.rank,
      lastUpdated: row.lastUpdated,
      completedContent: row.completedContent,
      completedQuizzes: row.completedQuizzes,
      totalSessions: row.totalSessions,
      isCurrentUser: false
    }));

    // Get the page of results
    const leaderboardQuery = usersWithRanks.slice(offset, offset + limit);

    // Get total count for pagination
    const totalCount = usersWithRanks.length || 0;

    // Find current user's rank and data from the pre-calculated results
    let currentUserRank = 0;
    let currentUserData = null;
    if (authSession.user.id) {
      const currentUserInfo = usersWithRanks.find((u: any) => u.userId === authSession.user.id);
      if (currentUserInfo) {
        currentUserRank = currentUserInfo.rank;
        currentUserData = {
          ...currentUserInfo,
          isCurrentUser: true
        };
      }
    }

    console.log(`[${timestamp}] [LEADERBOARD] Current user processing:`, {
      currentUserId: authSession?.user?.id,
      currentUserRank,
      hasCurrentUserData: !!currentUserData,
    });

    // Add isCurrentUser flag to results (ranks are already calculated)
    const leaderboardWithRanks = leaderboardQuery.map((user: any) => ({
      ...user,
      isCurrentUser: authSession?.user?.id ? user.userId === authSession.user.id : false,
    }));

    const responseData = {
      success: true,
      data: {
        leaderboard: leaderboardWithRanks,
        pagination: {
          page,
          limit,
          total: totalCount,
          hasMore: offset + limit < totalCount
        },
        currentUser: {
          rank: currentUserRank,
          id: authSession.user.id,
          data: currentUserData // Include full user data if not in top results
        }
      }
    };

    console.log(`[${timestamp}] [LEADERBOARD] Final response:`, {
      success: responseData.success,
      leaderboardLength: responseData.data.leaderboard.length,
      pagination: responseData.data.pagination,
      currentUserRank: responseData.data.currentUser.rank,
      currentUserId: responseData.data.currentUser.id,
    });

    return Response.json(responseData);

  } catch (error) {
    if (error instanceof Response && error.status === 401) {
      console.log(`[${timestamp}] [LEADERBOARD] Auth error (401):`, {
        status: error.status,
        statusText: error.statusText,
      });
      return error;
    }

    console.error(`[${timestamp}] [LEADERBOARD] Error occurred:`, {
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : error,
      errorType: typeof error,
    });

    const message = error instanceof Error ? error.message : 'Failed to fetch leaderboard data';
    return Response.json({
      success: false,
      error: message,
    }, { status: 500 });
  }
}

// Helper function to calculate ranks with proper tie handling
function calculateRanksWithTies(users: any[]): any[] {
  if (users.length === 0) return [];

  let currentRank = 1;
  let previousPoints: number | null = null;
  let usersAtCurrentPoints = 0;

  return users.map((user, index) => {
    // If points changed, update rank
    if (previousPoints !== null && user.totalPoints !== previousPoints) {
      currentRank += usersAtCurrentPoints;
      usersAtCurrentPoints = 1;
    } else {
      usersAtCurrentPoints++;
    }

    previousPoints = user.totalPoints;

    return {
      ...user,
      rank: currentRank
    };
  });
}