import { type ActionFunctionArgs, type LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { createRagSearchService } from "~/lib/rag";
import { trackAnalyticsEvent } from "~/db/services/analytics";
import { log } from "~/lib/logger";

// Validation schemas
const searchQuerySchema = z.object({
  query: z.string().min(1, "Search query is required"),
  contentType: z.enum(["standard", "kwaci-primer", "all"]).default("all"),
  learningLevel: z.enum(["beginner", "intermediate", "advanced", "all"]).default("all"),
  limit: z.number().min(1).max(50).default(10),
  includeMetadata: z.boolean().default(true),
  searchType: z.enum(["semantic", "keyword", "hybrid"]).default("hybrid"),
});

const indexContentSchema = z.object({
  action: z.literal("index_content"),
  contentId: z.string(),
  forceReindex: z.boolean().default(false),
});

const searchActionSchema = z.discriminatedUnion("action", [
  indexContentSchema,
]);

// GET /api/search - Perform RAG search on learning content
export async function loader({ request }: LoaderFunctionArgs) {
  const authSession = await requireAuthSession(request);
  const url = new URL(request.url);
  
  // Parse query parameters
  const queryParams = Object.fromEntries(url.searchParams);
  
  try {
    const searchParams = searchQuerySchema.parse({
      ...queryParams,
      limit: queryParams.limit ? parseInt(queryParams.limit) : 10,
      includeMetadata: queryParams.includeMetadata !== 'false',
    });

    // Initialize RAG search service with mock vector indexing service
    const mockVectorIndexingService = {
      indexContent: async (content: any) => [],
      indexChunks: async (chunks: any[]) => [],
      isContentIndexed: async (contentId: string) => false,
      deleteContentIndex: async (contentId: string) => {},
      updateContentIndex: async (content: any) => [],
      searchContent: async (query: string, options: any) => ({ 
        results: [], 
        query, 
        totalResults: 0, 
        processingTimeMs: 0 
      }),
      searchContentScoped: async (query: string, contentIds: string[], options: any) => ({ 
        results: [], 
        query, 
        totalResults: 0, 
        processingTimeMs: 0,
        contentScope: contentIds 
      })
    };
    const ragSearchService = createRagSearchService(mockVectorIndexingService);

    // Build search filters
    const filters: Record<string, any> = {};
    
    if (searchParams.contentType !== "all") {
      filters.contentType = searchParams.contentType;
    }
    
    if (searchParams.learningLevel !== "all") {
      filters.learningLevel = searchParams.learningLevel;
    }

    // Perform semantic search
    const searchResults = await ragSearchService.searchContent(searchParams.query, {
      limit: searchParams.limit,
      includeMetadata: searchParams.includeMetadata,
    });

    // Track search analytics
    await trackAnalyticsEvent(db, {
      contentId: "search",
      userId: authSession.user.id,
      eventType: "interaction",
      metadata: {
        action: "search_performed",
        deviceType: searchParams.searchType,
      },
    });

    return Response.json({
      success: true,
      data: {
        query: searchParams.query,
        results: searchResults.results,
        metadata: {
          totalResults: searchResults.totalResults,
          searchTime: searchResults.processingTimeMs || 0,
          searchType: searchParams.searchType,
          filters: filters,
        }
      }
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        { success: false, error: "Invalid search parameters", details: error.errors },
        { status: 400 }
      );
    }
    
    log.error('Error performing search', { error: error instanceof Error ? error.message : String(error) });
    return Response.json(
      { success: false, error: "Search failed" },
      { status: 500 }
    );
  }
}

// POST /api/search - Handle search-related actions (like indexing content)
export async function action({ request }: ActionFunctionArgs) {
  const authSession = await requireAuthSession(request);
  
  if (request.method !== "POST") {
    return Response.json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const input = searchActionSchema.parse(body);

    switch (input.action) {
      case "index_content": {
        // Initialize RAG search service with mock vector indexing service
        const mockVectorIndexingService = {
          indexContent: async (content: any) => [{
            vectorId: 'mock-vector',
            contentId: content.id,
            chunkId: 'mock-chunk',
            embedding: [],
            metadata: {
              learningContentId: content.id,
              stepId: 'mock-step',
              stepTitle: 'Mock Step',
              chunkIndex: 0,
              chunkText: content.content,
              embeddingModel: 'mock',
              embeddingDimension: 1536,
              processingMetadata: {
                chunkingStrategy: 'fixed-size' as const,
                chunkSize: 1000,
                chunkOverlap: 200
              }
            },
            createdAt: new Date()
          }],
          indexChunks: async (chunks: any[]) => chunks.map((chunk, i) => ({
            vectorId: `mock-vector-${i}`,
            contentId: chunk.contentId,
            chunkId: `mock-chunk-${i}`,
            embedding: [],
            metadata: {
              learningContentId: chunk.contentId,
              stepId: 'mock-step',
              stepTitle: 'Mock Step',
              chunkIndex: i,
              chunkText: chunk.text || '',
              embeddingModel: 'mock',
              embeddingDimension: 1536,
              processingMetadata: {
                chunkingStrategy: 'fixed-size' as const,
                chunkSize: 1000,
                chunkOverlap: 200
              }
            },
            createdAt: new Date()
          })),
          isContentIndexed: async (contentId: string) => false,
          deleteContentIndex: async (contentId: string) => {},
          updateContentIndex: async (content: any) => [{
            vectorId: 'mock-vector',
            contentId: content.id,
            chunkId: 'mock-chunk',
            embedding: [],
            metadata: {
              learningContentId: content.id,
              stepId: 'mock-step',
              stepTitle: 'Mock Step',
              chunkIndex: 0,
              chunkText: content.content,
              embeddingModel: 'mock',
              embeddingDimension: 1536,
              processingMetadata: {
                chunkingStrategy: 'fixed-size' as const,
                chunkSize: 1000,
                chunkOverlap: 200
              }
            },
            createdAt: new Date()
          }],
          searchContent: async (query: string, options: any) => ({ results: [], total: 0, query, totalResults: 0, processingTimeMs: 0 }),
          searchContentScoped: async (query: string, contentIds: string[], options: any) => ({ results: [], total: 0, query, totalResults: 0, processingTimeMs: 0, contentScope: contentIds })
        };
        const ragSearchService = createRagSearchService(mockVectorIndexingService);

        // Index the specified content
        const indexResult = await ragSearchService.indexContent({
          id: input.contentId,
          title: `Content ${input.contentId}`,
          content: `Content for ${input.contentId}`,
          forceReindex: input.forceReindex,
        });

        // Track indexing analytics
        await trackAnalyticsEvent(db, {
          userId: authSession.user.id,
          eventType: "interaction",
          contentId: input.contentId,
          metadata: {
            action: "content_indexed",
            forceReindex: input.forceReindex,
            chunksCreated: indexResult.chunksCreated,
            vectorsGenerated: indexResult.vectorsGenerated,
          },
        });

        return Response.json({ success: true, data: indexResult });
      }

      default:
        return Response.json({ success: false, error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }
    
    log.error('Error processing search action', { error: error instanceof Error ? error.message : String(error) });
    return Response.json(
      { success: false, error: "Failed to process request" },
      { status: 500 }
    );
  }
}