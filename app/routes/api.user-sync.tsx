import type { ActionFunctionArgs } from "react-router";
import { db } from "~/db";
import { user as userTable } from "~/db/schema";
import { eq } from "drizzle-orm";

export async function action({ request }: ActionFunctionArgs) {
  try {
    if (request.method !== 'POST') {
      return Response.json({ success: false, error: 'Method not allowed' }, { status: 405 });
    }

    const { user } = await request.json();
    if (!user?.id) {
      return Response.json({ success: false, error: 'Invalid user payload' }, { status: 400 });
    }

    // Upsert user into application database
    const existing = await db.select().from(userTable).where(eq(userTable.id, user.id)).limit(1);

    const userData = {
      id: user.id,
      email: user.email ?? '',
      name: user.user_metadata?.full_name || user.email?.split('@')[0] || 'User',
      emailVerified: !!user.email_confirmed_at,
      image: user.user_metadata?.avatar_url || null,
      avatar: user.user_metadata?.avatar_url || null,
      updatedAt: new Date(),
    } as any;

    if (existing.length === 0) {
      await db.insert(userTable).values({ ...userData, createdAt: new Date() });
    } else {
      await db.update(userTable).set(userData).where(eq(userTable.id, user.id));
    }

    return Response.json({ success: true });
  } catch (error) {
    console.error('User sync API error:', error);
    return Response.json({ success: false, error: 'Internal Server Error' }, { status: 500 });
  }
}
