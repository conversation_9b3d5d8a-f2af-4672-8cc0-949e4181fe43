import type {
  ActionFunctionArgs,
  LoaderFunctionArgs,
  MetaFunction,
} from "react-router";
import { useLoaderData, Form, Link } from "react-router";
import { redirect } from "react-router";
import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { Button } from "~/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Progress } from "~/components/ui/progress";

import { Clock, BookO<PERSON>, Brain, ArrowRight } from "lucide-react";
import { useAuth } from "~/lib/auth/auth-provider";
import { createAuthenticatedLoader, createAuthenticatedAction } from "~/lib/auth/middleware";

export const meta: MetaFunction = () => {
  return [
    { title: "Dashboard - Kwaci Learning" },
    { name: "description", content: "Your learning dashboard" },
  ];
};

export async function action({ request }: ActionFunctionArgs) {
  // Import and use the middleware function directly
  const { requireAuth } = await import('~/lib/auth/supabase-server');
  const authContext = await requireAuth(request);
  
  // Handle authenticated actions here
  // Logout is still handled client-side, but we can add server-side actions if needed
  return {
    success: true,
    user: authContext.user,
  };
}

export async function loader({ request }: LoaderFunctionArgs) {
  // Import and use the middleware function directly
  const { requireAuth } = await import('~/lib/auth/supabase-server');
  const authContext = await requireAuth(request);
  
  // Server-side authentication is now enforced
  // User is guaranteed to be authenticated at this point
  return {
    user: {
      id: authContext.user!.id,
      email: authContext.user!.email,
      emailVerified: authContext.user!.email_confirmed_at != null,
    },
    timestamp: new Date().toISOString(),
  };
}

export default function Dashboard() {
  const { user, timestamp } = useLoaderData<typeof loader>();
  const { signOut } = useAuth();

  // User is guaranteed to be authenticated due to server-side validation
  // No need for client-side authentication checks

  // Mock data for now - in a real app, you'd fetch this based on user.id
  const skillAreas = [
    { name: "Skill Area 1", progress: 86 },
    { name: "Skill Area 2", progress: 25 },
    { name: "Skill Area 3", progress: 33 },
    { name: "Skill Area 4", progress: 66 },
    { name: "Skill Area 5", progress: 88 },
  ];

  const currentDate = new Date().toLocaleDateString();

  const handleLogout = async () => {
    try {
      const logoutSuccessful = await signOut();
      if (!logoutSuccessful) {
        console.error("Logout failed - please try again or refresh the page");
        // You could show a toast notification here
        alert("Logout failed. Please try again or refresh the page.");
      }
      // Redirect will happen automatically via AuthProvider if successful
    } catch (error) {
      console.error("Logout error:", error);
      alert("An error occurred during logout. Please refresh the page.");
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Dashboard</h1>
          <Button onClick={handleLogout} variant="outline">
            Sign Out
          </Button>
        </div>

        {/* Quick Glance Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Continue Learning Materials */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="text-lg flex items-center gap-2">
                  <BookOpen className="h-5 w-5" />
                  Continue Learning
                </CardTitle>
                <CardDescription>Pick up where you left off</CardDescription>
              </div>
              <Link to="/dashboard/my-learning">
                <Button variant="ghost" size="sm">
                  View All <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </Link>
            </CardHeader>
            <CardContent>
              <div className="text-center py-6 text-muted-foreground">
                <BookOpen className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">Welcome to your learning dashboard!</p>
                <p className="text-xs mt-1">
                  Start exploring learning materials
                </p>
                <Link to="/dashboard/learn">
                  <Button variant="outline" size="sm" className="mt-2">
                    Start Learning
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          {/* Continue Quizzes */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Brain className="h-5 w-5" />
                  Continue Quizzes
                </CardTitle>
                <CardDescription>Complete your started quizzes</CardDescription>
              </div>
              <Link to="/dashboard/quizzes">
                <Button variant="ghost" size="sm">
                  View All <ArrowRight className="h-4 w-4 ml-1" />
                </Button>
              </Link>
            </CardHeader>
            <CardContent>
              <div className="text-center py-6 text-muted-foreground">
                <Brain className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No quizzes in progress</p>
                <p className="text-xs mt-1">
                  Test your knowledge with interactive quizzes
                </p>
                <Link to="/dashboard/quizzes">
                  <Button variant="outline" size="sm" className="mt-2">
                    Browse Quizzes
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer Actions */}
        <div className="flex justify-between items-center pt-8 border-t">
          <div className="text-sm text-muted-foreground">
            Welcome, {user?.email || "User"}!
          </div>
          <div className="text-sm text-muted-foreground">
            Last updated: {currentDate}
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
