import type { LoaderFunctionArgs } from "react-router";
import { sql, and, gte, lte, count } from "drizzle-orm";
import { db } from "~/db";
import { learningContentAnalytics } from "~/db/schema/analytics";
import { requireAuthSession } from "~/lib/auth/supabase-server";

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Require auth to access contributions
    const authSession = await requireAuthSession(request);
    
    const url = new URL(request.url);
    const year = parseInt(url.searchParams.get('year') || new Date().getFullYear().toString());
    
    // Get timezone offset from client (if provided) or default to UTC
    const timezoneOffset = parseInt(url.searchParams.get('timezoneOffset') || '0');
    
    // Get start and end dates for the year in UTC
    const startDate = new Date(Date.UTC(year, 0, 1)); // January 1st UTC
    const endDate = new Date(Date.UTC(year, 11, 31, 23, 59, 59)); // December 31st UTC

    // Get daily session counts for the user with timezone adjustment
    const contributionsQuery = await db
      .select({
        date: sql<string>`DATE(${learningContentAnalytics.createdAt} + INTERVAL '${sql.raw(timezoneOffset.toString())} minutes')`.as('date'),
        sessionCount: sql<number>`COUNT(DISTINCT ${learningContentAnalytics.sessionId})`.as('sessionCount'),
        totalEvents: count(learningContentAnalytics.id).as('totalEvents')
      })
      .from(learningContentAnalytics)
      .where(
        and(
          sql`${learningContentAnalytics.userId} = ${authSession.user.id}`,
          gte(learningContentAnalytics.createdAt, startDate),
          lte(learningContentAnalytics.createdAt, endDate),
          sql`${learningContentAnalytics.sessionId} IS NOT NULL`,
          // Add length check to filter out potentially corrupted session IDs
          sql`LENGTH(${learningContentAnalytics.sessionId}) < 1000`
        )
      )
      .groupBy(sql`DATE(${learningContentAnalytics.createdAt} + INTERVAL '${sql.raw(timezoneOffset.toString())} minutes')`)
      .orderBy(sql`DATE(${learningContentAnalytics.createdAt} + INTERVAL '${sql.raw(timezoneOffset.toString())} minutes')`)

    // Create a map for quick lookup
    const contributionMap = new Map();
    contributionsQuery.forEach((row: { date: string; sessionCount: number; totalEvents: number }) => {
      // Add validation to prevent extremely large session counts
      // Cap session count at a reasonable maximum (e.g., 100 sessions per day)
      const validatedSessionCount = Math.min(Math.max(0, row.sessionCount || 0), 100);
      
      contributionMap.set(row.date, {
        sessionCount: validatedSessionCount,
        totalEvents: row.totalEvents
      });
    });

    // Generate all dates for the year and fill in contribution data
    const contributions = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      const dateStr = currentDate.toISOString().split('T')[0];
      const contribution = contributionMap.get(dateStr);
      
      contributions.push({
        date: dateStr,
        sessionCount: contribution?.sessionCount || 0,
        totalEvents: contribution?.totalEvents || 0,
        // Determine contribution level (0-4) similar to GitHub
        level: getContributionLevel(contribution?.sessionCount || 0)
      });
      
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Calculate statistics with additional validation
    const totalSessions = Math.min(
      contributions.reduce((sum, day) => sum + day.sessionCount, 0),
      10000 // Cap total sessions at a reasonable maximum
    );
    const activeDays = contributions.filter(day => day.sessionCount > 0).length;
    const longestStreak = calculateLongestStreak(contributions);
    const currentStreak = calculateCurrentStreak(contributions);
    
    // Add debugging information in development
    if (process.env.NODE_ENV === 'development') {
      const rawTotal = contributions.reduce((sum, day) => sum + day.sessionCount, 0);
      if (rawTotal > 1000) {
        console.warn(`[DEBUG] Large session count detected: ${rawTotal} sessions for user ${authSession.user.id}`);
        console.warn(`[DEBUG] Active days: ${activeDays}, Max daily sessions: ${Math.max(...contributions.map(day => day.sessionCount))}`);
      }
    }

    // Get max sessions in a single day for scaling
    const maxSessions = Math.max(...contributions.map(day => day.sessionCount));

    return Response.json({
      success: true,
      data: {
        contributions,
        stats: {
          year,
          totalSessions,
          activeDays,
          longestStreak,
          currentStreak,
          maxSessions
        }
      }
    });

  } catch (error) {
    if (error instanceof Response && error.status === 401) return error;
    console.error('Error fetching contributions:', error);
    return Response.json({
      success: false,
      error: 'Failed to fetch contribution data'
    }, { status: 500 });
  }
}

// Helper function to determine contribution level (0-4) based on session count
function getContributionLevel(sessionCount: number): number {
  if (sessionCount === 0) return 0;
  if (sessionCount === 1) return 1;
  if (sessionCount <= 3) return 2;
  if (sessionCount <= 6) return 3;
  return 4; // 7+ sessions
}

// Helper function to calculate longest streak
function calculateLongestStreak(contributions: Array<{ sessionCount: number }>): number {
  let maxStreak = 0;
  let currentStreak = 0;
  
  contributions.forEach(day => {
    if (day.sessionCount > 0) {
      currentStreak++;
      maxStreak = Math.max(maxStreak, currentStreak);
    } else {
      currentStreak = 0;
    }
  });
  
  return maxStreak;
}

// Helper function to calculate current streak (from today backwards)
function calculateCurrentStreak(contributions: Array<{ date: string; sessionCount: number }>): number {
  let streak = 0;
  const today = new Date().toISOString().split('T')[0];
  
  // Find today's index in the contributions array
  const todayIndex = contributions.findIndex(day => day.date === today);
  
  // If today is not found, return 0
  if (todayIndex === -1) {
    return 0;
  }
  
  // Start from today and work backwards
  for (let i = todayIndex; i >= 0; i--) {
    if (contributions[i].sessionCount > 0) {
      streak++;
    } else {
      break;
    }
  }
  return streak;
}