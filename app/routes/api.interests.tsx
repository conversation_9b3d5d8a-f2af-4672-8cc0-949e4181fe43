import { type ActionFunctionArgs, type LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { 
  focusAreas, 
  themes, 
  topics, 
  userLearningInterests, 
  learningRecommendations 
} from "~/db/schema/learning-interests";
import { eq, and, desc, asc } from "drizzle-orm";
import { nanoid } from "nanoid";

// Helper function to handle AI recommendations
async function handleAIRecommendations(userId: string, params: any) {
  const {
    limit = 10,
    offset = 0,
    recommendationType = "content",
    includeViewed = false,
    includeCompleted = false,
  } = params;

  try {
    const conditions = [eq(learningRecommendations.userId, userId)];

    // Filter by recommendation type
    if (recommendationType) {
      conditions.push(eq(learningRecommendations.recommendationType, recommendationType));
    }

    // Filter out viewed recommendations if requested
    if (!includeViewed) {
      conditions.push(eq(learningRecommendations.isViewed, false));
    }

    // Filter out completed recommendations if requested
    if (!includeCompleted) {
      conditions.push(eq(learningRecommendations.isCompleted, false));
    }

    // Get recommendations with topic information
    const recommendations = await db
      .select({
        recommendation: learningRecommendations,
        topic: topics,
      })
      .from(learningRecommendations)
      .leftJoin(topics, eq(learningRecommendations.topicId, topics.id))
      .where(and(...conditions))
      .orderBy(desc(learningRecommendations.generatedAt))
      .limit(limit)
      .offset(offset);

    return Response.json({
      success: true,
      data: {
        recommendations: recommendations.map((row: any) => ({
          recommendation: row.recommendation,
          topic: row.topic,
        })),
        total: recommendations.length,
        hasMore: recommendations.length === limit,
      }
    });
  } catch (error) {
    console.error("Error fetching AI recommendations:", error);
    return Response.json(
      { success: false, error: "Failed to fetch AI recommendations" },
      { status: 500 }
    );
  }
}

// Validation schemas
const updateInterestSchema = z.object({
  action: z.literal("update_interest"),
  topicId: z.string(),
  interestLevel: z.number().min(1).max(5),
  priority: z.number().min(1).max(10),
});

const getRecommendationsSchema = z.object({
  action: z.literal("get_recommendations"),
  focusAreaId: z.string().optional(),
  themeId: z.string().optional(),
  limit: z.number().min(1).max(50).default(10),
});

const getAIRecommendationsSchema = z.object({
  limit: z.number().min(1).max(50).default(10),
  offset: z.number().min(0).default(0),
  recommendationType: z.enum(["content", "quiz", "topic"]).default("content"),
  includeViewed: z.boolean().default(false),
  includeCompleted: z.boolean().default(false),
});

const markRecommendationViewedSchema = z.object({
  action: z.literal("mark_recommendation_viewed"),
  recommendationId: z.string(),
});

const interestActionSchema = z.discriminatedUnion("action", [
  updateInterestSchema,
  getRecommendationsSchema,
  markRecommendationViewedSchema,
]);

const querySchema = z.object({
  focusAreaId: z.string().optional(),
  themeId: z.string().optional(),
  includeUserInterests: z.boolean().default(true),
  // AI Recommendations query params
  limit: z.number().min(1).max(50).optional(),
  offset: z.number().min(0).optional(),
  recommendationType: z.enum(["content", "quiz", "topic"]).optional(),
  includeViewed: z.boolean().optional(),
  includeCompleted: z.boolean().optional(),
});

// GET /api/interests - Get interest hierarchy, user preferences, or AI recommendations
export async function loader({ request }: LoaderFunctionArgs) {
  const authSession = await requireAuthSession(request);
  const url = new URL(request.url);

  const queryParams = Object.fromEntries(url.searchParams);
  const parsedParams = querySchema.parse({
    ...queryParams,
    includeUserInterests: queryParams.includeUserInterests !== 'false',
    limit: queryParams.limit ? parseInt(queryParams.limit) : undefined,
    offset: queryParams.offset ? parseInt(queryParams.offset) : undefined,
    includeViewed: queryParams.includeViewed ? queryParams.includeViewed === 'true' : undefined,
    includeCompleted: queryParams.includeCompleted ? queryParams.includeCompleted === 'true' : undefined,
  });

  // Check if this is a request for AI recommendations
  if (parsedParams.limit !== undefined || parsedParams.recommendationType !== undefined) {
    return handleAIRecommendations(authSession.user.id, parsedParams);
  }

  const { focusAreaId, themeId, includeUserInterests } = parsedParams;

  try {
    // Get focus areas
    const focusAreasData = await db
      .select()
      .from(focusAreas)
      .orderBy(asc(focusAreas.order));

    // Get themes (filtered by focus area if specified)
    const themesQuery = db.select().from(themes);
    if (focusAreaId) {
      themesQuery.where(eq(themes.focusAreaId, focusAreaId));
    }
    const themesData = await themesQuery.orderBy(asc(themes.order));

    // Get topics (filtered by theme if specified)
    const topicsQuery = db.select().from(topics);
    if (themeId) {
      topicsQuery.where(eq(topics.themeId, themeId));
    }
    const topicsData = await topicsQuery.orderBy(asc(topics.name));

    // Get user interests if requested
    let userInterests = [];
    if (includeUserInterests) {
      userInterests = await db
        .select()
        .from(userLearningInterests)
        .where(eq(userLearningInterests.userId, authSession.user.id))
        .orderBy(desc(userLearningInterests.priority));
    }

    return Response.json({
      success: true,
      data: {
        focusAreas: focusAreasData,
        themes: themesData,
        topics: topicsData,
        userInterests: userInterests,
      }
    });
  } catch (error) {
    console.error("Error fetching interests:", error);
    return Response.json(
      { success: false, error: "Failed to fetch interests" },
      { status: 500 }
    );
  }
}

// POST /api/interests - Handle interest actions
export async function action({ request }: ActionFunctionArgs) {
  const authSession = await requireAuthSession(request);
  
  if (request.method !== "POST") {
    return Response.json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const input = interestActionSchema.parse(body);

    switch (input.action) {
      case "update_interest": {
        // Check if user interest already exists
        const existingInterest = await db
          .select()
          .from(userLearningInterests)
          .where(and(
            eq(userLearningInterests.userId, authSession.user.id),
            eq(userLearningInterests.topicId, input.topicId)
          ))
          .limit(1);

        let result;
        if (existingInterest.length > 0) {
          // Update existing interest
          result = await db
            .update(userLearningInterests)
            .set({
              interestLevel: input.interestLevel,
              priority: input.priority,
              updatedAt: new Date(),
            })
            .where(and(
              eq(userLearningInterests.userId, authSession.user.id),
              eq(userLearningInterests.topicId, input.topicId)
            ))
            .returning();
        } else {
          // Create new interest
          result = await db
            .insert(userLearningInterests)
            .values({
              id: nanoid(),
              userId: authSession.user.id,
              topicId: input.topicId,
              interestLevel: input.interestLevel,
              priority: input.priority,
              createdAt: new Date(),
              updatedAt: new Date(),
            })
            .returning();
        }

        return Response.json({ success: true, data: result[0] });
      }

      case "get_recommendations": {
        // Get learning recommendations based on user interests
        // This is a legacy action - new code should use the GET endpoint with query params
        const conditions = [eq(learningRecommendations.userId, authSession.user.id)];

        const recommendations = await db
          .select({
            recommendation: learningRecommendations,
            topic: topics,
          })
          .from(learningRecommendations)
          .leftJoin(topics, eq(learningRecommendations.topicId, topics.id))
          .where(and(...conditions))
          .orderBy(desc(learningRecommendations.generatedAt))
          .limit(input.limit);

        return Response.json({
          success: true,
          data: recommendations.map((row: any) => ({
            recommendation: row.recommendation,
            topic: row.topic,
          }))
        });
      }

      case "mark_recommendation_viewed": {
        // Mark a recommendation as viewed
        const result = await db
          .update(learningRecommendations)
          .set({
            isViewed: true,
            viewedAt: new Date(),
            updatedAt: new Date(),
          })
          .where(and(
            eq(learningRecommendations.id, input.recommendationId),
            eq(learningRecommendations.userId, authSession.user.id)
          ))
          .returning();

        if (result.length === 0) {
          return Response.json(
            { success: false, error: "Recommendation not found" },
            { status: 404 }
          );
        }

        return Response.json({ success: true, data: result[0] });
      }

      default:
        return Response.json({ success: false, error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }
    
    console.error("Error processing interest action:", error);
    return Response.json(
      { success: false, error: "Failed to process request" },
      { status: 500 }
    );
  }
}