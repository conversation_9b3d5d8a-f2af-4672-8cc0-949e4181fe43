import { type ActionFunctionArgs, type LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { quiz, quizAttempt, quizProgress, quizFeedback } from "~/db/schema/quiz";
import { eq, and, desc, sql } from "drizzle-orm";
import { nanoid } from "nanoid";
import { log } from "~/lib/logger";
import { encryptQuestionAnswers } from "~/lib/crypto.server";
import { withDatabaseRetry, sanitizeDbParameter } from "~/lib/db-retry.server";

// Validation schemas
const startAttemptSchema = z.object({
  action: z.literal("start"),
});

const submitAnswerSchema = z.object({
  action: z.literal("submit_answer"),
  attemptId: z.string(),
  questionId: z.string(),
  answer: z.any(), // Answer format varies by question type
  timeSpent: z.number().min(0), // in seconds
});

const completeAttemptSchema = z.object({
  action: z.literal("complete"),
  attemptId: z.string(),
  totalTimeSpent: z.number().min(0), // in seconds
});

const submitFeedbackSchema = z.object({
  action: z.literal("feedback"),
  attemptId: z.string(),
  rating: z.number().min(1).max(5),
  difficultyRating: z.enum(["too_easy", "just_right", "too_hard"]),
  comments: z.string().optional(),
  suggestedImprovements: z.string().optional(),
});

const quizActionSchema = z.discriminatedUnion("action", [
  startAttemptSchema,
  submitAnswerSchema,
  completeAttemptSchema,
  submitFeedbackSchema,
]);

// Validation schema for quiz ID
const quizIdSchema = z.string().min(1, "Quiz ID is required");

// GET /api/quiz/:id - Get quiz details and user's attempts
export async function loader({ request, params }: LoaderFunctionArgs) {
  const { id } = params;

  // Validate quiz ID
  const quizIdValidation = quizIdSchema.safeParse(id);
  if (!quizIdValidation.success) {
    return Response.json({
      success: false,
      error: "Invalid quiz ID format",
      details: quizIdValidation.error.errors
    }, { status: 400 });
  }

  // Properly decode URL parameter and sanitize for Cloudflare Workers + Supabase HTTP driver
  const validQuizId = sanitizeDbParameter(quizIdValidation.data);

  // Log the quiz ID for debugging (only in development)
  if (process.env.NODE_ENV !== 'production') {
    console.log('🔍 Quiz - Quiz ID for database query:', JSON.stringify(validQuizId));
  }

  try {
    const authSession = await requireAuthSession(request);

    // Get quiz details with database retry
    console.log('🔍 Executing quiz details query with ID:', JSON.stringify(validQuizId));
    const quizData = await withDatabaseRetry(() =>
      db
      .select({
        id: quiz.id,
        title: quiz.title,
        description: quiz.description,
        difficulty: quiz.difficulty,
        estimatedDuration: quiz.estimatedDuration,
        totalPoints: quiz.totalPoints,
        isPublic: quiz.isPublic,
        createdBy: quiz.createdBy,
        createdAt: quiz.createdAt,
        updatedAt: quiz.updatedAt,
        learningContentId: quiz.learningContentId,
        showCorrectAnswers: quiz.showCorrectAnswers,
        allowRetakes: quiz.allowRetakes,
        timeLimit: quiz.timeLimit,
        questions: sql`(
          SELECT json_agg(
            CASE 
              WHEN elem->>'type' = 'flashcard' THEN
                json_build_object(
                  'id', elem->>'id',
                  'type', elem->>'type',
                  'difficulty', elem->>'difficulty',
                  'points', (elem->>'points')::integer,
                  'question', elem->'front',
                  'front', elem->'front',
                  'back', elem->'back',
                  'hint', elem->'hint'
                )
              WHEN elem->>'type' = 'multipleChoice' THEN
                 json_build_object(
                   'id', elem->>'id',
                   'type', elem->>'type',
                   'difficulty', elem->>'difficulty',
                   'points', (elem->>'points')::integer,
                   'question', elem->'question',
                   'options', elem->'options',
                   'correctAnswerIndex', (elem->>'correctAnswerIndex')::integer,
                   'hint', elem->'hint'
                 )
              WHEN elem->>'type' = 'trueFalse' THEN
                 json_build_object(
                   'id', elem->>'id',
                   'type', elem->>'type',
                   'difficulty', elem->>'difficulty',
                   'points', (elem->>'points')::integer,
                   'question', elem->'statement',
                   'statement', elem->'statement',
                   'correctAnswer', (elem->>'correctAnswer')::boolean,
                   'hint', elem->'hint'
                 )
              WHEN elem->>'type' = 'fillInBlank' THEN
                json_build_object(
                  'id', elem->>'id',
                  'type', elem->>'type',
                  'difficulty', elem->>'difficulty',
                  'points', (elem->>'points')::integer,
                  'question', elem->'text',
                  'text', elem->'text',
                  'blanks', elem->'blanks',
                  'hint', elem->'hint'
                )
              WHEN elem->>'type' = 'matching' THEN
                json_build_object(
                  'id', elem->>'id',
                  'type', elem->>'type',
                  'difficulty', elem->>'difficulty',
                  'points', (elem->>'points')::integer,
                  'question', elem->'instruction',
                  'instruction', elem->'instruction',
                  'pairs', elem->'pairs',
                  'hint', elem->'hint'
                )
              WHEN elem->>'type' = 'freeText' THEN
                json_build_object(
                  'id', elem->>'id',
                  'type', elem->>'type',
                  'difficulty', elem->>'difficulty',
                  'points', (elem->>'points')::integer,
                  'question', elem->'question',
                  'answerType', elem->'answerType',
                  'maxLength', (elem->>'maxLength')::integer,
                  'sampleAnswer', elem->'sampleAnswer',
                  'evaluationCriteria', elem->'evaluationCriteria',
                  'hint', elem->'hint'
                )
              WHEN elem->>'type' = 'ordering' THEN
                 json_build_object(
                   'id', elem->>'id',
                   'type', elem->>'type',
                   'difficulty', elem->>'difficulty',
                   'points', (elem->>'points')::integer,
                   'question', elem->'instruction',
                   'instruction', elem->'instruction',
                   'items', elem->'items',
                   'orderType', elem->'orderType',
                   'correctOrder', elem->'correctOrder',
                   'hint', elem->'hint'
                 )
              ELSE
                json_build_object(
                  'id', elem->>'id',
                  'type', elem->>'type',
                  'difficulty', elem->>'difficulty',
                  'points', (elem->>'points')::integer,
                  'question', elem->'question',
                  'options', elem->'options',
                  'hint', elem->'hint'
                )
            END
          )
          FROM json_array_elements(${quiz.questions}) AS elem
        )`.as('questions'),
      })
      .from(quiz)
      .where(eq(quiz.id, validQuizId))
      .limit(1)
    ) as Array<any>;

    if (quizData.length === 0) {
      return Response.json({ success: false, error: "Quiz not found" }, { status: 404 });
    }

    const quizItem = quizData[0];

    // Check if user has access to this quiz
    if (!quizItem.isPublic && quizItem.createdBy !== authSession.user.id) {
      return Response.json({ success: false, error: "Access denied" }, { status: 403 });
    }

    // Encrypt answer data in questions
    const encryptedQuestions = await Promise.all(
      (quizItem.questions || []).map(async (question: any) => {
        return await encryptQuestionAnswers(question, { quizId: validQuizId });
      })
    );

    const encryptedQuiz = {
      ...quizItem,
      questions: encryptedQuestions
    };

    return Response.json({
      success: true,
      data: {
        quiz: encryptedQuiz,
      }
    });
  } catch (error) {
    // If it's a Response (like 401 from requireAuthSession), re-throw it
    if (error instanceof Response) {
      throw error;
    }

    log.error('Error fetching quiz', { error: error instanceof Error ? error.message : String(error), quizId: id });
    return Response.json(
      { success: false, error: "Failed to fetch quiz" },
      { status: 500 }
    );
  }
}

// POST /api/quiz/:id - Handle quiz actions (start, submit answers, complete, feedback)
export async function action({ request, params }: ActionFunctionArgs) {
  const { id } = params;

  // Validate quiz ID
  const quizIdValidation = quizIdSchema.safeParse(id);
  if (!quizIdValidation.success) {
    return Response.json({
      success: false,
      error: "Invalid quiz ID format",
      details: quizIdValidation.error.errors
    }, { status: 400 });
  }

  // Properly decode URL parameter and sanitize for Cloudflare Workers + Supabase HTTP driver
  const validQuizId = sanitizeDbParameter(quizIdValidation.data);

  // Log the quiz ID for debugging (only in development)
  if (process.env.NODE_ENV !== 'production') {
    console.log('🔍 Quiz Action - Quiz ID for database query:', JSON.stringify(validQuizId));
  }

  try {
    const authSession = await requireAuthSession(request);
    const body = await request.json();
    const input = quizActionSchema.parse(body);

    // Verify quiz exists and user has access with database retry
    console.log('🔍 Executing quiz action query with ID:', JSON.stringify(validQuizId));
    const quizData = await withDatabaseRetry(() =>
      db
        .select()
        .from(quiz)
        .where(eq(quiz.id, validQuizId))
        .limit(1)
    ) as Array<any>;

    if (quizData.length === 0) {
      return Response.json({ success: false, error: "Quiz not found" }, { status: 404 });
    }

    const quizItem = quizData[0];

    if (!quizItem.isPublic && quizItem.authorId !== authSession.user.id) {
      return Response.json({ success: false, error: "Access denied" }, { status: 403 });
    }

    switch (input.action) {
      case "start": {
        // Check if retakes are allowed
        if (!quizItem.allowRetakes) {
          const existingAttempts = await withDatabaseRetry(() =>
            db
              .select()
              .from(quizAttempt)
              .where(and(
                eq(quizAttempt.quizId, validQuizId),
                eq(quizAttempt.userId, authSession.user.id)
              ))
          ) as Array<any>;

          if (existingAttempts.length > 0) {
            return Response.json(
              { success: false, error: "Retakes are not allowed for this quiz" },
              { status: 403 }
            );
          }
        }

        // Create new attempt
        const newAttempt = await withDatabaseRetry(() =>
          db.insert(quizAttempt).values({
            id: nanoid(),
            quizId: validQuizId,
            userId: authSession.user.id,
            startedAt: new Date(),
            status: "in_progress",
            currentQuestionIndex: 0,
            answers: {},
            score: 0,
            maxScore: quizItem.totalPoints,
          }).returning()
        ) as Array<any>;

        return Response.json({ success: true, data: newAttempt[0] }, { status: 201 });
      }

      case "submit_answer": {
        // Update quiz progress
        const progressUpdate = await withDatabaseRetry(() =>
          db.insert(quizProgress).values({
            id: nanoid(),
            attemptId: input.attemptId,
            questionId: input.questionId,
            answer: input.answer,
            timeSpent: input.timeSpent,
            submittedAt: new Date(),
          }).returning()
        ) as Array<any>;

        // Update attempt with new answer
        await withDatabaseRetry(() =>
          db
            .update(quizAttempt)
            .set({
              answers: { [input.questionId]: input.answer },
              updatedAt: new Date(),
            })
            .where(eq(quizAttempt.id, input.attemptId))
        );

        return Response.json({ success: true, data: progressUpdate[0] });
      }

      case "complete": {
        // Calculate final score (simplified - you may want more complex scoring)
        const attempt = await withDatabaseRetry(() =>
          db
            .select()
            .from(quizAttempt)
            .where(eq(quizAttempt.id, input.attemptId))
            .limit(1)
        ) as Array<any>;

        if (attempt.length === 0) {
          return Response.json({ success: false, error: "Attempt not found" }, { status: 404 });
        }

        // Update attempt as completed
        const completedAttempt = await withDatabaseRetry(() =>
          db
            .update(quizAttempt)
            .set({
              status: "completed",
              completedAt: new Date(),
              totalTimeSpent: input.totalTimeSpent,
              updatedAt: new Date(),
            })
            .where(eq(quizAttempt.id, input.attemptId))
            .returning()
        ) as Array<any>;

        return Response.json({ success: true, data: completedAttempt[0] });
      }

      case "feedback": {
        // Submit feedback for the quiz
        const feedback = await withDatabaseRetry(() =>
          db.insert(quizFeedback).values({
            id: nanoid(),
            quizId: validQuizId,
            userId: authSession.user.id,
            attemptId: input.attemptId,
            rating: input.rating,
            difficultyRating: input.difficultyRating,
            comments: input.comments,
            suggestedImprovements: input.suggestedImprovements,
            submittedAt: new Date(),
          }).returning()
        ) as Array<any>;

        return Response.json({ success: true, data: feedback[0] }, { status: 201 });
      }

      default:
        return Response.json({ success: false, error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    // If it's a Response (like 401 from requireAuthSession), re-throw it
    if (error instanceof Response) {
      throw error;
    }

    if (error instanceof z.ZodError) {
      return Response.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    log.error('Error processing quiz action', { error: error instanceof Error ? error.message : String(error), quizId: id });
    return Response.json(
      { success: false, error: "Failed to process request" },
      { status: 500 }
    );
  }
}