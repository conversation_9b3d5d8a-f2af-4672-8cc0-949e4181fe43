import { type LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { quizAttempt, quiz, learningContent, type QuizAttempt, type Quiz } from "~/db/schema";
import type { LearningContent } from "~/db/schema/learning-content";
import { eq, and, desc, sql } from "drizzle-orm";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { withDatabaseRetry } from "~/lib/db-retry.server";

// Validation schema for quiz attempts filters
const quizAttemptsFiltersSchema = z.object({
  quizId: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

export type QuizAttemptsFilters = z.infer<typeof quizAttemptsFiltersSchema>;

// Types for quiz attempt with quiz data
export interface QuizAttemptWithQuiz {
  id: string;
  quizId: string;
  userId: string;
  startedAt: Date;
  completedAt: Date | null;
  isCompleted: boolean;
  totalTimeSpent: number;
  score: {
    totalPoints: number;
    earnedPoints: number;
    percentage: number;
    correctAnswers: number;
    totalQuestions: number;
  } | null;
  quiz: {
    id: string;
    title: string;
    description: string | null;
    difficulty: 'easy' | 'medium' | 'hard';
    estimatedDuration: number;
    totalPoints: number;
    learningContentId: string;
    learningContent: {
      id: string;
      title: string;
      description: string;
      learningLevel: 'beginner' | 'intermediate' | 'advanced';
      contentType: 'standard' | 'kwaci-primer';
    };
  };
}

export interface QuizAttemptsResponse {
  success: boolean;
  attempts: QuizAttemptWithQuiz[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

// GET /api/quiz-attempts - Get user's quiz attempts with quiz data
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    const authSession = await requireAuthSession(request);
    const url = new URL(request.url);
    
    // Parse and validate query parameters
    const rawFilters = {
      quizId: url.searchParams.get("quizId") || undefined,
      limit: parseInt(url.searchParams.get("limit") || "20"),
      offset: parseInt(url.searchParams.get("offset") || "0"),
    };

    const filters = quizAttemptsFiltersSchema.parse(rawFilters);

    // Build base query with joins
    let baseQuery = db
      .select({
        attempt: quizAttempt,
        quiz: {
          id: quiz.id,
          title: quiz.title,
          description: quiz.description,
          difficulty: quiz.difficulty,
          estimatedDuration: quiz.estimatedDuration,
          totalPoints: quiz.totalPoints,
          learningContentId: quiz.learningContentId,
        },
        learningContent: {
          id: learningContent.id,
          title: learningContent.title,
          description: learningContent.description,
          learningLevel: learningContent.learningLevel,
          contentType: learningContent.contentType,
        },
      })
      .from(quizAttempt)
      .innerJoin(quiz, eq(quizAttempt.quizId, quiz.id))
      .innerJoin(learningContent, eq(quiz.learningContentId, learningContent.id))
      .where(eq(quizAttempt.userId, authSession.user.id));

    // Apply quiz filter if specified
    if (filters.quizId) {
      baseQuery = baseQuery.where(
        and(
          eq(quizAttempt.userId, authSession.user.id),
          eq(quizAttempt.quizId, filters.quizId)
        )
      );
    }

    // Get total count for pagination
    let countQuery = db
      .select({ count: sql<number>`count(*)` })
      .from(quizAttempt)
      .where(eq(quizAttempt.userId, authSession.user.id));

    if (filters.quizId) {
      countQuery = countQuery.where(
        and(
          eq(quizAttempt.userId, authSession.user.id),
          eq(quizAttempt.quizId, filters.quizId)
        )
      );
    }

    // Execute database queries with retry logic for cold start resilience
    const [totalResult, attemptsData] = await Promise.all([
      withDatabaseRetry(() => countQuery),
      withDatabaseRetry(() =>
        baseQuery
          .orderBy(desc(quizAttempt.startedAt))
          .limit(filters.limit)
          .offset(filters.offset)
      )
    ]);

    const total = (totalResult as any)[0].count;

    // Transform data to match expected interface
    const attempts: QuizAttemptWithQuiz[] = (attemptsData as any[]).map(({ attempt, quiz: quizData, learningContent: contentData }: {
      attempt: QuizAttempt;
      quiz: any;
      learningContent: any;
    }) => ({
      id: attempt.id,
      quizId: attempt.quizId,
      userId: attempt.userId,
      startedAt: attempt.startedAt,
      completedAt: attempt.completedAt,
      isCompleted: attempt.isCompleted,
      totalTimeSpent: attempt.totalTimeSpent || 0,
      score: attempt.score,
      quiz: {
        id: quizData.id,
        title: quizData.title,
        description: quizData.description,
        difficulty: quizData.difficulty,
        estimatedDuration: quizData.estimatedDuration,
        totalPoints: quizData.totalPoints,
        learningContentId: quizData.learningContentId,
        learningContent: contentData,
      },
    }));

    const response: QuizAttemptsResponse = {
      success: true,
      attempts,
      pagination: {
        total,
        limit: filters.limit,
        offset: filters.offset,
        hasMore: filters.offset + filters.limit < total,
      },
    };

    return Response.json(response);

  } catch (error) {
    // If it's a Response (like 401 from requireAuthSession), re-throw it
    if (error instanceof Response) {
      throw error;
    }

    console.error("Error fetching quiz attempts:", error);
    return Response.json(
      { success: false, error: "Failed to fetch quiz attempts" },
      { status: 500 }
    );
  }
}
