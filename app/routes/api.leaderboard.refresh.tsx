import type { ActionFunctionArgs } from "react-router";
import { sql } from "drizzle-orm";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { immediateRefreshLeaderboard } from "~/lib/utils/materialized-view-refresh";

/**
 * API endpoint to refresh the leaderboard materialized view
 * This should be called periodically to update the leaderboard data
 * 
 * Usage:
 * POST /api/leaderboard/refresh
 * 
 * Note: In production, you might want to:
 * 1. Add rate limiting to prevent abuse
 * 2. Restrict access to admin users only
 * 3. Set up automatic refresh via cron jobs
 */
export async function action({ request }: ActionFunctionArgs) {
  try {
    // Require authentication (you might want to add admin-only check here)
    await requireAuthSession(request);
    
    // Only allow POST requests
    if (request.method !== 'POST') {
      return Response.json({
        success: false,
        error: 'Method not allowed'
      }, { status: 405 });
    }

    // Refresh the materialized view immediately (manual refresh)
    await immediateRefreshLeaderboard(db);

    return Response.json({
      success: true,
      message: 'Leaderboard materialized view refreshed successfully',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error refreshing leaderboard materialized view:', error);
    
    // Check if the error is because the materialized view doesn't exist
    if (error instanceof Error && error.message.includes('does not exist')) {
      return Response.json({
        success: false,
        error: 'Materialized view does not exist. Please create it first using the provided SQL script.',
        hint: 'Run the SQL script in create_leaderboard_materialized_view.sql in your Supabase SQL editor'
      }, { status: 404 });
    }

    return Response.json({
      success: false,
      error: 'Failed to refresh leaderboard materialized view'
    }, { status: 500 });
  }
}

// Also allow GET requests to check the last refresh time
export async function loader() {
  try {
    // Get the last refresh time from the materialized view
    const result = await db.execute(sql`
      SELECT last_updated 
      FROM user_points_leaderboard_mv 
      ORDER BY last_updated DESC 
      LIMIT 1
    `);

    const lastUpdated = result.rows[0]?.last_updated || null;

    return Response.json({
      success: true,
      data: {
        lastUpdated,
        message: lastUpdated 
          ? `Leaderboard was last updated at ${lastUpdated}`
          : 'Leaderboard has not been updated yet or materialized view is empty'
      }
    });

  } catch (error) {
    console.error('Error checking leaderboard refresh status:', error);
    
    if (error instanceof Error && error.message.includes('does not exist')) {
      return Response.json({
        success: false,
        error: 'Materialized view does not exist. Please create it first using the provided SQL script.',
        hint: 'Run the SQL script in create_leaderboard_materialized_view.sql in your Supabase SQL editor'
      }, { status: 404 });
    }

    return Response.json({
      success: false,
      error: 'Failed to check leaderboard refresh status'
    }, { status: 500 });
  }
}