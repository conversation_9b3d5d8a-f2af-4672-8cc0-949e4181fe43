import type { ActionFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { quizAttempt, quizProgress } from "~/db/schema";
import { and, eq } from "drizzle-orm";

const schema = z.object({
  attemptId: z.string(),
  currentQuestionIndex: z.number().min(0),
  timeSpentOnCurrentQuestion: z.number().min(0),
  totalSessionTime: z.number().min(0),
  bookmarkedQuestions: z.array(z.string()).optional(),
});

// POST /api/quiz-attempts/save-progress
export async function action({ request }: ActionFunctionArgs) {
  const auth = await requireAuthSession(request);
  const input = schema.parse(await request.json());

  // Verify attempt belongs to user and not completed
  const rows = await db
    .select()
    .from(quizAttempt)
    .where(and(eq(quizAttempt.id, input.attemptId), eq(quizAttempt.userId, auth.user.id)))
    .limit(1);

  if (rows.length === 0) return Response.json({ success: false, error: "Attempt not found" }, { status: 404 });
  const attempt = rows[0];
  if (attempt.isCompleted) return Response.json({ success: false, error: "Attempt already completed" }, { status: 400 });

  // Update total time
  const newTotalTime = Math.max(attempt.totalTimeSpent || 0, input.totalSessionTime);
  await db
    .update(quizAttempt)
    .set({ totalTimeSpent: newTotalTime })
    .where(eq(quizAttempt.id, attempt.id));

  // Upsert progress
  const existing = await db
    .select()
    .from(quizProgress)
    .where(eq(quizProgress.attemptId, attempt.id))
    .limit(1);

  if (existing.length > 0) {
    const answeredQuestions = Array.isArray(existing[0].answeredQuestions) ? existing[0].answeredQuestions : [];
    const updateData: any = {
      currentQuestionIndex: input.currentQuestionIndex,
      timeSpent: newTotalTime,
      lastActivityAt: new Date(),
      updatedAt: new Date(),
    };
    
    // Only update bookmarked questions if provided
    if (input.bookmarkedQuestions !== undefined) {
      updateData.bookmarkedQuestions = input.bookmarkedQuestions;
    }
    
    return Response.json({
      success: true,
      data: (
        await db
          .update(quizProgress)
          .set(updateData)
          .where(eq(quizProgress.id, existing[0].id))
          .returning()
      )[0],
    });
  }

  const [created] = await db
    .insert(quizProgress)
    .values({
      id: crypto.randomUUID(),
      attemptId: attempt.id,
      userId: auth.user.id,
      quizId: attempt.quizId,
      currentQuestionIndex: input.currentQuestionIndex,
      answeredQuestions: [],
      bookmarkedQuestions: input.bookmarkedQuestions || [],
      timeSpent: newTotalTime,
      isCompleted: false,
      lastActivityAt: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    })
    .returning();

  return Response.json({ success: true, data: created });
}

