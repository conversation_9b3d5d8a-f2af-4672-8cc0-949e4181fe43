import type { ActionFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { quizAttempt, quiz } from "~/db/schema";
import { and, eq } from "drizzle-orm";
import { encryptAnswerData } from "~/lib/crypto.server";
import { withDatabaseRetry } from "~/lib/db-retry.server";

const schema = z.object({
  attemptId: z.string(),
  questionId: z.string(),
  answer: z.any(),
  timeSpent: z.number().min(0),
  isTemporary: z.boolean().optional(),
});

// POST /api/quiz-attempts/submit-answer
export async function action({ request }: ActionFunctionArgs) {
  try {
    const auth = await requireAuthSession(request);

    // Parse and validate input
    const body = await request.json().catch(() => ({}));
    const input = schema.parse(body);

    // Verify attempt belongs to user and is in-progress with database retry
    const rows = await withDatabaseRetry(() =>
      db
        .select()
        .from(quizAttempt)
        .where(and(eq(quizAttempt.id, input.attemptId), eq(quizAttempt.userId, auth.user.id)))
        .limit(1)
    ) as Array<any>;

  if (rows.length === 0) return Response.json({ success: false, error: "Attempt not found" }, { status: 404 });
  const attempt = rows[0];
  if (attempt.isCompleted) return Response.json({ success: false, error: "Attempt already completed" }, { status: 400 });

  // Fetch quiz questions to get question type and validate answer
  const quizQuestions = await getQuizQuestions(attempt.quizId);
  const question = quizQuestions.find((q: any) => q.id === input.questionId);
  
  if (!question) {
    return Response.json({ success: false, error: "Question not found" }, { status: 404 });
  }

  // Determine if answer is correct
  const isCorrect = validateAnswer(question, input.answer);

  const answers = Array.isArray(attempt.answers) ? [...attempt.answers] : [];
  const existingIdx = answers.findIndex((a: any) => a.questionId === input.questionId);
  const answerEntry = {
    questionId: input.questionId,
    questionType: question.type,
    answer: input.answer,
    timeSpent: input.timeSpent,
    isCorrect,
  };
  if (existingIdx >= 0) {
    answers[existingIdx] = answerEntry;
  } else {
    answers.push(answerEntry);
  }

    const totalTimeSpent = (attempt.totalTimeSpent || 0) + input.timeSpent;

    const [updated] = await withDatabaseRetry(() =>
      db
        .update(quizAttempt)
        .set({ answers, totalTimeSpent })
        .where(eq(quizAttempt.id, attempt.id))
        .returning()
    ) as Array<any>;

    // Extract answer fields for the submitted question to return to UI
    const answerFields = extractAnswerFields(question);

    // Encrypt answer fields before sending to client
    const encryptedAnswerFields: Record<string, any> = {};
    for (const [key, value] of Object.entries(answerFields)) {
      encryptedAnswerFields[key] = await encryptAnswerData(value, { quizId: attempt.quizId });
    }

    return Response.json({
      success: true,
      data: {
        attempt: updated[0],
        questionAnswerData: {
          questionId: input.questionId,
          isCorrect,
          answerFields: encryptedAnswerFields
        }
      }
    });
  } catch (error) {
    // If it's a Response (like 401 from requireAuthSession), re-throw it
    if (error instanceof Response) {
      throw error;
    }

    console.error("Error submitting answer:", error);
    return Response.json({ success: false, error: "Failed to submit answer" }, { status: 500 });
  }
}

async function getQuizQuestions(quizId: string) {
  const rows = await withDatabaseRetry(() =>
    db.select().from(quiz).where(eq(quiz.id, quizId)).limit(1)
  ) as Array<any>;
  return rows[0]?.questions || [];
}

function findQuestionTypeSync(questions: any[], questionId: string): string {
  const q = questions.find((q: any) => q.id === questionId);
  return q?.type || 'unknown';
}

function validateAnswer(question: any, submittedAnswer: any): boolean {
  if (!question) {
    return false;
  }

  const { type } = question;

  switch (type) {
    case 'multipleChoice':
      // For multiple choice, correctAnswerIndex should match submitted answer
      return submittedAnswer === question.correctAnswerIndex;
    
    case 'trueFalse':
      // For true/false, correctAnswer should be boolean
      return submittedAnswer === question.correctAnswer;
    
    case 'fillInBlank':
      // For fill-in-blank, validate each blank answer
      if (!Array.isArray(submittedAnswer) || !Array.isArray(question.blanks)) {
        return false;
      }
      
      return question.blanks.every((blank: any, index: number) => {
        const userAnswer = submittedAnswer[index]?.trim() || '';
        
        if (blank.caseSensitive) {
          if (userAnswer === blank.correctAnswer) return true;
          if (blank.acceptableAnswers?.includes(userAnswer)) return true;
        } else {
          if (userAnswer.toLowerCase() === blank.correctAnswer.toLowerCase()) return true;
          if (blank.acceptableAnswers?.some((answer: string) => 
            answer.toLowerCase() === userAnswer.toLowerCase()
          )) return true;
        }
        
        return false;
      });
    
    case 'matching':
      // For matching questions, correctAnswer should be an object/array
      return JSON.stringify(submittedAnswer) === JSON.stringify(question.correctAnswer);
    
    default:
      return false;
  }
}

function findQuestionType(quizId: string, questionId: string): string {
  // Note: We cannot use await in current simple helper; for correctness we could refactor to fetch questions earlier.
  // As a compromise, return 'unknown'. Frontend and evaluator use full quiz on completion.
  return 'unknown';
}

function extractAnswerFields(question: any): Record<string, any> {
  const answerFields: Record<string, any> = {};
  
  switch (question.type) {
    case 'multipleChoice':
      if (question.correctAnswerIndex !== undefined) {
        answerFields.correctAnswerIndex = question.correctAnswerIndex;
      }
      if (question.explanation) {
        answerFields.explanation = question.explanation;
      }
      break;
      
    case 'trueFalse':
      if (question.correctAnswer !== undefined) {
        answerFields.correctAnswer = question.correctAnswer;
      }
      if (question.explanation) {
        answerFields.explanation = question.explanation;
      }
      break;
      
    case 'fillInBlank':
      if (question.blanks) {
        answerFields.blanks = question.blanks;
      }
      if (question.hint) {
        answerFields.hint = question.hint;
      }
      break;
      
    case 'matching':
      if (question.pairs) {
        answerFields.pairs = question.pairs;
      }
      break;
      
    case 'ordering':
      if (question.correctOrder) {
        answerFields.correctOrder = question.correctOrder;
      }
      break;
      
    case 'freeText':
      if (question.sampleAnswer) {
        answerFields.sampleAnswer = question.sampleAnswer;
      }
      if (question.evaluationCriteria) {
        answerFields.evaluationCriteria = question.evaluationCriteria;
      }
      break;
      
    case 'flashcard':
      // Flashcards don't need answer fields as they're self-assessed
      break;
      
    default:
      break;
  }
  
  return answerFields;
}

