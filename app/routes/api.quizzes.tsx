import { type LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { quiz, quizAttempt, learningContent, user } from "~/db/schema";
import { eq, and, or, desc, asc, ilike, sql, count, inArray } from "drizzle-orm";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { withDatabaseRetry } from "~/lib/db-retry.server";

// Validation schema for quiz filters
const quizzesFiltersSchema = z.object({
  search: z.string().optional(),
  difficulty: z.enum(["easy", "medium", "hard"]).optional(),
  completionStatus: z.enum(["all", "completed", "in-progress", "not-started"]).default("all"),
  learningContentId: z.string().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
  includePublic: z.boolean().default(true),
  grouped: z.boolean().default(false),
});

export type QuizzesFilters = z.infer<typeof quizzesFiltersSchema>;

// Type for question summary in listing view (without sensitive data)
export interface QuizQuestionSummary {
  points: number;
}

// Types for quiz with progress data
export interface QuizWithProgress {
  id: string;
  title: string;
  description: string | null;
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedDuration: number;
  totalPoints: number;
  questions: QuizQuestionSummary[];
  learningContentId: string;
  learningContent: {
    id: string;
    title: string;
    description: string;
    learningLevel: 'beginner' | 'intermediate' | 'advanced';
    contentType: 'standard' | 'kwaci-primer';
    tags: string[];
  };
  createdAt: Date;
  updatedAt: Date;
  // Progress data from attempts
  progress?: {
    status: 'not-started' | 'in-progress' | 'completed';
    completionPercentage: number;
    bestScore?: {
      percentage: number;
      earnedPoints: number;
      totalPoints: number;
    };
    latestScore?: {
      percentage: number;
      earnedPoints: number;
      totalPoints: number;
    };
    totalAttempts: number;
    lastAttemptAt?: Date;
    timeSpent: number; // total time spent across all attempts
  };
}

export interface GroupedQuizzes {
  learningContentId: string;
  learningContentTitle: string;
  learningContentDescription: string;
  quizzes: QuizWithProgress[];
}

export interface QuizzesResponse {
  success: boolean;
  quizzes: QuizWithProgress[];
  grouped?: GroupedQuizzes[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

// GET /api/quizzes - Get all quizzes with filtering and progress data
export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Handle authentication - if this throws a Response, let it bubble up
    const authSession = await requireAuthSession(request);
    const url = new URL(request.url);
    
    // Parse and validate query parameters
    const rawFilters = {
      search: url.searchParams.get("search") || undefined,
      difficulty: url.searchParams.get("difficulty") || undefined,
      completionStatus: url.searchParams.get("completionStatus") || "all",
      learningContentId: url.searchParams.get("learningContentId") || undefined,
      limit: parseInt(url.searchParams.get("limit") || "20"),
      offset: parseInt(url.searchParams.get("offset") || "0"),
      includePublic: url.searchParams.get("includePublic") === "true",
      grouped: url.searchParams.get("grouped") === "true",
    };

    const filters = quizzesFiltersSchema.parse(rawFilters);

    // Build base query with joins and filtered questions
    let baseQuery = db
      .select({
        quiz: {
          id: quiz.id,
          title: quiz.title,
          description: quiz.description,
          difficulty: quiz.difficulty,
          estimatedDuration: quiz.estimatedDuration,
          totalPoints: quiz.totalPoints,
          learningContentId: quiz.learningContentId,
          createdAt: quiz.createdAt,
          updatedAt: quiz.updatedAt,
          // Filter questions at query level to only include safe fields (PostgreSQL syntax)
          questions: sql`(
            SELECT json_agg(
              json_build_object(
                'points', (elem->>'points')::integer
              )
            )
            FROM json_array_elements(${quiz.questions}) AS elem
          )`.as('filtered_questions'),
        },
        learningContent: {
          id: learningContent.id,
          title: learningContent.title,
          description: learningContent.description,
          learningLevel: learningContent.learningLevel,
          contentType: learningContent.contentType,
          tags: learningContent.tags,
        },
      })
      .from(quiz)
      .innerJoin(learningContent, eq(quiz.learningContentId, learningContent.id));

    // Apply access control filters
    const accessConditions = [];
    if (filters.includePublic) {
      accessConditions.push(eq(learningContent.isPublic, true));
    }
    // Always include user's own content
    accessConditions.push(eq(learningContent.userId, authSession.user.id));

    if (accessConditions.length > 0) {
      baseQuery = baseQuery.where(or(...accessConditions));
    }

    // Apply additional filters
    const conditions = [];
    
    if (filters.search) {
      conditions.push(
        or(
          ilike(quiz.title, `%${filters.search}%`),
          ilike(quiz.description, `%${filters.search}%`),
          ilike(learningContent.title, `%${filters.search}%`)
        )
      );
    }

    if (filters.difficulty) {
      conditions.push(eq(quiz.difficulty, filters.difficulty));
    }

    if (filters.learningContentId) {
      conditions.push(eq(quiz.learningContentId, filters.learningContentId));
    }

    if (conditions.length > 0) {
      baseQuery = baseQuery.where(and(...conditions));
    }

    // Get total count for pagination
    const countQuery = db
      .select({ count: count() })
      .from(quiz)
      .innerJoin(learningContent, eq(quiz.learningContentId, learningContent.id));

    if (accessConditions.length > 0) {
      countQuery.where(or(...accessConditions));
    }
    if (conditions.length > 0) {
      countQuery.where(and(...conditions));
    }

    // Execute database queries with retry logic for cold start resilience
    const [totalResult, quizzesData] = await Promise.all([
      withDatabaseRetry(() => countQuery),
      withDatabaseRetry(() =>
        baseQuery
          .orderBy(desc(quiz.createdAt))
          .limit(filters.limit)
          .offset(filters.offset)
      )
    ]);

    const total = (totalResult as any)[0].count;

    // Get quiz attempts for progress calculation
    const quizIds = (quizzesData as any[]).map((item: { quiz: { id: string } }) => item.quiz.id);
    const attempts = quizIds.length > 0 ? await withDatabaseRetry(() =>
      db
        .select()
        .from(quizAttempt)
        .where(
          and(
            inArray(quizAttempt.quizId, quizIds),
            eq(quizAttempt.userId, authSession.user.id)
          )
        )
        .orderBy(desc(quizAttempt.startedAt))
    ) : [];

    // Process quizzes with progress data
    const quizzesWithProgress: QuizWithProgress[] = (quizzesData as any[]).map(({ quiz: quizData, learningContent: contentData }: { quiz: any; learningContent: any }) => {
      const quizAttempts = (attempts as any[]).filter((attempt: any) => attempt.quizId === quizData.id);
      
      // Calculate progress
      const progress = calculateQuizProgress(quizAttempts, quizData);
      
      // Apply completion status filter
      if (filters.completionStatus !== "all") {
        const statusMatches = progress.status === filters.completionStatus;
        if (!statusMatches) return null;
      }

      // Questions are already filtered at the query level
      const filteredQuestions = quizData.questions || [];

      return {
        id: quizData.id,
        title: quizData.title,
        description: quizData.description,
        difficulty: quizData.difficulty,
        estimatedDuration: quizData.estimatedDuration,
        totalPoints: quizData.totalPoints,
        questions: filteredQuestions,
        learningContentId: quizData.learningContentId,
        learningContent: contentData,
        createdAt: quizData.createdAt,
        updatedAt: quizData.updatedAt,
        progress,
      };
    }).filter(Boolean) as QuizWithProgress[];

    // Group by learning content if requested
    let grouped: GroupedQuizzes[] | undefined;
    if (filters.grouped) {
      const groupMap = new Map<string, GroupedQuizzes>();
      
      quizzesWithProgress.forEach(quiz => {
        const contentId = quiz.learningContentId;
        if (!groupMap.has(contentId)) {
          groupMap.set(contentId, {
            learningContentId: contentId,
            learningContentTitle: quiz.learningContent.title,
            learningContentDescription: quiz.learningContent.description,
            quizzes: [],
          });
        }
        groupMap.get(contentId)!.quizzes.push(quiz);
      });
      
      grouped = Array.from(groupMap.values());
    }

    const response: QuizzesResponse = {
      success: true,
      quizzes: quizzesWithProgress,
      grouped,
      pagination: {
        total,
        limit: filters.limit,
        offset: filters.offset,
        hasMore: filters.offset + filters.limit < total,
      },
    };

    return Response.json(response);

  } catch (error) {
    // If it's a Response (like 401 from requireAuthSession), re-throw it
    if (error instanceof Response) {
      throw error;
    }

    console.error("Error fetching quizzes:", error);
    return Response.json(
      { success: false, error: "Failed to fetch quizzes" },
      { status: 500 }
    );
  }
}

// Helper function to calculate quiz progress from attempts
function calculateQuizProgress(attempts: any[], quizData: any) {
  if (attempts.length === 0) {
    return {
      status: 'not-started' as const,
      completionPercentage: 0,
      totalAttempts: 0,
      timeSpent: 0,
    };
  }

  const completedAttempts = attempts.filter(attempt => attempt.isCompleted);
  const inProgressAttempts = attempts.filter(attempt => !attempt.isCompleted);
  
  let status: 'not-started' | 'in-progress' | 'completed';
  let completionPercentage = 0;
  
  // Determine status based on the most recent attempt
  // attempts are already ordered by startedAt desc, so attempts[0] is the most recent
  const mostRecentAttempt = attempts[0];
  
  if (mostRecentAttempt?.isCompleted) {
    status = 'completed';
    completionPercentage = 100;
  } else if (inProgressAttempts.length > 0) {
    status = 'in-progress';
    
    // Calculate actual progress for in-progress quizzes
    const latestInProgressAttempt = inProgressAttempts[0]; // Most recent incomplete attempt
    const totalQuestions = quizData.questions?.length || 0;
    const answeredQuestions = latestInProgressAttempt.answers?.length || 0;
    
    if (totalQuestions > 0) {
      completionPercentage = Math.round((answeredQuestions / totalQuestions) * 100);
    } else {
      completionPercentage = 0;
    }
  } else {
    status = 'not-started';
    completionPercentage = 0;
  }

  // Calculate best score from completed attempts
  let bestScore;
  if (completedAttempts.length > 0) {
    const bestAttempt = completedAttempts.reduce((best, current) => {
      const currentPercentage = current.score?.percentage || 0;
      const bestPercentage = best.score?.percentage || 0;
      return currentPercentage > bestPercentage ? current : best;
    });
    
    if (bestAttempt.score) {
      bestScore = {
        percentage: bestAttempt.score.percentage,
        earnedPoints: bestAttempt.score.earnedPoints,
        totalPoints: bestAttempt.score.totalPoints,
      };
    }
  }

  // Calculate latest score from the most recent completed attempt
  let latestScore;
  if (completedAttempts.length > 0) {
    // Find the most recent completed attempt (attempts are ordered by startedAt desc)
    const latestCompletedAttempt = completedAttempts.find(attempt => attempt.isCompleted && attempt.score);
    
    if (latestCompletedAttempt && latestCompletedAttempt.score) {
      latestScore = {
        percentage: latestCompletedAttempt.score.percentage,
        earnedPoints: latestCompletedAttempt.score.earnedPoints,
        totalPoints: latestCompletedAttempt.score.totalPoints,
      };
    }
  }

  // Calculate total time spent
  const totalTimeSpent = attempts.reduce((total, attempt) => {
    return total + (attempt.totalTimeSpent || 0);
  }, 0);

  return {
    status,
    completionPercentage,
    bestScore,
    latestScore,
    totalAttempts: attempts.length,
    lastAttemptAt: attempts[0]?.startedAt,
    timeSpent: totalTimeSpent,
  };
}
