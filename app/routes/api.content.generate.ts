/**
 * API route for generating learning content using AI
 */

import type { ActionFunctionArgs } from 'react-router';
import { z } from 'zod';
import { generateLearningContent, transformAIContentToDBFormat, generateKWACIPrimer, transformKWACIToDBFormat } from '~/lib/ai';
import { getSession } from '~/lib/auth/supabase-server';
import { log } from '~/lib/logger';

// Input validation schema
const generateContentSchema = z.object({
  topic: z.string().min(1, 'Topic is required'),
  learningLevel: z.enum(['beginner', 'intermediate', 'advanced']),
  preferredContentTypes: z.array(z.enum([
    'paragraph', 'infoBox', 'bulletList', 'numberedList',
    'grid', 'comparison', 'table', 'scatterPlot', 'keyValueGrid'
  ])).optional(),
  duration: z.number().min(1).max(120).optional(),
  additionalContext: z.string().optional(),
  contentType: z.enum(['standard', 'kwaci-primer']).optional(),
});

export async function action({ request }: ActionFunctionArgs) {
  const requestId = `api-gen-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const logger = log.withRequestId(requestId);
  logger.start(`API Content Generation started`, {
    method: request.method,
    url: request.url,
    timestamp: new Date().toISOString()
  });

  if (request.method !== 'POST') {
    logger.failure(`Invalid method: ${request.method}`);
    return Response.json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    logger.processing(`Checking authentication...`);
    await getSession(request); // Ensure user is authenticated
    logger.success(`Authentication successful`);
    
    logger.processing(`Parsing request body...`);
    const body = await request.json();
    logger.success(`Request body parsed`, {
      topic: body.topic,
      learningLevel: body.learningLevel,
      contentType: body.contentType,
      hasAdditionalContext: !!body.additionalContext,
      preferredContentTypesCount: body.preferredContentTypes?.length || 0,
      timestamp: new Date().toISOString()
    });

    // Validate input
    logger.validation(`Validating input schema...`);
    const input = generateContentSchema.parse(body);
    logger.success(`Input validation successful`, {
      topic: input.topic,
      learningLevel: input.learningLevel,
      contentType: input.contentType,
      timestamp: new Date().toISOString()
    });

    // Generate content using enhanced AI service
    logger.ai(`Starting AI content generation...`, {
      model: 'openai/gpt-4o-mini',
      retryOnFailure: true,
      contentType: input.contentType,
      timestamp: new Date().toISOString()
    });
    
    const aiStartTime = Date.now();
    let aiGeneratedContent: any;
    let dbSteps: any[];
    
    if (input.contentType === 'kwaci-primer') {
      // Generate KWACI Primer using specialized function
      logger.ai(`Generating KWACI Primer...`);
      const kwaciContent = await generateKWACIPrimer({
        topic: input.topic,
        learningLevel: input.learningLevel,
        focusAreas: input.additionalContext,
      }, {
        model: 'openai/gpt-4o-mini',
        retryOnFailure: true,
        env: process.env,
      });
      
      aiGeneratedContent = kwaciContent;
      
      // Transform KWACI content to database format
      logger.processing(`Transforming KWACI content to database format...`);
      dbSteps = transformKWACIToDBFormat(kwaciContent);
      
      logger.success(`KWACI transformation completed`, {
        dbStepsCount: dbSteps?.length || 0,
        timestamp: new Date().toISOString()
      });
    } else {
      // Generate standard learning content
      aiGeneratedContent = await generateLearningContent({
        topic: input.topic,
        learningLevel: input.learningLevel,
        preferredContentTypes: input.preferredContentTypes as any,
        focusAreas: input.additionalContext,
      }, {
        model: 'openai/gpt-4o-mini', // Use free model with structured output support
        retryOnFailure: true,
        env: process.env, // Pass environment variables for AI configuration
      });
      
      // Transform AI content to database format
      logger.processing(`Transforming AI content to database format...`);
      dbSteps = transformAIContentToDBFormat(aiGeneratedContent);
      
      logger.success(`Content transformation completed`, {
        dbStepsCount: dbSteps?.length || 0,
        timestamp: new Date().toISOString()
      });
    }
    
    const aiDuration = Date.now() - aiStartTime;
    
    logger.complete(`AI content generation completed`, {
      success: !!aiGeneratedContent,
      title: aiGeneratedContent?.title,
      hasSteps: !!aiGeneratedContent?.steps,
      stepCount: aiGeneratedContent?.steps?.length || 0,
      contentType: input.contentType,
      duration: `${aiDuration}ms`,
      timestamp: new Date().toISOString()
    });

    const totalDuration = Date.now() - (aiStartTime - aiDuration);
    logger.success(`API Content Generation successful!`, {
      aiDuration: `${aiDuration}ms`,
      totalDuration: `${totalDuration}ms`,
      finalStepCount: dbSteps?.length || 0,
      timestamp: new Date().toISOString()
    });

    return Response.json({
      success: true,
      data: {
        ...aiGeneratedContent,
        steps: dbSteps
      }
    });
    
  } catch (error) {
    logger.failure(`Content generation failed`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
      requestId
    });
    
    if (error instanceof z.ZodError) {
      logger.failure(`Schema validation error`, {
        errors: error.errors,
        timestamp: new Date().toISOString()
      });
      return Response.json({
        success: false,
        error: 'Invalid input',
        details: error.errors
      }, { status: 400 });
    }
    
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate content'
    }, { status: 500 });
  }
}