import type { ActionFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { quiz, quizAttempt } from "~/db/schema";
import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";

const schema = z.object({ quizId: z.string() });

// POST /api/quiz-attempts/start
export async function action({ request }: ActionFunctionArgs) {
  const auth = await requireAuthSession(request);
  
  const body = await request.json();
  const input = schema.parse(body);

  // Verify quiz exists and access
  const quizRows = await db.select().from(quiz).where(eq(quiz.id, input.quizId)).limit(1);
  if (quizRows.length === 0) {
    return Response.json({ success: false, error: "Quiz not found" }, { status: 404 });
  }
  const q = quizRows[0];

  // If retakes not allowed, ensure no prior attempts
  if (q.allowRetakes === false) {
    const prior = await db
      .select({ id: quizAttempt.id })
      .from(quizAttempt)
      .where(eq(quizAttempt.quizId, input.quizId)).where(eq(quizAttempt.userId, auth.user.id))
      .limit(1);
    if (prior.length > 0) {
      return Response.json({ success: false, error: "Retakes are not allowed for this quiz" }, { status: 403 });
    }
  }

  // Create attempt with shuffled question order if quiz.shuffleQuestions
  const questionOrder = Array.isArray(q.questions) ? q.questions.map((qq: any) => qq.id) : [];
  if (q.shuffleQuestions && questionOrder.length > 1) {
    for (let i = questionOrder.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [questionOrder[i], questionOrder[j]] = [questionOrder[j], questionOrder[i]];
    }
  }

  const attemptData = {
    id: nanoid(),
    quizId: q.id,
    userId: auth.user.id,
    startedAt: new Date(),
    isCompleted: false,
    answers: [],
    metadata: {
      questionOrder,
      pausedDurations: [],
    },
  };
  
  const [attempt] = await db
    .insert(quizAttempt)
    .values(attemptData)
    .returning();

  return Response.json({ success: true, data: attempt }, { status: 201 });
}

