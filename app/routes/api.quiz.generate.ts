/**
 * API route for generating quizzes from learning content using AI
 */

import type { ActionFunctionArgs } from 'react-router';
import { z } from 'zod';
import { db } from '~/db';
import { requireAuthSession } from '~/lib/auth/supabase-server';
import { getLearningContentById } from '~/db/services/learning-content';
import { quiz } from '~/db/schema/quiz';
import { generateQuizFromContent } from '~/lib/ai/services/quiz-generator';
import { nanoid } from 'nanoid';
import type { QuizGenerationConfig } from '~/components/quiz/types';

// Input validation schema for quiz generation
const generateQuizSchema = z.object({
  learningContentId: z.string().min(1, 'Learning content ID is required'),
  quizTypes: z.array(z.enum([
    'flashcard', 'multipleChoice', 'trueFalse', 'fillInBlank',
    'matching', 'freeText', 'ordering'
  ])).min(1, 'At least one quiz type is required'),
  difficulty: z.enum(['easy', 'medium', 'hard']),
  questionsPerType: z.number().min(1).max(10),
  includeHints: z.boolean().default(true),
  includeExplanations: z.boolean().default(true),
  shuffleQuestions: z.boolean().default(false),
  timeLimit: z.number().min(1).max(300).optional(), // in minutes
});

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== 'POST') {
    return Response.json({ error: 'Method not allowed' }, { status: 405 });
  }

  try {
    const authSession = await requireAuthSession(request);
    const body = await request.json();

    // Validate input
    const config = generateQuizSchema.parse(body);

    console.info('Quiz generation request:', {
      userId: authSession.user.id,
      learningContentId: config.learningContentId,
      quizTypes: config.quizTypes,
      difficulty: config.difficulty,
      questionsPerType: config.questionsPerType
    });

    // Get the learning content (pass userId for access control)
    console.info('Fetching learning content:', { contentId: config.learningContentId });
    const learningContent = await getLearningContentById(db, config.learningContentId, authSession.user.id);
    console.info('Learning content result:', { 
      found: !!learningContent,
      userId: learningContent?.userId,
      isPublic: learningContent?.isPublic,
      title: learningContent?.title
    });
    
    if (!learningContent) {
      console.error('Learning content not found for ID:', config.learningContentId);
      return Response.json({
        success: false,
        error: 'Learning content not found'
      }, { status: 404 });
    }

    // Access control is already handled by getLearningContentById
    console.info('Quiz generation access granted for content:', {
      userId: authSession.user.id,
      contentUserId: learningContent.userId,
      isCreator: learningContent.userId === authSession.user.id,
      isPublic: learningContent.isPublic,
      contentId: config.learningContentId
    });

    // Generate quiz using AI service
    const generatedQuiz = await generateQuizFromContent(
      learningContent,
      config as QuizGenerationConfig,
      {
        preferCostEffective: true,
        retryOnFailure: true,
        env: process.env, // Pass environment variables for AI configuration
      }
    );

    // Log AI-generated quiz details for debugging feedback fields
    console.info('AI-generated quiz details:', {
      questionsCount: generatedQuiz.questions.length,
      title: generatedQuiz.title,
      difficulty: generatedQuiz.difficulty
    });

    // Log each question with feedback and hint fields for debugging
    generatedQuiz.questions.forEach((question, index) => {
      console.info(`Question ${index + 1} (${question.type}):`, {
        id: question.id,
        type: question.type,
        hasCorrectFeedback: !!question.correctFeedback,
        hasIncorrectFeedback: !!question.incorrectFeedback,
        hasHint: !!question.hint,
        correctFeedback: question.correctFeedback,
        incorrectFeedback: question.incorrectFeedback,
        hint: question.hint,
        sourceStepId: question.sourceStepId
      });
    });

    // Save generated quiz to database
    const quizData = {
      id: nanoid(),
      title: generatedQuiz.title,
      description: generatedQuiz.description,
      learningContentId: config.learningContentId,
      difficulty: generatedQuiz.difficulty,
      estimatedDuration: generatedQuiz.estimatedDuration,
      totalPoints: generatedQuiz.totalPoints,
      questions: generatedQuiz.questions,
      metadata: generatedQuiz.metadata,
      isPublic: generatedQuiz.isPublic,
      allowRetakes: generatedQuiz.allowRetakes,
      showCorrectAnswers: generatedQuiz.showCorrectAnswers,
      shuffleQuestions: generatedQuiz.shuffleQuestions,
      timeLimit: generatedQuiz.timeLimit,
      createdBy: authSession.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const [newQuiz] = await db.insert(quiz).values(quizData).returning();

    // Log what was actually saved to the database
    console.info('Quiz saved to database:', {
      quizId: newQuiz.id,
      questionsGenerated: generatedQuiz.questions.length,
      estimatedDuration: generatedQuiz.estimatedDuration,
      totalPoints: generatedQuiz.totalPoints
    });

    // Log the questions that were saved to verify feedback and hint fields
    console.info('Questions saved to database:', {
      questionsWithCorrectFeedback: quizData.questions.filter(q => q.correctFeedback).length,
      questionsWithIncorrectFeedback: quizData.questions.filter(q => q.incorrectFeedback).length,
      questionsWithHints: quizData.questions.filter(q => q.hint).length,
      totalQuestions: quizData.questions.length
    });

    // Log detailed hint information for each question
    console.info('Hint details for saved questions:');
    quizData.questions.forEach((question, index) => {
      console.info(`Question ${index + 1} hint:`, {
        id: question.id,
        type: question.type,
        hasHint: !!question.hint,
        hint: question.hint || 'No hint'
      });
    });

    console.info('Quiz generation completed:', {
      quizId: newQuiz.id,
      questionsGenerated: generatedQuiz.questions.length,
      estimatedDuration: generatedQuiz.estimatedDuration,
      totalPoints: generatedQuiz.totalPoints
    });

    return Response.json({
      success: true,
      data: {
        quizId: newQuiz.id,
        message: `Quiz generated successfully with ${generatedQuiz.questions.length} questions`
      }
    }, { status: 201 });
    
  } catch (error) {
    console.error('Quiz generation failed:', error);
    
    if (error instanceof z.ZodError) {
      return Response.json({
        success: false,
        error: 'Invalid input',
        details: error.errors
      }, { status: 400 });
    }
    
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to generate quiz'
    }, { status: 500 });
  }
}