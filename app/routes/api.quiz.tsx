import { type ActionFunctionArgs, type LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { quiz, quizAttempt } from "~/db/schema/quiz";
import { eq, and, desc } from "drizzle-orm";
import { nanoid } from "nanoid";

// Validation schemas
const createQuizSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  learningContentId: z.string().min(1, "Learning content ID is required"),
  difficulty: z.enum(["easy", "medium", "hard"]),
  estimatedDuration: z.number().min(1).max(300), // 1-300 minutes
  totalPoints: z.number().min(1),
  questions: z.array(z.object({
    id: z.string(),
    type: z.enum(["flashcard", "multipleChoice", "trueFalse", "fillInBlank", "matching", "freeText", "ordering"]),
    difficulty: z.enum(["easy", "medium", "hard"]),
    sourceStepId: z.string(),
    sourceContent: z.string(),
    points: z.number().min(1),
  })).min(1, "At least one question is required"),
  metadata: z.object({
    generatedAt: z.string(),
    aiModel: z.string(),
    sourceStepsUsed: z.array(z.string()),
    difficultyDistribution: z.record(z.number()),
    typeDistribution: z.record(z.number()),
    validationPassed: z.boolean().optional(),
    generationOptions: z.record(z.any()).optional(),
  }).optional(),
  isPublic: z.boolean().default(false),
  allowRetakes: z.boolean().default(true),
  showCorrectAnswers: z.boolean().default(true),
  shuffleQuestions: z.boolean().default(false),
  timeLimit: z.number().min(1).max(300).optional(), // in minutes
});

const listQuizzesSchema = z.object({
  learningContentId: z.string().optional(),
  difficulty: z.enum(["easy", "medium", "hard"]).optional(),
  isPublic: z.boolean().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

// GET /api/quiz - List quizzes for user
export async function loader({ request }: LoaderFunctionArgs) {
  const authSession = await requireAuthSession(request);
  const url = new URL(request.url);
  
  // Parse query parameters
  const queryParams = Object.fromEntries(url.searchParams);
  const searchParams = listQuizzesSchema.parse({
    ...queryParams,
    limit: queryParams.limit ? parseInt(queryParams.limit) : 20,
    offset: queryParams.offset ? parseInt(queryParams.offset) : 0,
    isPublic: queryParams.isPublic ? queryParams.isPublic === 'true' : undefined,
  });

  try {
    const conditions = [];
    
    if (searchParams.learningContentId) {
      conditions.push(eq(quiz.learningContentId, searchParams.learningContentId));
    }
    
    if (searchParams.difficulty) {
      conditions.push(eq(quiz.difficulty, searchParams.difficulty));
    }
    
    if (searchParams.isPublic !== undefined) {
      conditions.push(eq(quiz.isPublic, searchParams.isPublic));
    }

    const quizzes = await db
      .select()
      .from(quiz)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(desc(quiz.createdAt))
      .limit(searchParams.limit)
      .offset(searchParams.offset);

    return Response.json({ success: true, data: quizzes });
  } catch (error) {
    console.error("Error fetching quizzes:", error);
    return Response.json(
      { success: false, error: "Failed to fetch quizzes" },
      { status: 500 }
    );
  }
}

// POST /api/quiz - Create new quiz
export async function action({ request }: ActionFunctionArgs) {
  const authSession = await requireAuthSession(request);
  
  if (request.method !== "POST") {
    return Response.json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const input = createQuizSchema.parse(body);

    const newQuiz = await db.insert(quiz).values({
      id: nanoid(),
      ...input,
      authorId: authSession.user.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();

    return Response.json({ success: true, data: newQuiz[0] }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }
    
    console.error("Error creating quiz:", error);
    return Response.json(
      { success: false, error: "Failed to create quiz" },
      { status: 500 }
    );
  }
}