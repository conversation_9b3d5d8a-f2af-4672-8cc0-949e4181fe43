import type { LoaderFunctionArgs, MetaFunction } from "react-router";
import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Progress } from "~/components/ui/progress";
import { Badge } from "~/components/ui/badge";
import { 
  TrendingUp, 
  Clock, 
  BookOpen, 
  Target, 
  Calendar,
  Activity,
  Award,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Brain
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
// Server-only modules are imported dynamically inside the loader to avoid bundling into client
import { useLeaderboardInfinite, useContributions } from "~/lib/hooks/use-leaderboard-api";
import { useAuth } from "~/lib/auth/auth-provider";

import { Leaderboard } from "~/components/progress/leaderboard";
import { LearningActivityCalendar } from "~/components/progress/learning-activity-calendar";

export const meta: MetaFunction = () => {
  return [
    { title: "Learning Progress - Kwaci Learning" },
    { name: "description", content: "Track your learning progress and analytics" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Server-side authentication validation
  const { requireAuth } = await import('~/lib/auth/supabase-server');
  const authContext = await requireAuth(request);
  
  return {
    user: {
      id: authContext.user!.id,
      email: authContext.user!.email,
    },
  };
}

export default function ProgressPage() {
  // Client-side data fetching using React Query
  const leaderboardQuery = useLeaderboardInfinite(5);
  const contributionsQuery = useContributions();

  // Debug auth state
  const { isAuthenticated, isLoading: authLoading, user } = useAuth();
  console.log('[PROGRESS PAGE] Auth state:', {
    isAuthenticated,
    authLoading,
    hasUser: !!user,
    userId: user?.id,
  });

  // Debug logging for query states
  console.log('[PROGRESS PAGE] Leaderboard query:', {
    isLoading: leaderboardQuery.isLoading,
    isError: leaderboardQuery.isError,
    error: leaderboardQuery.error,
    hasData: !!leaderboardQuery.data,
    pagesCount: leaderboardQuery.data?.pages?.length,
  });

  console.log('[PROGRESS PAGE] Contributions query:', {
    isLoading: contributionsQuery.isLoading,
    isError: contributionsQuery.isError,
    error: contributionsQuery.error,
    hasData: !!contributionsQuery.data,
    data: contributionsQuery.data,
    isEnabled: isAuthenticated && !authLoading,
  });

  // Flatten leaderboard data from pages
  const allLeaderboardUsers = leaderboardQuery.data?.pages.flatMap((page: any) => {
    // Handle both possible response structures
    if (page?.success && page?.data?.leaderboard) {
      return page.data.leaderboard;
    } else if (page?.leaderboard) {
      return page.leaderboard;
    } else {
      console.warn('[PROGRESS PAGE] Unexpected page structure:', page);
      return [];
    }
  }) || [];

  // Debug the data transformation
  if (leaderboardQuery.data?.pages && leaderboardQuery.data.pages.length > 0) {
    console.log('[PROGRESS PAGE] DETAILED Data transformation:', {
      hasQueryData: !!leaderboardQuery.data,
      pagesCount: leaderboardQuery.data.pages.length,
      firstPageRaw: JSON.stringify(leaderboardQuery.data.pages[0], null, 2),
      allLeaderboardUsersLength: allLeaderboardUsers.length,
      allLeaderboardUsersRaw: JSON.stringify(allLeaderboardUsers, null, 2),
    });
  } else {
    console.log('[PROGRESS PAGE] Data transformation (no pages):', {
      hasQueryData: !!leaderboardQuery.data,
      pagesCount: leaderboardQuery.data?.pages?.length,
      queryData: leaderboardQuery.data,
    });
  }

  // Get current user rank and data
  console.log('[PROGRESS PAGE] Current user data extraction:', {
    hasPages: !!leaderboardQuery.data?.pages?.length,
    firstPage: leaderboardQuery.data?.pages[0],
    currentUserFromFirstPage: (leaderboardQuery.data?.pages[0] as any)?.currentUser,
  });

  const currentUserRank = (leaderboardQuery.data?.pages[0] as any)?.currentUser?.rank || 0;
  const currentUserData = (leaderboardQuery.data?.pages[0] as any)?.currentUser?.data || null;

  // Get the last updated timestamp from leaderboard data
  const lastUpdated = leaderboardQuery.data?.pages[0]?.success && allLeaderboardUsers.length > 0
    ? allLeaderboardUsers[0].lastUpdated
    : null;

  // Get total count of users from pagination
  const totalUsers = leaderboardQuery.data?.pages[0]?.success 
    ? leaderboardQuery.data.pages[0].data.pagination.total 
    : 0;

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Learning Progress</h1>
            <p className="text-muted-foreground">
              Track your learning journey with leaderboards and activity
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Activity className="h-5 w-5 text-blue-500" />
            <span className="text-sm text-muted-foreground">
              Last updated: {lastUpdated 
                ? formatDistanceToNow(new Date(lastUpdated), { addSuffix: true })
                : formatDistanceToNow(new Date(), { addSuffix: true })
              }
            </span>
          </div>
        </div>

                {/* Action Buttons */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium mb-2">Ready to Learn More?</h3>
                <p className="text-muted-foreground">
                  Continue your learning journey with personalized content
                </p>
              </div>
              <div className="space-x-4">
                <Button variant="outline" asChild>
                  <a href="/dashboard/my-learning">View My Content</a>
                </Button>
                <Button asChild>
                  <a href="/dashboard/learn">Create New Content</a>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* New Features: Leaderboard and Contribution Heatmap */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Leaderboard */}
          <Leaderboard
            users={allLeaderboardUsers}
            currentUserRank={currentUserRank}
            currentUserData={currentUserData}
            totalUsers={totalUsers}
            isLoading={leaderboardQuery.isLoading}
            hasNextPage={leaderboardQuery.hasNextPage}
            isFetchingNextPage={leaderboardQuery.isFetchingNextPage}
            onLoadMore={() => leaderboardQuery.fetchNextPage()}
          />

          {/* Learning Activity Calendar */}
          {contributionsQuery.data?.data?.contributions && (
            <LearningActivityCalendar
              contributions={contributionsQuery.data.data.contributions}
              stats={contributionsQuery.data.data.stats}
            />
          )}

          {/* Loading state for learning activity */}
          {contributionsQuery.isLoading && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BookOpen className="h-5 w-5" />
                  <span>Learning Activity</span>
                </CardTitle>
                <CardDescription>
                  Your learning journey throughout the year in calendar view
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-48 flex items-center justify-center text-muted-foreground">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Error states for debugging */}
          {contributionsQuery.isError && (
            <Card>
              <CardHeader>
                <CardTitle className="text-red-600">Contributions Error</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-sm bg-red-50 p-4 rounded">
                  {JSON.stringify(contributionsQuery.error, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}

          {leaderboardQuery.isError && (
            <Card>
              <CardHeader>
                <CardTitle className="text-red-600">Leaderboard Error</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-sm bg-red-50 p-4 rounded">
                  {JSON.stringify(leaderboardQuery.error, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}