import type { LoaderFunctionArgs, MetaFunction } from "react-router";
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from "react-router";
import { useState } from "react";

import { useGetLearningContent } from "~/lib/hooks/use-learning-api";
import type { LearningContent } from "~/db/schema/learning-content";
import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { EnhancedLearningContentDisplay } from "~/components/learn/EnhancedLearningContentDisplay";
import { Button } from "~/components/ui/button";
import { QuizSelectionModal } from "~/components/quiz/QuizSelectionModal";
import { useGenerateQuiz } from "~/lib/hooks/use-quiz-api";
import { ArrowLeft, Zap, PanelRight, Loader2, AlertCircle } from "lucide-react";
import type { QuizGenerationConfig } from "~/components/quiz/types";
import { useUIStore } from "~/lib/stores/ui-store";
import { useBreadcrumbTitle } from "~/lib/hooks/use-breadcrumbs";
import { Alert, AlertDescription } from "~/components/ui/alert";
import { ContentSkeleton } from "~/components/shared/ContentSkeleton";

export const meta: MetaFunction = () => {
  return [
    { title: "Learning Content - Kwaci Learning" },
    { name: "description", content: "View your learning content" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Server-side authentication validation
  const { requireAuth } = await import('~/lib/auth/supabase-server');
  const authContext = await requireAuth(request);
  
  return {
    user: {
      id: authContext.user!.id,
      email: authContext.user!.email,
    },
  };
}

export default function LearnContentPage() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [isQuizModalOpen, setIsQuizModalOpen] = useState(false);

  // UI store hooks
  const { rightSidebarOpen, toggleRightSidebar } = useUIStore();

  // Client-side data fetching using React Query (same pattern as working route)
  const {
    data: content,
    isLoading,
    isError,
    error,
    refetch
  } = useGetLearningContent(id!);

  // Set dynamic breadcrumb title
  useBreadcrumbTitle(content?.title || "Learning Content");

  // Quiz generation hooks
  const generateQuizMutation = useGenerateQuiz();

  // Loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="py-8 px-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Loading learning content...</span>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Error state
  if (isError || !content) {
    return (
      <DashboardLayout>
        <div className="py-8 px-6">
          <Alert className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>
                {error instanceof Error
                  ? error.message
                  : "Failed to load learning content. Please try again."
                }
              </span>
              <Button variant="outline" size="sm" onClick={() => refetch()}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
          <div className="text-center">
            <Link to="/dashboard/my-learning">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to My Learning
              </Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Transform database steps to component-expected format
  const transformedSteps = content.steps.map(step => {
    // If step has multiple blocks, we need to handle them differently
    if (step.blocks.length === 1) {
      const block = step.blocks[0];
      // Ensure the type is one of the valid StepConfig types
      const validTypes = ['paragraph', 'infoBox', 'bulletList', 'numberedList', 'grid', 'comparison', 'table', 'scatterPlot', 'keyValueGrid'] as const;
      const blockType = validTypes.includes(block.type as any) ? block.type as typeof validTypes[number] : 'paragraph';
      
      return {
        title: step.title,
        icon: step.icon || '📚',
        type: blockType,
        data: block.data
      };
    } else {
      // Multiple blocks - for now, default to paragraph with combined text
      return {
        title: step.title,
        icon: step.icon || '📚',
        type: 'paragraph' as const,
        data: step.blocks
          .map(block => typeof block.data === 'string' ? block.data : JSON.stringify(block.data))
          .join('\n')
      };
    }
  });

  const handleStepChange = (step: number) => {
    // TODO: Implement progress tracking
    console.log('Step changed to:', step);
  };

  const handleProgressUpdate = (progress: number) => {
    // TODO: Implement progress update
    console.log('Progress updated to:', progress);
  };

  const handleQuizGenerate = () => {
    setIsQuizModalOpen(true);
  };

  const handleQuizGenerateSubmit = async (config: QuizGenerationConfig) => {
    try {
      const result = await generateQuizMutation.mutateAsync(config);
      
      // API client unwraps the response, so result contains {quizId, message} directly
      if (result.quizId) {
        // Navigate to the generated quiz
        navigate(`/dashboard/quiz/${result.quizId}`);
      } else {
        console.error('Quiz generation failed - no quizId found:', result);
      }
    } catch (error) {
      console.error('Quiz generation error:', error);
    } finally {
      setIsQuizModalOpen(false);
    }
  };

  return (
    <DashboardLayout>

      {/* Enhanced Learning Content Display */}
      <EnhancedLearningContentDisplay
        contentId={content.id}
        title={content.title}
        description={content.description}
        steps={transformedSteps}
        learningLevel={content.learningLevel}
        estimatedReadingTime={content.estimatedReadingTime}
        isPublic={content.isPublic}
        contentType={content.contentType}
        initialStep={0}
        progress={0}
        onStepChange={handleStepChange}
        onProgressUpdate={handleProgressUpdate}
      />

      {/* Quiz Selection Modal */}
      <QuizSelectionModal
        isOpen={isQuizModalOpen}
        onClose={() => setIsQuizModalOpen(false)}
        onGenerate={handleQuizGenerateSubmit}
        learningContentId={content.id}
        isGenerating={generateQuizMutation.isPending}
      />
    </DashboardLayout>
  );
}
