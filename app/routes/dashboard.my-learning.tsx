import type { LoaderFunctionArgs, MetaFunction } from "react-router";
import { useState, useMemo, useEffect, useCallback, useRef } from "react";
import { Link } from "react-router";
import {
  Plus,
  BookOpen,
  AlertCircle,
  RefreshCw,
  Loader2
} from "lucide-react";

import { DashboardLayout } from "~/components/layout/dashboard-layout";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardTitle } from "~/components/ui/card";
import { Alert, AlertDescription } from "~/components/ui/alert";
import {
  LearningContentCard,
  AdvancedSearchFilters,
  ShareContentModal
} from "~/components/my-learning";
import { StickyFooter } from "~/components/shared/StickyFooter";
import { ContentSkeleton } from "~/components/shared/ContentSkeleton";
import { ViewControls } from "~/components/shared/ViewControls";
import {
  useGetMyLearningContent, 
  useDeleteMyLearningContent, 
  useToggleContentPublic 
} from "~/lib/hooks/use-learning-api";
import type { MyLearningFilters, LearningContentWithProgress } from "~/db/services/learning-content";

export const meta: MetaFunction = () => {
  return [
    { title: "My Learning - Kwaci Learning" },
    { name: "description", content: "Manage and view your learning content" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Server-side authentication validation
  const { requireAuth } = await import('~/lib/auth/supabase-server');
  const authContext = await requireAuth(request);
  
  return {
    user: {
      id: authContext.user!.id,
      email: authContext.user!.email,
    },
  };
}

export default function MyLearningPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filters, setFilters] = useState<Omit<MyLearningFilters, 'offset'>>({
    limit: 12,
  });
  const [shareContent, setShareContent] = useState<LearningContentWithProgress | null>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  // Memoize filters to prevent infinite re-renders
  const memoizedFilters = useMemo(() => filters, [
    filters.limit, 
    filters.search, 
    filters.sortBy, 
    filters.sortOrder, 
    filters.contentType,
    filters.learningLevel,
    filters.completionStatus,
    filters.isPublic,
    filters.tags,
    filters.dateRange,
    filters.readingTimeRange
  ]);

  // React Query infinite hooks
  const { 
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
    refetch
  } = useGetMyLearningContent(memoizedFilters);

  const deleteContentMutation = useDeleteMyLearningContent();
  const togglePublicMutation = useToggleContentPublic();

  const handleFiltersChange = (newFilters: Omit<MyLearningFilters, 'offset'>) => {
    setFilters(newFilters);
  };

  const handleDelete = async (contentId: string) => {
    if (window.confirm('Are you sure you want to delete this learning content? This action cannot be undone.')) {
      try {
        await deleteContentMutation.mutateAsync(contentId);
      } catch (error) {
        console.error('Failed to delete content:', error);
      }
    }
  };

  const handleTogglePublic = async (contentId: string) => {
    try {
      await togglePublicMutation.mutateAsync(contentId);
    } catch (error) {
      console.error('Failed to toggle content visibility:', error);
    }
  };

  const handleShare = (content: LearningContentWithProgress) => {
    setShareContent(content);
  };

  // Flatten all pages into single array
  const content = useMemo((): LearningContentWithProgress[] => {
    if (!(data as any)?.pages) return [];
    return (data as any).pages.flatMap((page: { success: boolean; content: LearningContentWithProgress[]; pagination: { limit: number; offset: number; hasMore: boolean } }) => page.content);
  }, [data]);

  // Setup infinite scroll observer
  useEffect(() => {
    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasNextPage && !isFetchingNextPage && !isLoading) {
          fetchNextPage();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current && hasNextPage) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [fetchNextPage, hasNextPage, isFetchingNextPage, isLoading]);

  return (
    <DashboardLayout>
      <div className="relative py-8 px-6 pb-16">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              My Learning
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Manage and track your learning content
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <Link to="/dashboard/learn">
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Create New
              </Button>
            </Link>
          </div>
        </div>



        {/* Search and Filters */}
        <div className="mb-6">
          <AdvancedSearchFilters
            filters={filters}
            onFiltersChange={handleFiltersChange}
            className=""
          />
        </div>

        {/* Main Content */}
        <div>
            {/* View Controls */}
            <div className="flex items-center justify-between mb-6">
              <ViewControls
                viewMode={viewMode}
                onViewModeChange={setViewMode}
              />
              <div className="flex items-center gap-2" />
            </div>

            {/* Error State */}
            {isError && (
              <Alert className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription className="flex items-center justify-between">
                  <span>Failed to load learning content. Please try again.</span>
                  <Button variant="outline" size="sm" onClick={() => refetch()}>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Retry
                  </Button>
                </AlertDescription>
              </Alert>
            )}

            {/* Loading State */}
            {isLoading && (
              <ContentSkeleton
                viewMode={viewMode}
                variant="card"
                count={6}
                gridClasses="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"
              />
            )}

            {/* Empty State */}
            {!isLoading && !isError && content.length === 0 && (
              <Card className="text-center py-12">
                <CardContent>
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <CardTitle className="mb-2">No learning content found</CardTitle>
                  <CardDescription className="mb-4">
                    {filters.search || filters.learningLevel || filters.contentType
                      ? "Try adjusting your filters or search terms."
                      : "Get started by creating your first learning content."
                    }
                  </CardDescription>
                  <div className="flex items-center justify-center gap-2">
                    {(filters.search || filters.learningLevel || filters.contentType) && (
                      <Button
                        variant="outline"
                        onClick={() => setFilters({ limit: 12 })}
                      >
                        Clear Filters
                      </Button>
                    )}
                    <Link to="/dashboard/learn">
                      <Button>
                        <Plus className="h-4 w-4 mr-2" />
                        Create Content
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Content Grid/List */}
            {!isLoading && content.length > 0 && (
              <>
                <div className={
                  viewMode === 'grid'
                    ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"
                    : "space-y-4"
                }>
                  {content.map((item) => (
                    <LearningContentCard
                      key={item.id}
                      content={item}
                      viewMode={viewMode}
                      onDelete={handleDelete}
                      onShare={handleShare}
                      onTogglePublic={handleTogglePublic}
                    />
                  ))}
                </div>

                {/* Infinite Scroll Trigger */}
                {hasNextPage && (
                  <div ref={loadMoreRef} className="text-center mt-8 py-4">
                    {isFetchingNextPage && (
                      <div className="flex items-center justify-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>Loading more content...</span>
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
        </div>

        {/* Share Modal */}
        <ShareContentModal
          content={shareContent}
          isOpen={!!shareContent}
          onClose={() => setShareContent(null)}
          onTogglePublic={handleTogglePublic}
        />

        {/* Sticky Footer with Stats */}
        {!isLoading && content.length > 0 && (
          <StickyFooter
            offsetClassName="md:left-64"
            items={[
              {
                key: 'total',
                content: (
                  <div className="flex items-center gap-1.5">
                    <BookOpen className="h-3 w-3" />
                    <span>
                      Total: <span className="font-medium text-gray-700 dark:text-gray-300">{content.length}</span>
                    </span>
                  </div>
                )
              },
              {
                key: 'completed',
                content: (
                  <div className="flex items-center gap-1.5">
                    <div className="h-2 w-2 bg-green-500 rounded-full" />
                    <span>
                      Completed: <span className="font-medium text-gray-700 dark:text-gray-300">{content.filter((c: LearningContentWithProgress) => c.progress?.isCompleted).length}</span>
                    </span>
                  </div>
                )
              },
              {
                key: 'in-progress',
                content: (
                  <div className="flex items-center gap-1.5">
                    <div className="h-2 w-2 bg-blue-500 rounded-full" />
                    <span>
                      In Progress: <span className="font-medium text-gray-700 dark:text-gray-300">{content.filter((c: LearningContentWithProgress) => c.progress && !c.progress.isCompleted).length}</span>
                    </span>
                  </div>
                )
              },
              {
                key: 'not-started',
                content: (
                  <div className="flex items-center gap-1.5">
                    <div className="h-2 w-2 bg-gray-400 rounded-full" />
                    <span>
                      Not Started: <span className="font-medium text-gray-700 dark:text-gray-300">{content.filter((c: LearningContentWithProgress) => !c.progress).length}</span>
                    </span>
                  </div>
                )
              },
              {
                key: 'public',
                content: (
                  <div className="flex items-center gap-1.5">
                    <div className="h-2 w-2 bg-purple-500 rounded-full" />
                    <span>
                      Public: <span className="font-medium text-gray-700 dark:text-gray-300">{content.filter((c: LearningContentWithProgress) => c.isPublic).length}</span>
                    </span>
                  </div>
                )
              },
              {
                key: 'private',
                content: (
                  <div className="flex items-center gap-1.5">
                    <div className="h-2 w-2 bg-orange-500 rounded-full" />
                    <span>
                      Private: <span className="font-medium text-gray-700 dark:text-gray-300">{content.filter((c: LearningContentWithProgress) => !c.isPublic).length}</span>
                    </span>
                  </div>
                )
              },
            ]}
          />
        )}
      </div>
    </DashboardLayout>
  );
}
