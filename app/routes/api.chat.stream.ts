/**
 * Streaming Chat API route for real-time RAG-powered conversations
 * 
 * This route provides server-sent events for streaming chat responses,
 * enabling real-time AI conversation experiences.
 */

import type { LoaderFunctionArgs } from 'react-router';
import { createRagChatService } from '~/lib/rag/services/rag-chat-service';
import { db } from '~/db/connection';
import { requireAuthSession } from '~/lib/auth/supabase-server';
import { getAuthenticatedUser } from '~/lib/auth/middleware';
import { log } from '~/lib/logger';
import { z } from 'zod';

// Request validation schema
const StreamChatRequestSchema = z.object({
  message: z.string().min(1, 'Message cannot be empty').max(2000, 'Message too long'),
  conversationId: z.string().optional(),
  learningContentId: z.string().uuid('Invalid learning content ID'),
});

/**
 * Handle streaming chat requests
 */
export async function loader({ request, context }: LoaderFunctionArgs) {
  const requestId = `stream-${Date.now()}-${Math.random().toString(36).substring(7)}`;
  
  try {
    // Authentication check
    const authSession = await requireAuthSession(request);
    const user = await getAuthenticatedUser(authSession);
    if (!user) {
      return new Response(
        JSON.stringify({ success: false, error: 'Authentication required' }),
        { 
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Parse query parameters for streaming request
    const url = new URL(request.url);
    const message = url.searchParams.get('message');
    const conversationId = url.searchParams.get('conversationId') || undefined;
    const learningContentId = url.searchParams.get('learningContentId');

    // Validate required parameters
    const validation = StreamChatRequestSchema.safeParse({
      message,
      conversationId,
      learningContentId,
    });

    if (!validation.success) {
      return new Response(
        JSON.stringify({ 
          success: false, 
          error: 'Invalid request parameters',
          details: validation.error.issues.map(issue => issue.message)
        }),
        { 
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    const validatedData = validation.data;

    log.info(`[${requestId}] Streaming chat request received`, {
      userId: user.id,
      learningContentId: validatedData.learningContentId,
      conversationId: validatedData.conversationId,
      messageLength: validatedData.message.length,
    });

    // Initialize services
    const vectorizeIndex = (context as any)?.cloudflare?.env?.VECTORIZE_INDEX;
    const env = (context as any)?.cloudflare?.env;
    const chatService = createRagChatService(db, vectorizeIndex, env);

    // Create streaming response
    const streamResponse = await chatService.chatStream({
      message: validatedData.message,
      conversationId: validatedData.conversationId,
      learningContentId: validatedData.learningContentId,
      userId: user.id,
    });

    // Return Server-Sent Events response
    return new Response(streamResponse.stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
        'X-Conversation-Id': streamResponse.conversationId,
        'X-Message-Id': streamResponse.messageId,
      },
    });

  } catch (error) {
    log.error(`[${requestId}] Streaming chat API error`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    return new Response(
      JSON.stringify({ 
        success: false, 
        error: 'Internal server error',
        conversationId: '',
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Handle POST requests for streaming chat (alternative method)
 */
export async function action({ request, context }: LoaderFunctionArgs) {
  const requestId = `stream-post-${Date.now()}-${Math.random().toString(36).substring(7)}`;
  
  try {
    // Authentication check
    const authSession = await requireAuthSession(request);
    const user = await getAuthenticatedUser(authSession);
    if (!user) {
      return Response.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Validate request method
    if (request.method !== 'POST') {
      return Response.json(
        { success: false, error: 'Method not allowed' },
        { status: 405 }
      );
    }

    // Parse and validate request body
    let requestData;
    try {
      requestData = await request.json();
    } catch (error) {
      return Response.json(
        { success: false, error: 'Invalid JSON request body' },
        { status: 400 }
      );
    }

    const validation = StreamChatRequestSchema.safeParse(requestData);
    if (!validation.success) {
      return Response.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: validation.error.issues.map(issue => issue.message)
        },
        { status: 400 }
      );
    }

    const { message, conversationId, learningContentId } = validation.data;

    log.info(`[${requestId}] Streaming chat POST request received`, {
      userId: user.id,
      learningContentId,
      conversationId,
      messageLength: message.length,
    });

    // Initialize services
    const vectorizeIndex = (context as any)?.cloudflare?.env?.VECTORIZE_INDEX;
    const env = (context as any)?.cloudflare?.env;
    const chatService = createRagChatService(db, vectorizeIndex, env);

    // Create streaming response
    const streamResponse = await chatService.chatStream({
      message,
      conversationId,
      learningContentId,
      userId: user.id,
    });

    // Return streaming response
    return new Response(streamResponse.stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
        'X-Conversation-Id': streamResponse.conversationId,
        'X-Message-Id': streamResponse.messageId,
      },
    });

  } catch (error) {
    log.error(`[${requestId}] Streaming chat POST API error`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    return Response.json(
      { 
        success: false, 
        error: 'Internal server error',
        conversationId: '',
      },
      { status: 500 }
    );
  }
}