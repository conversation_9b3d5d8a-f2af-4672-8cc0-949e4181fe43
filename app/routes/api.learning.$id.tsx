import { type ActionFunctionArgs, type LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { 
  getLearningContentById, 
  updateLearningContent, 
  deleteLearningContent,
  duplicateLearningContent 
} from "~/db/services/learning-content";
import { requireAuthSession } from "~/lib/auth/supabase-server";

// Import schemas from api.learning.tsx for consistency
const stepBlockSchema = z.object({
  id: z.string(),
  type: z.enum(["paragraph", "list", "code", "quote", "table", "chart"]),
  data: z.any(), // Required data property
});

const stepSchema = z.object({
  id: z.string(),
  title: z.string(),
  blocks: z.array(stepBlockSchema),
});

// Validation schemas
const updateContentSchema = z.object({
  title: z.string().min(1, "Title is required").optional(),
  description: z.string().optional(),
  steps: z.array(stepSchema).optional(), // Use steps instead of content
  contentType: z.enum(["standard", "kwaci-primer"]).optional(),
  learningLevel: z.enum(["beginner", "intermediate", "advanced"]).optional(),
  isPublic: z.boolean().optional(),
  aiMetadata: z.any().optional(),
});

const actionSchema = z.object({
  action: z.enum(["update", "delete", "duplicate"]),
  data: updateContentSchema.optional(),
});

// GET /api/learning/:id - Get specific learning content
export async function loader({ request, params }: LoaderFunctionArgs) {
  const requestId = `req-${Date.now()}-${Math.random().toString(36).substring(7)}`;
  const authSession = await requireAuthSession(request);
  const { id } = params;

  console.log(`🔍 [${requestId}] API /learning/${id} - Request started`, {
    contentId: id,
    userId: authSession.user.id,
    userEmail: authSession.user.email,
    timestamp: new Date().toISOString(),
    url: request.url,
    method: request.method
  });

  if (!id) {
    console.error(`❌ [${requestId}] Missing content ID`);
    return Response.json({ success: false, error: "Content ID is required" }, { status: 400 });
  }

  try {
    console.log(`📊 [${requestId}] Calling getLearningContentById`, {
      contentId: id,
      userId: authSession.user.id,
      hasUserId: !!authSession.user.id
    });

    const content = await getLearningContentById(db, id, authSession.user.id);
    
    console.log(`📋 [${requestId}] Database query result`, {
      contentId: id,
      found: !!content,
      contentTitle: content?.title,
      contentUserId: content?.userId,
      contentIsPublic: content?.isPublic,
      requestingUserId: authSession.user.id
    });
    
    if (!content) {
      console.warn(`⚠️ [${requestId}] Content not found or access denied`, {
        contentId: id,
        userId: authSession.user.id
      });
      return Response.json({ success: false, error: "Content not found" }, { status: 404 });
    }

    console.log(`✅ [${requestId}] Successfully retrieved content`, {
      contentId: id,
      title: content.title,
      stepsCount: content.steps?.length || 0
    });

    return Response.json({ success: true, data: content });
  } catch (error) {
    console.error(`❌ [${requestId}] Error fetching learning content:`, {
      contentId: id,
      userId: authSession.user.id,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    
    // Check if it's an access denied error
    if (error instanceof Error && error.message.includes('Access denied')) {
      return Response.json(
        { success: false, error: "Access denied to private content" },
        { status: 403 }
      );
    }
    
    return Response.json(
      { success: false, error: "Failed to fetch learning content" },
      { status: 500 }
    );
  }
}

// POST /api/learning/:id - Update, delete, or duplicate learning content
export async function action({ request, params }: ActionFunctionArgs) {
  const authSession = await requireAuthSession(request);
  const { id } = params;

  if (!id) {
    return Response.json({ success: false, error: "Content ID is required" }, { status: 400 });
  }

  try {
    const body = await request.json();
    const { action, data } = actionSchema.parse(body);

    // First, verify the content exists and user has permission
    const existingContent = await getLearningContentById(db, id);
    if (!existingContent) {
      return Response.json({ success: false, error: "Content not found" }, { status: 404 });
    }

    if (existingContent.userId !== authSession.user.id) {
      return Response.json({ success: false, error: "Access denied" }, { status: 403 });
    }

    switch (action) {
      case "update":
        if (!data) {
          return Response.json({ success: false, error: "Update data is required" }, { status: 400 });
        }
        const updatedContent = await updateLearningContent(db, id, authSession.user.id, data as any);
        if (!updatedContent) {
          return Response.json({ success: false, error: "Failed to update content" }, { status: 500 });
        }
        return Response.json({ success: true, data: updatedContent });

      case "delete":
        const deleteResult = await deleteLearningContent(db, id, authSession.user.id);
        if (!deleteResult) {
          return Response.json({ success: false, error: "Failed to delete content" }, { status: 500 });
        }
        return Response.json({ success: true, message: "Content deleted successfully" });

      case "duplicate":
        const duplicatedContent = await duplicateLearningContent(db, id, authSession.user.id);
        return Response.json({ success: true, data: duplicatedContent }, { status: 201 });

      default:
        return Response.json({ success: false, error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }
    
    console.error("Error processing learning content action:", error);
    return Response.json(
      { success: false, error: "Failed to process request" },
      { status: 500 }
    );
  }
}