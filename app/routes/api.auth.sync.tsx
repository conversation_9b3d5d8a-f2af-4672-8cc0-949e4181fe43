import type { ActionFunctionArgs } from "react-router";

function cookie(name: string, value: string, opts: { maxAge?: number; httpOnly?: boolean; secure?: boolean; sameSite?: 'Lax' | 'Strict' | 'None'; path?: string } = {}) {
  const parts = [`${name}=${value}`];
  if (opts.maxAge !== undefined) parts.push(`Max-Age=${opts.maxAge}`);
  parts.push(`Path=${opts.path ?? '/'}`);
  parts.push(`SameSite=${opts.sameSite ?? 'Lax'}`);
  if (opts.secure) parts.push('Secure');
  if (opts.httpOnly) parts.push('HttpOnly');
  return parts.join('; ');
}

export async function action({ request }: ActionFunctionArgs) {
  try {
    if (request.method === 'DELETE') {
      return new Response(JSON.stringify({ success: true }), {
        status: 200,
        headers: [
          ['Content-Type', 'application/json'],
          ['Set-Cookie', cookie('sb-access-token', '', { maxAge: 0, path: '/' })],
          ['Set-Cookie', cookie('sb-refresh-token', '', { maxAge: 0, path: '/' })],
        ],
      });
    }

    if (request.method !== 'POST') {
      return Response.json({ success: false, error: 'Method not allowed' }, { status: 405 });
    }

    const { access_token, refresh_token, expires_at } = await request.json();
    if (!access_token) {
      return Response.json({ success: false, error: 'Missing access token' }, { status: 400 });
    }

    const url = new URL(request.url);
    const secure = url.protocol === 'https:';
    const maxAge = expires_at ? Math.max(0, Math.floor(expires_at - Date.now() / 1000)) : 60 * 60;

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Set-Cookie': [
          cookie('sb-access-token', access_token, { maxAge, httpOnly: true, secure, sameSite: 'Lax', path: '/' }),
          refresh_token ? cookie('sb-refresh-token', refresh_token, { maxAge: 60 * 60 * 24 * 30, httpOnly: true, secure, sameSite: 'Lax', path: '/' }) : ''
        ].filter(Boolean).join(', '),
      },
    });
  } catch (error) {
    console.error('auth sync error', error);
    return Response.json({ success: false, error: 'Internal Server Error' }, { status: 500 });
  }
}

