import { type ActionFunctionArgs, type LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { conversations, messages } from "~/db/schema/chat";
import { eq, and, desc, asc } from "drizzle-orm";
import { nanoid } from "nanoid";
import { trackAnalyticsEvent } from "~/db/services/analytics";
import { getConversationMemory } from "~/db/services/memory";
import { createConversationMemoryService } from "~/lib/rag/services/conversation-memory-service";

// Validation schemas
const createConversationSchema = z.object({
  action: z.literal("create_conversation"),
  learningContentId: z.string(),
  title: z.string().optional(),
});

const sendMessageSchema = z.object({
  action: z.literal("send_message"),
  conversationId: z.string(),
  content: z.string().min(1, "Message content is required"),
  messageType: z.enum(["text", "system"]).default("text"),
});

const getMessagesSchema = z.object({
  action: z.literal("get_messages"),
  conversationId: z.string(),
  limit: z.number().min(1).max(100).default(50),
  offset: z.number().min(0).default(0),
});

const getMemorySchema = z.object({
  action: z.literal("get_memory"),
  conversationId: z.string(),
});

const chatActionSchema = z.discriminatedUnion("action", [
  createConversationSchema,
  sendMessageSchema,
  getMessagesSchema,
  getMemorySchema,
]);

const conversationQuerySchema = z.object({
  learningContentId: z.string().optional(),
  limit: z.number().min(1).max(50).default(20),
  offset: z.number().min(0).default(0),
});

// GET /api/chat - Get user's conversations
export async function loader({ request }: LoaderFunctionArgs) {
  const authSession = await requireAuthSession(request);
  const url = new URL(request.url);
  
  const queryParams = Object.fromEntries(url.searchParams);
  const { learningContentId, limit, offset } = conversationQuerySchema.parse({
    ...queryParams,
    limit: queryParams.limit ? parseInt(queryParams.limit) : 20,
    offset: queryParams.offset ? parseInt(queryParams.offset) : 0,
  });

  try {
    const conditions = [eq(conversations.userId, authSession.user.id)];
    
    if (learningContentId) {
      conditions.push(eq(conversations.learningContentId, learningContentId));
    }

    const userConversations = await db
      .select()
      .from(conversations)
      .where(and(...conditions))
      .orderBy(desc(conversations.updatedAt))
      .limit(limit)
      .offset(offset);

    return Response.json({
      success: true,
      data: userConversations
    });
  } catch (error) {
    console.error("Error fetching conversations:", error);
    return Response.json(
      { success: false, error: "Failed to fetch conversations" },
      { status: 500 }
    );
  }
}

// POST /api/chat - Handle chat actions
export async function action({ request }: ActionFunctionArgs) {
  const authSession = await requireAuthSession(request);
  
  if (request.method !== "POST") {
    return Response.json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const input = chatActionSchema.parse(body);

    switch (input.action) {
      case "create_conversation": {
        const newConversation = await db.insert(conversations).values({
          id: nanoid(),
          userId: authSession.user.id,
          learningContentId: input.learningContentId,
          title: input.title || `Chat about Learning Content`,
          createdAt: new Date(),
          updatedAt: new Date(),
        }).returning();

        // Track conversation creation
        await trackAnalyticsEvent(db, {
          userId: authSession.user.id,
          eventType: "interaction",
          contentId: input.learningContentId,
          metadata: {
            action: "conversation_created",
          },
        });

        return Response.json({ success: true, data: newConversation[0] }, { status: 201 });
      }

      case "send_message": {
        // Verify conversation exists and user owns it
        const conversation = await db
          .select()
          .from(conversations)
          .where(and(
            eq(conversations.id, input.conversationId),
            eq(conversations.userId, authSession.user.id)
          ))
          .limit(1);

        if (conversation.length === 0) {
          return Response.json(
            { success: false, error: "Conversation not found" },
            { status: 404 }
          );
        }

        // Save user message
        const userMessage = await db.insert(messages).values({
          id: nanoid(),
          conversationId: input.conversationId,
          content: input.content,
          senderRole: "user",
          messageType: input.messageType,
          createdAt: new Date(),
        }).returning();

        // Update conversation timestamp
        await db
          .update(conversations)
          .set({ updatedAt: new Date() })
          .where(eq(conversations.id, input.conversationId));

        // Here you would typically:
        // 1. Process the message with RAG system
        // 2. Generate AI response
        // 3. Save AI response as another message
        // For now, we'll just save the user message and return a placeholder response

        const aiResponse = "This is a placeholder AI response. In a full implementation, this would be generated using the RAG system with your learning content.";
        
        const aiMessage = await db.insert(messages).values({
          id: nanoid(),
          conversationId: input.conversationId,
          content: aiResponse,
          senderRole: "assistant",
          messageType: "text",
          createdAt: new Date(),
        }).returning();

        // Track message interaction
        await trackAnalyticsEvent(db, {
          userId: authSession.user.id,
          eventType: "interaction",
          contentId: conversation[0].learningContentId,
          metadata: {
            action: "message_sent",
          },
        });

        // Extract and update conversation memory after messages are sent
        try {
          const memoryService = createConversationMemoryService(db, process.env);
          
          // Get all messages for this conversation to extract memory
          const allMessages = await db
            .select()
            .from(messages)
            .where(eq(messages.conversationId, input.conversationId))
            .orderBy(asc(messages.createdAt));
          
          // Extract memory if we have enough messages (3+ for meaningful analysis)
          if (allMessages.length >= 3) {
            console.log('Triggering memory extraction for conversation:', input.conversationId, 'with', allMessages.length, 'messages');
            await memoryService.extractAndUpdateMemory(input.conversationId, allMessages);
          }
        } catch (memoryError) {
          // Log memory extraction error but don't fail the request
          console.error('Failed to extract conversation memory:', memoryError);
        }

        return Response.json({
          success: true,
          data: {
            userMessage: userMessage[0],
            aiMessage: aiMessage[0],
          }
        });
      }

      case "get_messages": {
        // Verify conversation exists and user owns it
        const conversation = await db
          .select()
          .from(conversations)
          .where(and(
            eq(conversations.id, input.conversationId),
            eq(conversations.userId, authSession.user.id)
          ))
          .limit(1);

        if (conversation.length === 0) {
          return Response.json(
            { success: false, error: "Conversation not found" },
            { status: 404 }
          );
        }

        // Get messages for the conversation
        const conversationMessages = await db
          .select()
          .from(messages)
          .where(eq(messages.conversationId, input.conversationId))
          .orderBy(asc(messages.createdAt))
          .limit(input.limit)
          .offset(input.offset);

        return Response.json({
          success: true,
          data: conversationMessages
        });
      }

      case "get_memory": {
        // Verify conversation exists and user owns it
        const conversation = await db
          .select()
          .from(conversations)
          .where(and(
            eq(conversations.id, input.conversationId),
            eq(conversations.userId, authSession.user.id)
          ))
          .limit(1);

        if (conversation.length === 0) {
          return Response.json(
            { success: false, error: "Conversation not found" },
            { status: 404 }
          );
        }

        // Get conversation memory
        const memory = await getConversationMemory(db, input.conversationId);

        return Response.json({
          success: true,
          data: {
            proposedTopics: memory?.proposedTopics || [],
            keyTopics: memory?.keyTopics || [],
            userPreferences: memory?.userPreferences || {},
            contextSummary: memory?.contextSummary || '',
          }
        });
      }

      default:
        return Response.json({ success: false, error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }
    
    console.error("Error processing chat action:", error);
    return Response.json(
      { success: false, error: "Failed to process request" },
      { status: 500 }
    );
  }
}