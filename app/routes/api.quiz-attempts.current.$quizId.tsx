import type { LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { quiz, quizAttempt, quizProgress, learningContent } from "~/db/schema";
import { and, desc, eq, or, gte } from "drizzle-orm";
import { withDatabaseRetry, sanitizeDbParameter } from "~/lib/db-retry.server";

// Validation schema for quiz ID
const quizIdSchema = z.string().min(1, "Quiz ID is required");

// GET /api/quiz-attempts/current/:quizId
export async function loader({ request, params }: LoaderFunctionArgs) {
  const auth = await requireAuthSession(request);
  const { quizId } = params;

  // Validate quiz ID
  const quizIdValidation = quizIdSchema.safeParse(quizId);
  if (!quizIdValidation.success) {
    return Response.json({
      success: false,
      error: "Invalid quiz ID format",
      details: quizIdValidation.error.errors
    }, { status: 400 });
  }

  // Properly decode URL parameter and sanitize for Cloudflare Workers + Supabase HTTP driver
  const validQuizId = sanitizeDbParameter(quizIdValidation.data);

  // Log the quiz ID for debugging (only in development)
  if (process.env.NODE_ENV !== 'production') {
    console.log('🔍 Quiz ID for database query:', JSON.stringify(validQuizId));
  }

  try {
    // Verify quiz exists and access with database retry
    console.log('🔍 Executing quiz query with ID:', JSON.stringify(validQuizId));
    const quizRows = await withDatabaseRetry(() => {
      console.log('🔍 Database retry attempt for quiz ID:', JSON.stringify(validQuizId));
      return db
        .select({
          id: quiz.id,
          isPublic: quiz.isPublic,
          createdBy: quiz.createdBy,
          learningContentId: quiz.learningContentId,
        })
        .from(quiz)
        .where(eq(quiz.id, validQuizId))
        .limit(1);
    }) as Array<{
      id: string;
      isPublic: boolean;
      createdBy: string;
      learningContentId: string;
    }>;

    if (quizRows.length === 0) {
      return Response.json({ success: false, error: "Quiz not found" }, { status: 404 });
    }

    const q = quizRows[0];

    // Access: public OR createdBy OR content owner
    const contentRows = await withDatabaseRetry(() =>
      db
        .select({
          isPublic: learningContent.isPublic,
          userId: learningContent.userId,
        })
        .from(learningContent)
        .where(eq(learningContent.id, q.learningContentId))
        .limit(1)
    ) as Array<{
      isPublic: boolean;
      userId: string;
    }>;

    const canAccess =
      q.isPublic ||
      q.createdBy === auth.user.id ||
      (contentRows[0] && (contentRows[0].isPublic || contentRows[0].userId === auth.user.id));

    if (!canAccess) {
      return Response.json({ success: false, error: "Access denied" }, { status: 403 });
    }

    // Find latest attempt (incomplete or recently completed within 5 minutes)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

    const attemptRows = await withDatabaseRetry(() =>
      db
        .select()
        .from(quizAttempt)
        .where(
          and(
            eq(quizAttempt.quizId, validQuizId),
            eq(quizAttempt.userId, auth.user.id),
            or(
              eq(quizAttempt.isCompleted, false),
              and(
                eq(quizAttempt.isCompleted, true),
                gte(quizAttempt.completedAt, fiveMinutesAgo)
              )
            )
          )
        )
        .orderBy(desc(quizAttempt.startedAt))
        .limit(1)
    ) as Array<any>;

    if (attemptRows.length === 0) {
      return Response.json({ success: true, data: { attempt: null, progress: null } });
    }

    const attempt = attemptRows[0];

    // Fetch progress row (if any)
    const progressRows = await withDatabaseRetry(() =>
      db
        .select()
        .from(quizProgress)
        .where(eq(quizProgress.attemptId, attempt.id))
        .limit(1)
    ) as Array<any>;

    return Response.json({ success: true, data: { attempt, progress: progressRows[0] || null } });
  } catch (error) {
    // If it's a Response (like 401 from requireAuthSession), re-throw it
    if (error instanceof Response) {
      throw error;
    }

    console.error("Error fetching current attempt:", error);
    return Response.json({ success: false, error: "Failed to fetch current attempt" }, { status: 500 });
  }
}

