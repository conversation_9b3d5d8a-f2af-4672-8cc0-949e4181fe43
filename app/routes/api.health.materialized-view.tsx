import type { LoaderFunctionArgs } from "react-router";
import { checkMaterializedViewHealth, forceRefreshMaterializedView } from "~/lib/startup/materialized-view-setup";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { materializedViewProcessor } from "~/lib/services/materialized-view-processor";
import { log } from "~/lib/logger";

/**
 * Health check endpoint for materialized view optimization system
 * GET: Check system health status
 * POST: Force refresh materialized view (admin only)
 */

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Check if user is authenticated (optional for health checks)
    let isAdmin = false;
    try {
      const session = await requireAuthSession(request);
      // You can add admin role check here if needed
      isAdmin = true;
    } catch {
      // Not authenticated, continue with limited info
    }

    const health = await checkMaterializedViewHealth();
    const queueStatus = await materializedViewProcessor.getQueueStatus();

    const response = {
      status: health.isHealthy ? "healthy" : "unhealthy",
      timestamp: new Date().toISOString(),
      system: {
        isHealthy: health.isHealthy,
        errors: health.errors,
        lastRefresh: health.lastRefresh,
      },
      queue: isAdmin ? queueStatus : {
        // Limited info for non-admin users
        pending_count: queueStatus.pending_count,
        processing_count: queueStatus.processing_count,
        last_completed_at: queueStatus.last_completed_at,
      },
      processor: {
        status: "running", // MaterializedViewProcessor doesn't expose isRunning method
      },
    };

    return new Response(JSON.stringify(response), {
      status: health.isHealthy ? 200 : 503,
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0",
      },
    });
  } catch (error) {
    console.error("Health check failed:", error);
    return new Response(
      JSON.stringify({
        status: "error",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }
}

export async function action({ request }: LoaderFunctionArgs) {
  try {
    // Require authentication for force refresh
    await requireAuthSession(request);

    const method = request.method;

    if (method === "POST") {
      // Force refresh the materialized view
      await forceRefreshMaterializedView();
      
      console.log("Materialized view force refresh completed via API");
      
      return new Response(
        JSON.stringify({
          status: "success",
          timestamp: new Date().toISOString(),
          message: "Materialized view refresh initiated",
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }

    if (method === "DELETE") {
      // Clear failed items from queue
      const clearedCount = await materializedViewProcessor.clearFailedItems();
      
      console.log(`Cleared ${clearedCount} failed items from materialized view queue`);
      
      return new Response(
        JSON.stringify({
          status: "success",
          timestamp: new Date().toISOString(),
          message: "Failed items cleared from queue",
        }),
        {
          status: 200,
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
    }

    return new Response(
      JSON.stringify({ error: "Method not allowed" }),
      { 
        status: 405,
        headers: { "Content-Type": "application/json" }
      }
    );
  } catch (error) {
    console.error("Materialized view action failed:", error);
    return new Response(
      JSON.stringify({
        status: "error",
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : "Unknown error",
      }),
      {
        status: 500,
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  }
}