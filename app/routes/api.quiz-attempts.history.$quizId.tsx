import type { LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { quizAttempt, quiz, learningContent } from "~/db/schema";
import { and, desc, eq } from "drizzle-orm";
import { withDatabaseRetry, sanitizeDbParameter } from "~/lib/db-retry.server";

const paramsSchema = z.object({
  limit: z.coerce.number().min(1).max(100).default(20),
  offset: z.coerce.number().min(0).default(0),
});

// Validation schema for quiz ID
const quizIdSchema = z.string().min(1, "Quiz ID is required");

// GET /api/quiz-attempts/history/:quizId
export async function loader({ request, params }: LoaderFunctionArgs) {
  const { quizId } = params;

  // Validate quiz ID
  const quizIdValidation = quizIdSchema.safeParse(quizId);
  if (!quizIdValidation.success) {
    return Response.json({
      success: false,
      error: "Invalid quiz ID format",
      details: quizIdValidation.error.errors
    }, { status: 400 });
  }

  // Properly decode URL parameter and sanitize for Cloudflare Workers + Supabase HTTP driver
  const validQuizId = sanitizeDbParameter(quizIdValidation.data);

  // Log the quiz ID for debugging (only in development)
  if (process.env.NODE_ENV !== 'production') {
    console.log('🔍 Quiz History - Quiz ID for database query:', JSON.stringify(validQuizId));
  }

  const url = new URL(request.url);
  const parsed = paramsSchema.parse({
    limit: url.searchParams.get("limit") || undefined,
    offset: url.searchParams.get("offset") || undefined,
  });

  try {
    // Ensure authenticated
    const auth = await requireAuthSession(request);

    // Verify quiz exists and access with database retry
    const quizRows = await withDatabaseRetry(() =>
      db
        .select({ id: quiz.id, learningContentId: quiz.learningContentId, isPublic: quiz.isPublic, createdBy: quiz.createdBy })
        .from(quiz)
        .where(eq(quiz.id, validQuizId))
        .limit(1)
    ) as Array<{
      id: string;
      learningContentId: string;
      isPublic: boolean;
      createdBy: string;
    }>;

    if (quizRows.length === 0) {
      return Response.json({ success: false, error: "Quiz not found" }, { status: 404 });
    }

    // Access control via learning content with database retry
    const contentRows = await withDatabaseRetry(() =>
      db
        .select({ isPublic: learningContent.isPublic, userId: learningContent.userId })
        .from(learningContent)
        .where(eq(learningContent.id, quizRows[0].learningContentId))
        .limit(1)
    ) as Array<{
      isPublic: boolean;
      userId: string;
    }>;

    const canAccess =
      quizRows[0].isPublic ||
      quizRows[0].createdBy === auth.user.id ||
      (contentRows[0] && (contentRows[0].isPublic || contentRows[0].userId === auth.user.id));

    if (!canAccess) {
      return Response.json({ success: false, error: "Access denied" }, { status: 403 });
    }

    // Fetch user's attempts (completed and in-progress) for history with database retry
    const attempts = await withDatabaseRetry(() =>
      db
        .select()
        .from(quizAttempt)
        .where(and(eq(quizAttempt.quizId, validQuizId), eq(quizAttempt.userId, auth.user.id)))
        .orderBy(desc(quizAttempt.startedAt))
        .limit(parsed.limit)
        .offset(parsed.offset)
    ) as Array<any>;

    return Response.json({
      success: true,
      data: {
        attempts,
        pagination: {
          limit: parsed.limit,
          offset: parsed.offset,
          hasMore: attempts.length === parsed.limit,
        },
      },
    });
  } catch (error) {
    // If it's a Response (like 401 from requireAuthSession), re-throw it
    if (error instanceof Response) {
      throw error;
    }

    console.error("Error fetching attempt history:", error);
    return Response.json({ success: false, error: "Failed to fetch attempt history" }, { status: 500 });
  }
}

