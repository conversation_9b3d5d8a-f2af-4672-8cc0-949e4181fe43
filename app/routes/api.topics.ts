/**
 * Topics API route for memory-aware topic management
 * 
 * This route handles topic acceptance and rejection for the enhanced memory system.
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from 'react-router';
import { createRagChatService } from '~/lib/rag/services/rag-chat-service';
import { db } from '~/db/connection';
import { requireAuthSession } from '~/lib/auth/supabase-server';
import { getAuthenticatedUser } from '~/lib/auth/middleware';
import { log } from '~/lib/logger';
import { z } from 'zod';

// Request validation schemas
const TopicActionSchema = z.object({
  action: z.enum(['accept', 'reject']),
  conversationId: z.string().min(1, 'Conversation ID cannot be empty'),
  topic: z.string().min(1, 'Topic cannot be empty'),
});

/**
 * Handle GET requests - return 405 Method Not Allowed
 */
export async function loader({ request }: LoaderFunctionArgs) {
  return Response.json(
    { success: false, error: 'Method not allowed. Use POST for topic actions.' },
    { status: 405, headers: { 'Allow': 'POST' } }
  );
}

/**
 * Handle topic action POST requests (accept/reject)
 */
export async function action({ request, context }: ActionFunctionArgs) {
  const requestId = `topic-${Date.now()}-${Math.random().toString(36).substring(7)}`;

  try {
    // Authentication check
    const authSession = await requireAuthSession(request);
    const user = getAuthenticatedUser(authSession);

    // Validate request method
    if (request.method !== 'POST') {
      return Response.json(
        { success: false, error: 'Method not allowed' },
        { status: 405 }
      );
    }

    // Parse and validate request body
    let requestData;
    try {
      requestData = await request.json();
    } catch (error) {
      return Response.json(
        { success: false, error: 'Invalid JSON request body' },
        { status: 400 }
      );
    }

    const validation = TopicActionSchema.safeParse(requestData);
    if (!validation.success) {
      return Response.json(
        { 
          success: false, 
          error: 'Invalid request data',
          details: validation.error.issues.map(issue => issue.message)
        },
        { status: 400 }
      );
    }

    const { action, conversationId, topic } = validation.data;

    log.info(`[${requestId}] Topic ${action} request`, {
      userId: user.id,
      conversationId,
      topic,
      action,
    });

    // Get environment variables from context (Cloudflare Workers) or process.env (local dev)
    const env = (context as any)?.cloudflare?.env;
    const vectorizeIndex = env?.VECTORIZE_INDEX;
    
    // Check for OpenRouter API key in both Cloudflare env and process.env
    const openRouterApiKey = env?.OPENROUTER_API_KEY || process.env.OPENROUTER_API_KEY;
    
    if (!openRouterApiKey) {
      log.error(`[${requestId}] Missing OpenRouter API key`);
      return Response.json(
        { success: false, error: 'Service configuration error' },
        { status: 500 }
      );
    }
    
    // Create environment object that works for both local and Cloudflare
    const envVars = {
      ...env,
      OPENROUTER_API_KEY: openRouterApiKey,
      VOYAGE_API_KEY: env?.VOYAGE_API_KEY || process.env.VOYAGE_API_KEY,
      VECTORIZE_INDEX_NAME: env?.VECTORIZE_INDEX_NAME || process.env.VECTORIZE_INDEX_NAME,
    };

    // Create chat service
    const chatService = createRagChatService(db, vectorizeIndex, envVars);

    // Handle topic action
    try {
      if (action === 'accept') {
        await chatService.acceptTopic(conversationId, topic);
      } else {
        await chatService.rejectTopic(conversationId, topic);
      }

      log.success(`[${requestId}] Topic ${action} completed successfully`, {
        conversationId,
        topic,
        action,
      });

      return Response.json({
        success: true,
        message: `Topic ${action}ed successfully`
      });
    } catch (topicError) {
      log.error(`[${requestId}] Topic ${action} failed`, {
        conversationId,
        topic,
        action,
        error: topicError instanceof Error ? topicError.message : String(topicError),
      });

      return Response.json(
        {
          success: false,
          error: `Failed to ${action} topic`
        },
        { status: 500 }
      );
    }

  } catch (error) {
    log.error(`[${requestId}] Topic API error`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    return Response.json(
      { 
        success: false, 
        error: 'Internal server error'
      },
      { status: 500 }
    );
  }
}