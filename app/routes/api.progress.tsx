import { type ActionFunctionArgs, type LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { sql } from "drizzle-orm";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { 
  trackAnalyticsEvent, 
  updateLearningProgress, 
  getUserProgress,
  submitContentFeedback,
  addBookmark,
  removeBookmark,
  addNote,
  updateNote,
  deleteNote
} from "~/db/services/analytics";
import { getLearningContentById } from "~/db/services/learning-content";
import { generateSessionId } from "~/lib/utils/session";
import { optimizedRefreshLeaderboard } from "~/lib/utils/materialized-view-refresh";

// Validation schemas
const trackEventSchema = z.object({
  action: z.literal("track_event"),
  eventType: z.enum([
    "content_viewed",
    "content_completed", 
    "quiz_started",
    "quiz_completed",
    "step_completed",
    "time_spent",
    "interaction"
  ]),
  contentId: z.string().optional(),
  quizId: z.string().optional(),
  stepId: z.string().optional(),
  sessionId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  duration: z.number().min(0).optional(), // in seconds
});

const updateProgressSchema = z.object({
  action: z.literal("update_progress"),
  contentId: z.string(),
  stepId: z.string().optional(),
  progress: z.number().min(0).max(100), // percentage
  timeSpent: z.number().min(0), // in seconds
  completed: z.boolean().default(false),
  completedSteps: z.array(z.number()).optional(),
  currentStep: z.number().optional(),
  sessionId: z.string().optional(),
});

const submitFeedbackSchema = z.object({
  action: z.literal("submit_feedback"),
  contentId: z.string(),
  rating: z.number().min(1).max(5),
  feedbackText: z.string().optional(),
  isHelpful: z.boolean().optional(),
  suggestedImprovements: z.object({
    contentQuality: z.boolean().optional(),
    clarity: z.boolean().optional(),
    examples: z.boolean().optional(),
    length: z.boolean().optional(),
    difficulty: z.boolean().optional(),
    other: z.string().optional(),
  }).optional(),
  requestRegeneration: z.boolean().optional(),
  regenerationReason: z.string().optional(),
});

const addBookmarkSchema = z.object({
  action: z.literal("add_bookmark"),
  contentId: z.string(),
  stepId: z.string().optional(),
  note: z.string().optional(),
});

const removeBookmarkSchema = z.object({
  action: z.literal("remove_bookmark"),
  contentId: z.string(),
  stepId: z.string().optional(),
});

const addNoteSchema = z.object({
  action: z.literal("add_note"),
  contentId: z.string(),
  stepId: z.string().optional(),
  content: z.string(),
  noteType: z.enum(["general", "step_specific"]).default("step_specific"),
});

const updateNoteSchema = z.object({
  action: z.literal("update_note"),
  noteId: z.string(),
  content: z.string(),
});

const deleteNoteSchema = z.object({
  action: z.literal("delete_note"),
  noteId: z.string(),
});

const progressActionSchema = z.discriminatedUnion("action", [
  trackEventSchema,
  updateProgressSchema,
  submitFeedbackSchema,
  addBookmarkSchema,
  removeBookmarkSchema,
  addNoteSchema,
  updateNoteSchema,
  deleteNoteSchema,
]);

const progressQuerySchema = z.object({
  contentId: z.string().optional(),
  timeframe: z.enum(["week", "month", "all"]).default("month"),
  includeAnalytics: z.boolean().default(true),
});

// GET /api/progress - Get user's learning progress and analytics
export async function loader({ request }: LoaderFunctionArgs) {
  const timestamp = new Date().toISOString();
  const url = new URL(request.url);

  console.log(`[${timestamp}] [PROGRESS] Request received:`, {
    method: request.method,
    url: url.toString(),
    searchParams: Object.fromEntries(url.searchParams),
    hasAuthHeader: !!request.headers.get('Authorization'),
    hasCookies: !!request.headers.get('Cookie'),
  });

  console.log(`[${timestamp}] [PROGRESS] Calling requireAuthSession...`);
  const authSession = await requireAuthSession(request);

  console.log(`[${timestamp}] [PROGRESS] Auth session obtained:`, {
    hasUser: !!authSession?.user,
    userId: authSession?.user?.id,
    userEmail: authSession?.user?.email,
  });

  const queryParams = Object.fromEntries(url.searchParams);
  const { contentId, timeframe, includeAnalytics } = progressQuerySchema.parse({
    ...queryParams,
    includeAnalytics: queryParams.includeAnalytics !== 'false',
  });

  console.log(`[${timestamp}] [PROGRESS] Query parameters:`, {
    contentId,
    timeframe,
    includeAnalytics,
  });

  try {
    // Get user's learning progress
    console.log(`[${timestamp}] [PROGRESS] Fetching user progress...`);
    const progress = contentId ? await getUserProgress(db, contentId, authSession.user.id) : null;

    console.log(`[${timestamp}] [PROGRESS] User progress result:`, {
      hasProgress: !!progress,
      contentId: progress?.contentId,
      completionPercentage: progress?.completionPercentage,
      isCompleted: progress?.isCompleted,
      totalTimeSpent: progress?.totalTimeSpent,
    });

    let analytics = null;
    if (includeAnalytics) {
      // Calculate timeframe dates
      const now = new Date();
      let startDate: Date;
      
      switch (timeframe) {
        case "week":
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case "month":
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(0); // Beginning of time
      }

      // Get analytics data (you might want to create a specific analytics query function)
      analytics = {
        totalTimeSpent: progress?.totalTimeSpent || 0,
        contentCompleted: progress?.isCompleted ? 1 : 0,
        contentInProgress: progress && !progress.isCompleted && (progress.completionPercentage || 0) > 0 ? 1 : 0,
        averageProgress: progress?.completionPercentage || 0,
        recentActivity: progress ? [{
          id: progress.id,
          contentId: progress.contentId,
          updatedAt: progress.updatedAt,
          completionPercentage: progress.completionPercentage,
          isCompleted: progress.isCompleted
        }] : [],
      };
    }

    const responseData = {
      success: true,
      data: {
        progress,
        analytics,
      }
    };

    console.log(`[${timestamp}] [PROGRESS] Final response:`, {
      success: responseData.success,
      hasProgress: !!responseData.data.progress,
      hasAnalytics: !!responseData.data.analytics,
      analyticsKeys: responseData.data.analytics ? Object.keys(responseData.data.analytics) : 'N/A',
    });

    return Response.json(responseData);
  } catch (error) {
    console.error(`[${timestamp}] [PROGRESS] Error occurred:`, {
      error: error instanceof Error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
      } : error,
      errorType: typeof error,
    });

    return Response.json(
      { success: false, error: "Failed to fetch progress" },
      { status: 500 }
    );
  }
}

// POST /api/progress - Handle progress tracking actions
export async function action({ request }: ActionFunctionArgs) {
  const authSession = await requireAuthSession(request);
  
  if (request.method !== "POST") {
    return Response.json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    const input = progressActionSchema.parse(body);

    switch (input.action) {
      case "track_event": {
        // Generate session ID if not provided
        const sessionId = input.sessionId || generateSessionId();
        
        const event = await trackAnalyticsEvent(db, {
          userId: authSession.user.id,
          eventType: input.eventType === 'content_viewed' ? 'view' : 
                    input.eventType === 'content_completed' ? 'complete' :
                    input.eventType === 'quiz_started' ? 'start' :
                    input.eventType === 'quiz_completed' ? 'complete' :
                    input.eventType === 'step_completed' ? 'step_complete' :
                    input.eventType === 'time_spent' ? 'interaction' : 'interaction',
          contentId: input.contentId || '',
          sessionId: sessionId,
          stepId: input.stepId,
          timeSpent: input.duration,
          metadata: input.metadata,
        });

        return Response.json({ success: true, data: { ...event, sessionId } });
      }

      case "update_progress": {
        // Generate session ID if not provided
        const sessionId = input.sessionId || generateSessionId();
        
        // Fetch learning content to get total steps count
        const learningContent = await getLearningContentById(db, input.contentId, authSession.user.id);
        if (!learningContent) {
          return Response.json({ success: false, error: "Learning content not found" }, { status: 404 });
        }
        
        // Calculate if content should be marked as completed
        const totalSteps = learningContent.steps.length;
        const completedSteps = input.completedSteps || [];
        const shouldBeCompleted = input.completed || (completedSteps.length >= totalSteps);
        
        const progressUpdate = await updateLearningProgress(db, input.contentId, authSession.user.id, {
          currentStepIndex: input.currentStep !== undefined ? input.currentStep : (input.stepId ? parseInt(input.stepId) : 0),
          completedSteps: completedSteps,
          completionPercentage: input.progress,
          totalTimeSpent: input.timeSpent,
          isCompleted: shouldBeCompleted,
        });

        // Also track as analytics event with session ID
        await trackAnalyticsEvent(db, {
          userId: authSession.user.id,
          eventType: shouldBeCompleted ? "complete" : "step_complete",
          contentId: input.contentId,
          sessionId: sessionId,
          stepId: input.stepId,
          timeSpent: input.timeSpent,
          completionPercentage: input.progress,
          metadata: {
            totalSteps: totalSteps,
          },
        });

        // Refresh the leaderboard materialized view when learning content is completed
        if (shouldBeCompleted) {
          try {
            await optimizedRefreshLeaderboard(db);
          } catch (error) {
            // Log error but don't fail the request - leaderboard refresh is not critical
            console.warn('Failed to refresh leaderboard materialized view:', error);
          }
        }

        return Response.json({ success: true, data: { ...progressUpdate, sessionId } });
      }

      case "submit_feedback": {
        const feedback = await submitContentFeedback(db, {
          userId: authSession.user.id,
          contentId: input.contentId,
          rating: input.rating,
          feedbackText: input.feedbackText,
          isHelpful: input.isHelpful,
          suggestedImprovements: input.suggestedImprovements,
          requestRegeneration: input.requestRegeneration,
          regenerationReason: input.regenerationReason,
        });

        // Track feedback submission as analytics event
        await trackAnalyticsEvent(db, {
          userId: authSession.user.id,
          eventType: "feedback",
          contentId: input.contentId,
          metadata: {
            action: "feedback_submitted",
            rating: input.rating,
            feedbackText: input.feedbackText,
            isHelpful: input.isHelpful,
            requestRegeneration: input.requestRegeneration,
          },
        });

        return Response.json({ success: true, data: feedback });
      }

      case "add_bookmark": {
        const stepIndex = input.stepId ? parseInt(input.stepId) : 0;
        const bookmark = await addBookmark(db, input.contentId, authSession.user.id, stepIndex, input.note);

        // Track bookmark addition as analytics event
        await trackAnalyticsEvent(db, {
          userId: authSession.user.id,
          eventType: "bookmark",
          contentId: input.contentId,
          stepId: input.stepId,
          stepIndex,
          metadata: {
            action: "bookmark_added",
          },
        });

        return Response.json({ success: true, data: bookmark });
      }

      case "remove_bookmark": {
        const stepIndex = input.stepId ? parseInt(input.stepId) : 0;
        const bookmark = await removeBookmark(db, input.contentId, authSession.user.id, stepIndex);

        // Track bookmark removal as analytics event
        await trackAnalyticsEvent(db, {
          userId: authSession.user.id,
          eventType: "bookmark",
          contentId: input.contentId,
          stepId: input.stepId,
          stepIndex,
          metadata: {
            action: "bookmark_removed",
          },
        });

        return Response.json({ success: true, data: bookmark });
      }

      case "add_note": {
        const stepIndex = input.stepId ? parseInt(input.stepId) : 0;
        const note = await addNote(db, input.contentId, authSession.user.id, stepIndex, input.content);

        // Track note addition as analytics event
        await trackAnalyticsEvent(db, {
          userId: authSession.user.id,
          eventType: "interaction",
          contentId: input.contentId,
          stepId: input.stepId,
          stepIndex,
          metadata: {
            action: "note_added",
            noteType: input.noteType,
          },
        });

        return Response.json({ success: true, data: note });
      }

      case "update_note": {
        // Parse noteId formatted as `${contentId}:${index}`
        const [contentIdFromNote, noteIndexStr] = input.noteId.split(":")
        const noteIndex = parseInt(noteIndexStr, 10)
        if (!contentIdFromNote || Number.isNaN(noteIndex)) {
          throw new Error("Invalid noteId format")
        }

        const updated = await updateNote(db, contentIdFromNote, authSession.user.id, noteIndex, input.content)

        await trackAnalyticsEvent(db, {
          userId: authSession.user.id,
          eventType: "interaction",
          contentId: contentIdFromNote,
          metadata: {
            action: "note_updated",
            noteIndex,
          },
        })

        return Response.json({ success: true, data: updated })
      }

      case "delete_note": {
        // Parse noteId formatted as `${contentId}:${index}`
        const [contentIdFromNote, noteIndexStr] = input.noteId.split(":")
        const noteIndex = parseInt(noteIndexStr, 10)
        if (!contentIdFromNote || Number.isNaN(noteIndex)) {
          throw new Error("Invalid noteId format")
        }

        const updated = await deleteNote(db, contentIdFromNote, authSession.user.id, noteIndex)

        await trackAnalyticsEvent(db, {
          userId: authSession.user.id,
          eventType: "interaction",
          contentId: contentIdFromNote,
          metadata: {
            action: "note_deleted",
            noteIndex,
          },
        })

        return Response.json({ success: true, data: updated })
      }

      default:
        return Response.json({ success: false, error: "Invalid action" }, { status: 400 });
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return Response.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }
    
    console.error("Error processing progress action:", error);
    return Response.json(
      { success: false, error: "Failed to process request" },
      { status: 500 }
    );
  }
}