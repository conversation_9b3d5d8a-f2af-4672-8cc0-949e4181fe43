import type { LoaderFunctionArgs, MetaFunction } from "react-router";
import { <PERSON> } from "react-router";
import { redirect } from "react-router";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { getSession } from "~/lib/auth/supabase-server";

export const meta: MetaFunction = () => {
  return [
    { title: "Kwaci Learning" },
    { name: "description", content: "Welcome to Kwaci Learning!" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  
  if (session) {
    return redirect("/dashboard");
  }
  
  return null;
}

export default function Index() {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center space-y-6">
          <h1 className="text-4xl font-bold text-foreground">
            Welcome to Kwaci Learning
          </h1>
          <p className="text-xl text-muted-foreground">
            A Remix.js app with TypeScript, Tailwind CSS, and shadcn/ui
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Remix.js</CardTitle>
              <CardDescription>
                Full-stack web framework focused on web standards
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Built with React Router and modern web APIs for fast, resilient user experiences.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>TypeScript</CardTitle>
              <CardDescription>
                Strongly typed programming language
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Adds static type definitions to JavaScript for better developer experience.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Tailwind CSS</CardTitle>
              <CardDescription>
                Utility-first CSS framework
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Rapidly build modern websites without ever leaving your HTML.
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>shadcn/ui</CardTitle>
              <CardDescription>
                Beautifully designed components
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Copy and paste components built with Radix UI and Tailwind CSS.
              </p>
            </CardContent>
          </Card>
        </div>
        
        <div className="flex justify-center space-x-4">
          <Button asChild>
            <Link to="/register">Get Started</Link>
          </Button>
          <Button variant="outline" asChild>
            <Link to="/login">Sign In</Link>
          </Button>
        </div>
        
        <div className="mt-8 p-4 bg-primary/10 border border-primary/30 rounded-lg">
          <p className="text-primary font-semibold">
            ✅ Tailwind CSS v4 is working! This box has semantic styling.
          </p>
        </div>
      </div>
    </div>
  );
}