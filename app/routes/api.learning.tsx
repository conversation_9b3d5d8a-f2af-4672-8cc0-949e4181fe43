import { type ActionFunctionArgs, type LoaderFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import {
  saveLearningContent,
  getUserLearningContent,
  searchLearningContent,
  getLearningContentStats
} from "~/db/services/learning-content";
import {
  type LearningContent,
  type NewLearningContent,
  type LearningContentListResponse,
  type LearningContentStatsResponse
} from "~/db/schema/learning-content";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { log } from "~/lib/logger";

// Validation schemas with proper typing
const stepBlockSchema = z.object({
  id: z.string(),
  type: z.string(),
  data: z.any(), // Block data varies by type - required to match DB schema
  isEditing: z.boolean().optional(),
});

const stepSchema = z.object({
  id: z.string(),
  title: z.string(),
  icon: z.string().optional(),
  blocks: z.array(stepBlockSchema),
});

const contentDataSchema = z.object({
  steps: z.array(stepSchema),
  estimatedReadingTime: z.number().optional(),
  metadata: z.any().optional(),
});

const aiMetadataSchema = z.object({
  aiModel: z.string(),
  generatedAt: z.string(),
  contentTypes: z.array(z.string()),
  learningLevel: z.string(),
  topic: z.string(),
  preferredContentTypes: z.array(z.string()).optional(),
});

const createContentSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),
  content: contentDataSchema,
  contentType: z.enum(["standard", "kwaci-primer"]).default("standard"),
  learningLevel: z.enum(["beginner", "intermediate", "advanced"]).default("beginner"),
  isPublic: z.boolean().default(true),
  aiMetadata: aiMetadataSchema.optional(),
});

const searchSchema = z.object({
  query: z.string().optional(),
  contentType: z.enum(["standard", "kwaci-primer"]).optional(),
  learningLevel: z.enum(["beginner", "intermediate", "advanced"]).optional(),
  isPublic: z.boolean().optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
});

// GET /api/learning - Search and list learning content
export async function loader({ request }: LoaderFunctionArgs) {
  const authSession = await requireAuthSession(request);
  const url = new URL(request.url);
  
  // Parse query parameters
  const queryParams = Object.fromEntries(url.searchParams);
  const searchParams = searchSchema.parse({
    ...queryParams,
    limit: queryParams.limit ? parseInt(queryParams.limit) : 20,
    offset: queryParams.offset ? parseInt(queryParams.offset) : 0,
    isPublic: queryParams.isPublic ? queryParams.isPublic === 'true' : undefined,
  });

  try {
    if (searchParams.query) {
      // Search learning content
      const results = await searchLearningContent(db, {
        query: searchParams.query,
        contentType: searchParams.contentType,
        learningLevel: searchParams.learningLevel,
        isPublic: searchParams.isPublic,
        limit: searchParams.limit,
        offset: searchParams.offset,
      });
      return Response.json({
        success: true,
        data: results
      } satisfies { success: true; data: LearningContentListResponse });
    } else {
      // Get user's learning content
      const content = await getUserLearningContent(db, authSession.user.id, {
        contentType: searchParams.contentType,
        learningLevel: searchParams.learningLevel,
        limit: searchParams.limit,
        offset: searchParams.offset,
      });
      return Response.json({
        success: true,
        data: content
      } satisfies { success: true; data: LearningContentListResponse });
    }
  } catch (error) {
    if (error instanceof Response && error.status === 401) return error;
    log.error('Error fetching learning content', { error: error instanceof Error ? error.message : String(error) });
    return Response.json(
      { success: false, error: "Failed to fetch learning content" },
      { status: 500 }
    );
  }
}

// POST /api/learning - Create new learning content
export async function action({ request }: ActionFunctionArgs) {
  const requestId = Math.random().toString(36).substring(7);
  log.info('API Learning Content - Request initiated', { requestId });
  
  const authSession = await requireAuthSession(request);
  
  if (request.method !== "POST") {
    log.warn('API Learning Content - Method not allowed', { requestId, method: request.method });
    return Response.json({ success: false, error: "Method not allowed" }, { status: 405 });
  }

  try {
    const body = await request.json();
    log.info('API Learning Content - Request body received', {
      requestId,
      hasTitle: !!body.title,
      hasContent: !!body.content,
      hasSteps: !!body.content?.steps,
      stepsCount: body.content?.steps?.length || 0,
      contentType: body.contentType,
      learningLevel: body.learningLevel,
      bodyKeys: Object.keys(body)
    });
    
    const input = createContentSchema.parse(body);
    log.success('API Learning Content - Validation passed', { requestId });

    // Extract steps from content object and map to database schema
    const contentData = {
      title: input.title,
      description: input.description || '',
      steps: input.content.steps, // Extract steps from content (content is required)
      learningLevel: input.learningLevel,
      estimatedReadingTime: input.content.estimatedReadingTime || 5,
      contentType: input.contentType,
      isPublic: input.isPublic,
      aiMetadata: input.aiMetadata,
      userId: authSession.user.id, // Fixed: use userId instead of authorId
    };

    log.info('API Learning Content - Calling saveLearningContent', {
      requestId,
      title: contentData.title,
      stepsCount: contentData.steps.length,
      contentType: contentData.contentType,
      learningLevel: contentData.learningLevel,
      userId: contentData.userId
    });
    
    const newContent = await saveLearningContent(db, contentData as Omit<NewLearningContent, 'id' | 'createdAt' | 'updatedAt'>);
    
    log.success('API Learning Content - Content saved successfully', {
      requestId,
      id: newContent.id,
      title: newContent.title
    });

    return Response.json({
      success: true,
      data: newContent
    } satisfies { success: true; data: LearningContent }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      log.error('API Learning Content - Validation failed', {
        requestId,
        errors: error.errors,
        formattedErrors: error.format()
      });
      return Response.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }
    
    log.error('API Learning Content - Error creating content', {
      requestId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      type: typeof error
    });
    return Response.json(
      { success: false, error: "Failed to create learning content" },
      { status: 500 }
    );
  }
}