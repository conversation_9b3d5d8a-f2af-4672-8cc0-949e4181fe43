import type { ActionFunctionArgs } from "react-router";
import { z } from "zod";
import { db } from "~/db";
import { requireAuthSession } from "~/lib/auth/supabase-server";
import { quizAttempt, quiz } from "~/db/schema";
import { and, eq, sql } from "drizzle-orm";
import { trackAnalyticsEvent } from "~/db/services/analytics";
import { getOrCreateSessionId } from "~/lib/utils/session";
import { optimizedRefreshLeaderboard } from "~/lib/utils/materialized-view-refresh";

const schema = z.object({ attemptId: z.string() });

type AnyQuestion = {
  id: string;
  type: string;
  points?: number;
  correctAnswerIndex?: number;
  correctAnswer?: boolean;
  acceptableAnswers?: string[];
  blanks?: Array<{
    position: number;
    correctAnswer: string;
    acceptableAnswers?: string[];
    caseSensitive?: boolean;
  }>;
  correctPairs?: [string, string][];
  correctOrder?: number[];
  items?: string[];
  sampleAnswer?: string;
  correctFeedback?: string;
  incorrectFeedback?: string;
  explanation?: string;
  [key: string]: unknown;
};

// POST /api/quiz-attempts/complete
export async function action({ request }: ActionFunctionArgs) {
  const auth = await requireAuthSession(request);
  const input = schema.parse(await request.json());

  // Verify attempt belongs to user
  const rows = await db
    .select()
    .from(quizAttempt)
    .where(and(eq(quizAttempt.id, input.attemptId), eq(quizAttempt.userId, auth.user.id)))
    .limit(1);

  if (rows.length === 0) return Response.json({ success: false, error: "Attempt not found" }, { status: 404 });
  const attempt = rows[0];
  if (attempt.isCompleted) return Response.json({ success: true, data: attempt });

  // Load quiz questions for evaluation
  const quizRows = await db.select().from(quiz).where(eq(quiz.id, attempt.quizId)).limit(1);
  if (quizRows.length === 0) return Response.json({ success: false, error: "Quiz not found" }, { status: 404 });

  // Evaluate answers (basic objective + simple heuristics; AI integration can be added later)
  const questions = (quizRows[0].questions || []) as AnyQuestion[];
  const answers = Array.isArray(attempt.answers) ? attempt.answers : [];
  const showCorrectAnswers = quizRows[0].showCorrectAnswers || false;

  // Helper function to get appropriate feedback
  const getFeedback = (question: AnyQuestion, isCorrect: boolean): string | null => {
    // If explanations are disabled, return null
    if (!showCorrectAnswers) {
      return null;
    }
    
    // If explanations are enabled, prefer explanation over feedback
    if (question.explanation) {
      return question.explanation;
    }
    
    // Fallback to correctFeedback/incorrectFeedback
    return isCorrect ? (question.correctFeedback || 'Correct!') : (question.incorrectFeedback || 'Incorrect.');
  };

  // Simple evaluator mirroring libs/ai behavior for objective types; freeText gets partial credit if non-empty
  let totalPoints = 0;
  let earnedPoints = 0;
  const questionResults: Array<{ questionId: string; isCorrect: boolean; pointsEarned: number; feedback?: string | null }>= [];

  const qMap = new Map(questions.map((q) => [q.id, q]));

  for (const q of questions) totalPoints += (q.points || 0);

  for (const a of answers as Array<{ questionId: string; answer: any }>) {
    const q = qMap.get(a.questionId) as AnyQuestion | undefined;
    if (!q) continue;
    let isCorrect = false;
    let points = 0;
    let feedback: string | null = '';
    switch (q.type) {
      case 'multipleChoice':
        isCorrect = typeof a.answer === 'number' && a.answer === q.correctAnswerIndex;
        points = isCorrect ? (q.points || 0) : 0;
        feedback = getFeedback(q, isCorrect);
        break;
      case 'trueFalse':
        isCorrect = typeof a.answer === 'boolean' && a.answer === q.correctAnswer;
        points = isCorrect ? (q.points || 0) : 0;
        feedback = getFeedback(q, isCorrect);
        break;
      case 'fillInBlank':
        // For fill-in-blank, validate each blank answer
        if (Array.isArray(a.answer) && Array.isArray(q.blanks)) {
          isCorrect = q.blanks.every((blank: any, index: number) => {
            const userAnswer = a.answer[index]?.trim() || '';
            
            if (blank.caseSensitive) {
              if (userAnswer === blank.correctAnswer) return true;
              if (blank.acceptableAnswers?.includes(userAnswer)) return true;
            } else {
              if (userAnswer.toLowerCase() === blank.correctAnswer.toLowerCase()) return true;
              if (blank.acceptableAnswers?.some((answer: string) => 
                answer.toLowerCase() === userAnswer.toLowerCase()
              )) return true;
            }
            
            return false;
          });
        } else {
          isCorrect = false;
        }
        points = isCorrect ? (q.points || 0) : 0;
        feedback = getFeedback(q, isCorrect);
        break;
      case 'matching':
        isCorrect = Array.isArray(a.answer) && Array.isArray(q.correctPairs) && a.answer.length === q.correctPairs.length && a.answer.every((pair: any, i: number) => pair[1] === q.correctPairs![i][1]);
        points = isCorrect ? (q.points || 0) : 0;
        feedback = getFeedback(q, isCorrect);
        break;
      case 'ordering':
        isCorrect = Array.isArray(a.answer) && Array.isArray(q.correctOrder) && a.answer.length === q.correctOrder.length && a.answer.every((v: any, i: number) => v === q.correctOrder![i]);
        points = isCorrect ? (q.points || 0) : 0;
        feedback = getFeedback(q, isCorrect);
        break;
      case 'flashcard':
        isCorrect = a.answer === 'correct';
        points = isCorrect ? (q.points || 0) : 0;
        feedback = getFeedback(q, isCorrect);
        break;
      case 'freeText':
        if (typeof a.answer === 'string' && a.answer.trim().length > 10) {
          isCorrect = true; // heuristic
          points = Math.round((q.points || 0) * 0.6);
          feedback = showCorrectAnswers ? getFeedback(q, true) : 'Answer recorded; partial credit awarded.';
        } else {
          isCorrect = false;
          points = 0;
          feedback = showCorrectAnswers ? getFeedback(q, false) : 'Please provide a more detailed answer.';
        }
        break;
      default:
        isCorrect = false;
        points = 0;
        feedback = showCorrectAnswers ? null : 'Unknown question type.';
    }
    earnedPoints += points;
    questionResults.push({ 
      questionId: a.questionId, 
      isCorrect, 
      pointsEarned: points, 
      ...(feedback !== null && { feedback })
    });
  }

  const percentage = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0;

  const completed = await db
    .update(quizAttempt)
    .set({
      isCompleted: true,
      completedAt: new Date(),
      score: { totalPoints, earnedPoints, percentage, correctAnswers: questionResults.filter(r => r.isCorrect).length, totalQuestions: questions.length },
      questionResults,
    })
    .where(eq(quizAttempt.id, attempt.id))
    .returning();

  // Track quiz completion analytics event
  try {
    await trackAnalyticsEvent(db, {
      contentId: quizRows[0].learningContentId,
      userId: auth.user.id,
      eventType: 'complete',
      sessionId: getOrCreateSessionId(),
      timeSpent: Math.floor((new Date().getTime() - new Date(attempt.startedAt).getTime()) / 1000),
      completionPercentage: 100,
      metadata: {
          action: 'quiz_completed'
        }
    });
  } catch (error) {
    console.warn('Failed to track quiz completion analytics:', error);
  }

  // Refresh the leaderboard materialized view after quiz completion
  try {
    await optimizedRefreshLeaderboard(db);
  } catch (error) {
    // Log error but don't fail the request - leaderboard refresh is not critical
    console.warn('Failed to refresh leaderboard materialized view:', error);
  }

  return Response.json({ success: true, data: completed });
}

