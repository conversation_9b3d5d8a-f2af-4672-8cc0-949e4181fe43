import type { LoaderFunctionArgs } from 'react-router';
import { useParams, useLoaderData } from 'react-router';
import { useQuery } from '@tanstack/react-query';
import { createQuery<PERSON><PERSON>, apiClient } from '~/lib/data-fetching';
import type { LearningContent } from '~/db/schema/learning-content';
import { useLearningProgress } from '~/lib/hooks/use-hybrid-state';
import { useUIStore } from '~/lib/stores/ui-store';
import { Button } from '~/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Progress } from '~/components/ui/progress';
import { showSuccessToast } from '~/lib/error-handling';

// React Router 7 loader with authentication and error handling
export async function loader({ params, request }: LoaderFunctionArgs) {
  // Client-side auth handled via AuthProvider; do not redirect here to avoid loops
  const contentId = params.id;
  if (!contentId) {
    throw new Response('Content ID is required', { status: 400 });
  }

  // Mock learning content for example (replace with actual data fetching)
  const content = {
    id: contentId,
    title: 'Example Learning Content',
    description: 'This is an example learning content for demonstration.',
    steps: [
      {
        id: '1',
        title: 'Introduction',
        blocks: [
          {
            id: 'block-1',
            type: 'paragraph',
            data: 'Learn the basics of this topic.'
          }
        ]
      },
      {
        id: '2',
        title: 'Practice',
        blocks: [
          {
            id: 'block-2',
            type: 'paragraph',
            data: 'Apply what you have learned.'
          }
        ]
      },
      {
        id: '3',
        title: 'Assessment',
        blocks: [
          {
            id: 'block-3',
            type: 'paragraph',
            data: 'Test your understanding.'
          }
        ]
      },
    ],
  };
  
  return Response.json({
    content,
    userId: 'example-user',
    contentId,
  });
}

// Example component using hybrid state management
export default function LearningContentPage() {
  const params = useParams();
  const contentId = params.id!;
  
  // 1. Server state from React Router loader (SSR)
  const loaderData = useLoaderData<{
    content: any;
    userId: string;
    contentId: string;
  }>();
  
  // 2. Hybrid state management for learning progress
  const {
    progress,
    currentStep,
    progressPanelOpen,
    updateProgress,
    isUpdating,
    setCurrentStep,
    toggleProgressPanel,
    refetch: refetchProgress,
  } = useLearningProgress(loaderData?.userId || '', contentId);
  
  // 3. UI-only state from Zustand
  const {
    learning: { notesPanelOpen, bookmarkedSections },
    toggleNotesPanel,
    addBookmark,
    removeBookmark,
  } = useUIStore();
  
  // 4. Enhanced client-side data with React Query using typed API client
  const { data: enhancedContent, isLoading: isLoadingEnhanced } = useQuery({
    queryKey: createQueryKey('learning-content-enhanced', { contentId }),
    queryFn: async () => {
      // ✅ Use typed API client instead of raw fetch
      return await apiClient.get<LearningContent>(`/learning/${contentId}/enhanced`);
    },
    enabled: !!contentId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
  
  // 5. Real-time analytics with React Query
  const { data: analytics } = useQuery({
    queryKey: createQueryKey('learning-analytics', { contentId, userId: loaderData?.userId }),
    queryFn: async () => {
      return await apiClient.getLearningAnalytics(contentId, loaderData?.userId);
    },
    enabled: !!contentId && !!loaderData?.userId,
    refetchInterval: 30 * 1000, // Refetch every 30 seconds
    staleTime: 15 * 1000, // 15 seconds
  });
  
  // Handle step completion
  const handleStepComplete = async (stepIndex: number) => {
    try {
      await updateProgress({
        step: stepIndex,
        completed: true,
        timeSpent: Date.now() - (progress?.startTime || Date.now()),
      });
      
      setCurrentStep(stepIndex + 1);
      showSuccessToast(`Step ${stepIndex + 1} completed!`);
    } catch (error) {
      console.error('Failed to update progress:', error);
    }
  };
  
  // Handle bookmark toggle
  const handleBookmarkToggle = (sectionId: string) => {
    const isBookmarked = bookmarkedSections.includes(sectionId);
    if (isBookmarked) {
      removeBookmark(sectionId);
      showSuccessToast('Bookmark removed');
    } else {
      addBookmark(sectionId);
      showSuccessToast('Bookmark added');
    }
  };
  
  // Error handling is now done at the route level
  
  if (!loaderData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }
  
  const { content } = loaderData;
  const progressPercentage = progress ? (progress.completedSteps / progress.totalSteps) * 100 : 0;
  
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Content Area */}
        <div className="lg:col-span-3">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-2xl">{content.title}</CardTitle>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleBookmarkToggle(content.id)}
                  >
                    {bookmarkedSections.includes(content.id) ? '★' : '☆'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={toggleNotesPanel}
                  >
                    Notes {notesPanelOpen ? '📝' : '📄'}
                  </Button>
                </div>
              </div>
              
              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Progress</span>
                  <span>{Math.round(progressPercentage)}%</span>
                </div>
                <Progress value={progressPercentage} className="w-full" />
              </div>
            </CardHeader>
            
            <CardContent>
              {/* Content Steps */}
              <div className="space-y-6">
                {content.steps?.map((step: any, index: number) => (
                  <div
                    key={step.id}
                    className={`p-4 rounded-lg border ${
                      index === currentStep
                        ? 'border-primary bg-primary/5'
                        : index < currentStep
                        ? 'border-green-500 bg-green-50'
                        : 'border-muted'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-semibold">Step {index + 1}: {step.title}</h3>
                      {index < currentStep && (
                        <span className="text-green-600 text-sm">✓ Completed</span>
                      )}
                    </div>
                    
                    <p className="text-muted-foreground mb-4">{step.description}</p>
                    
                    {/* Enhanced content from React Query */}
                    {enhancedContent?.steps?.[index] && (
                      <div className="bg-blue-50 p-3 rounded border-l-4 border-blue-500 mb-4">
                        <p className="text-sm text-blue-800">
                          💡 {enhancedContent.steps[index].title}
                        </p>
                      </div>
                    )}
                    
                    {index === currentStep && (
                      <Button
                        onClick={() => handleStepComplete(index)}
                        disabled={isUpdating}
                        className="mt-4"
                      >
                        {isUpdating ? 'Saving...' : 'Complete Step'}
                      </Button>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
        
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <div className="space-y-4">
            {/* Progress Panel */}
            {progressPanelOpen && (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">Your Progress</CardTitle>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={toggleProgressPanel}
                    >
                      ×
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>Steps Completed</span>
                      <span>{progress?.completedSteps || 0}/{progress?.totalSteps || 0}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Time Spent</span>
                      <span>{Math.round((progress?.timeSpent || 0) / 60)}m</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Current Step</span>
                      <span>{currentStep + 1}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
            
            {/* Analytics Panel */}
            {analytics && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Learning Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span>Avg. Completion Time</span>
                      <span>{analytics.avgCompletionTime}m</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Success Rate</span>
                      <span>{analytics.successRate}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Difficulty Level</span>
                      <span>{analytics.difficultyLevel}/5</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
            
            {/* Notes Panel */}
            {notesPanelOpen && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Notes</CardTitle>
                </CardHeader>
                <CardContent>
                  <textarea
                    className="w-full h-32 p-2 border rounded resize-none"
                    placeholder="Take notes while learning..."
                  />
                  <Button size="sm" className="mt-2 w-full">
                    Save Notes
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}