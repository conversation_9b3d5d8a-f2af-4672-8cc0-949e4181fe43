import { useState, useEffect } from 'react';
import { decryptQuestionAnswers, decryptAnswerFields } from '~/lib/crypto.client';
import type { Quiz, QuizQuestion } from '~/components/quiz/types';

// Function to decrypt question using server-side API
async function decryptQuestionViaAPI(question: QuizQuestion): Promise<QuizQuestion> {
  try {
    const response = await fetch('/api/decrypt', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        data: question,
        type: 'question'
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Decryption failed');
    }

    return result.data.decrypted;
  } catch (error) {
    console.error('Failed to decrypt question via API:', error);
    // Return original question if API call fails
    return question;
  }
}

// Hook to decrypt quiz questions
export function useQuizDecryption(quiz: Quiz | null) {
  const [decryptedQuiz, setDecryptedQuiz] = useState<Quiz | null>(null);
  const [isDecrypting, setIsDecrypting] = useState(false);
  const [decryptionError, setDecryptionError] = useState<string | null>(null);

  useEffect(() => {
    if (!quiz) {
      setDecryptedQuiz(null);
      return;
    }

    const decryptQuiz = async () => {
      setIsDecrypting(true);
      setDecryptionError(null);

      try {
        const decryptedQuestions = await Promise.all(
          quiz.questions.map(async (question: QuizQuestion) => {
            return await decryptQuestionAnswers(question, quiz.id);
          })
        );

        setDecryptedQuiz({
          ...quiz,
          questions: decryptedQuestions
        });
      } catch (error) {
        console.error('Failed to decrypt quiz:', error);
        setDecryptionError('Failed to decrypt quiz data');
        // Fallback to original quiz if decryption fails
        setDecryptedQuiz(quiz);
      } finally {
        setIsDecrypting(false);
      }
    };

    decryptQuiz();
  }, [quiz]);

  return {
    decryptedQuiz,
    isDecrypting,
    decryptionError
  };
}

// Hook to decrypt answer fields from submit-answer response
export function useAnswerDecryption() {
  const decryptAnswers = async (answerFields: Record<string, any>, quizId: string) => {
    try {
      return await decryptAnswerFields(answerFields, quizId);
    } catch (error) {
      console.error('Failed to decrypt answer fields:', error);
      // Return original data if decryption fails
      return answerFields;
    }
  };

  return { decryptAnswers };
}

// Hook to handle quiz data with decryption state
export function useDecryptedQuizData(quiz: Quiz | null) {
  const { decryptedQuiz, isDecrypting, decryptionError } = useQuizDecryption(quiz);
  const { decryptAnswers } = useAnswerDecryption();

  return {
    quiz: decryptedQuiz,
    isDecrypting,
    decryptionError,
    decryptAnswers
  };
}