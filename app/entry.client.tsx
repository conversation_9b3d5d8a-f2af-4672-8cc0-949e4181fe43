import { HydratedRouter } from "react-router/dom";
import { startTransition, StrictMode } from "react";
import { hydrateRoot } from "react-dom/client";

// Polyfill Buffer for browser usage if required by some dependencies
async function setupBuffer() {
  try {
    // Lazy import to avoid bundling cost if not needed
    const { <PERSON><PERSON><PERSON> } = await import('buffer');
    // @ts-ignore
    if (typeof window !== 'undefined' && !(window as any).Buffer) {
      // @ts-ignore
      (window as any).Buffer = Buffer;
    }
  } catch (_) {
    // ignore if buffer cannot be loaded; most environments won't need it
  }
}

// Setup buffer polyfill and then hydrate
setupBuffer().then(() => {
  startTransition(() => {
    hydrateRoot(
      document,
      <StrictMode>
        <HydratedRouter />
      </StrictMode>,
    );
  });
});