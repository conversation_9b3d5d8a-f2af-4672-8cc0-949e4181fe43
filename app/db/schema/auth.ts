import { pgTable, text, timestamp, boolean, integer } from "drizzle-orm/pg-core";

/**
 * User profile table
 *
 * Note: This table is now used for application user profiles and data relationships.
 * Authentication is handled by Supabase Auth (auth.users table).
 * The user.id should match the Supabase Auth user.id for data consistency.
 */
export const user = pgTable("user", {
					id: text('id').primaryKey(), // Should match Supabase Auth user.id
					name: text('name').notNull(),
 email: text('email').notNull().unique(), // Should match Supabase Auth user.email
 emailVerified: boolean('email_verified').$defaultFn(() => false).notNull(),
 image: text('image'),
 createdAt: timestamp('created_at').$defaultFn(() => /* @__PURE__ */ new Date()).notNull(),
 updatedAt: timestamp('updated_at').$defaultFn(() => /* @__PURE__ */ new Date()).notNull(),
 avatar: text('avatar')
				});

// Note: session, account, and verification tables have been removed
// as they are no longer needed with Supabase Auth integration.
// Supabase Auth handles sessions, OAuth accounts, and email verification.