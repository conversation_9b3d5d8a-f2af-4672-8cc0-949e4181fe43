import { pgTable, text, timestamp, json, index } from "drizzle-orm/pg-core";
import { conversations } from "./chat";

/**
 * Conversation Memory table
 * Stores persistent memory information for conversations to maintain context across sessions
 */
export const conversationMemory = pgTable("conversation_memory", {
  id: text('id').primaryKey(),

  // Conversation this memory belongs to
  conversationId: text('conversation_id').notNull().references(() => conversations.id, { onDelete: 'cascade' }),

  // Key topics discussed in the conversation
  keyTopics: json('key_topics').$type<string[]>().notNull().default([]),

  // User preferences and learning patterns
  userPreferences: json('user_preferences').$type<Record<string, any>>().notNull().default({}),

  // Proposed topics and their status
  proposedTopics: json('proposed_topics').$type<Array<{
    topic: string;
    status: 'proposed' | 'accepted' | 'rejected';
    timestamp: string; // ISO date string
  }>>().notNull().default([]),

  // Context summary for the conversation
  contextSummary: text('context_summary').notNull().default(''),

  // Memory extraction metadata
  extractionMetadata: json('extraction_metadata').$type<{
    lastMessageCount?: number;
    extractionMethod?: string;
    confidence?: number;
  }>(),

  // Timestamps
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  // Indexes for performance
  conversationIdx: index('conversation_memory_conversation_idx').on(table.conversationId),
  updatedAtIdx: index('conversation_memory_updated_at_idx').on(table.updatedAt),
}));

// Export types for TypeScript inference
export type ConversationMemory = typeof conversationMemory.$inferSelect;
export type NewConversationMemory = typeof conversationMemory.$inferInsert;