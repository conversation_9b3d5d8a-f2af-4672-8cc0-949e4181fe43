/**
 * Leaderboard Schema
 *
 * Note: The user_points_leaderboard_mv is a materialized view created manually
 * in the database using SQL scripts. It's not managed by Drizzle migrations
 * to avoid conflicts and allow for complex aggregation logic.
 *
 * The materialized view is created by running:
 * create_leaderboard_materialized_view.sql
 */

// Type definition for the materialized view (for TypeScript inference)
// This matches the structure of the materialized view created by SQL
export type UserLeaderboard = {
  user_id: string;
  user_name: string | null;
  user_email: string | null;
  total_points: number;
  quiz_points: number;
  progress_points: number;
  analytics_points: number;
  rank: number;
  last_updated: Date;
};