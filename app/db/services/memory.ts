/**
 * Memory database service
 * Provides functions to manage conversation memory for persistent context retention
 */

import { eq, and, desc } from 'drizzle-orm';
import {
  conversationMemory,
  type ConversationMemory,
  type NewConversationMemory,
} from '../schema/memory';
import type { Database } from '../connection';
import { log } from '~/lib/logger';

/**
 * Create or update conversation memory
 */
export async function upsertConversationMemory(
  db: Database,
  data: {
    conversationId: string;
    keyTopics: string[];
    userPreferences: Record<string, any>;
    proposedTopics: Array<{
      topic: string;
      status: 'proposed' | 'accepted' | 'rejected';
      timestamp: string;
    }>;
    contextSummary: string;
    extractionMetadata?: {
      lastMessageCount?: number;
      extractionMethod?: string;
      confidence?: number;
    };
  }
): Promise<ConversationMemory> {
  const memoryId = crypto.randomUUID();
  const now = new Date();

  // Check if memory already exists
  const existingMemory = await getConversationMemory(db, data.conversationId);

  if (existingMemory) {
    // Update existing memory
    const [updatedMemory] = await db
      .update(conversationMemory)
      .set({
        keyTopics: data.keyTopics,
        userPreferences: data.userPreferences,
        proposedTopics: data.proposedTopics,
        contextSummary: data.contextSummary,
        extractionMetadata: data.extractionMetadata,
        updatedAt: now,
      })
      .where(eq(conversationMemory.conversationId, data.conversationId))
      .returning();

    log.info('Updated conversation memory', {
      conversationId: data.conversationId,
      keyTopicsCount: data.keyTopics.length,
      proposedTopicsCount: data.proposedTopics.length,
    });

    return updatedMemory;
  } else {
    // Create new memory
    const newMemory: NewConversationMemory = {
      id: memoryId,
      conversationId: data.conversationId,
      keyTopics: data.keyTopics,
      userPreferences: data.userPreferences,
      proposedTopics: data.proposedTopics,
      contextSummary: data.contextSummary,
      extractionMetadata: data.extractionMetadata,
      createdAt: now,
      updatedAt: now,
    };

    const [memory] = await db
      .insert(conversationMemory)
      .values(newMemory)
      .returning();

    log.info('Created conversation memory', {
      conversationId: data.conversationId,
      keyTopicsCount: data.keyTopics.length,
      proposedTopicsCount: data.proposedTopics.length,
    });

    return memory;
  }
}

/**
 * Get conversation memory by conversation ID
 */
export async function getConversationMemory(
  db: Database,
  conversationId: string
): Promise<ConversationMemory | null> {
  const [memory] = await db
    .select()
    .from(conversationMemory)
    .where(eq(conversationMemory.conversationId, conversationId))
    .limit(1);

  return memory || null;
}

/**
 * Get all conversation memories for a user (via conversation ownership)
 */
export async function getUserConversationMemories(
  db: Database,
  userId: string,
  options: {
    limit?: number;
    offset?: number;
  } = {}
): Promise<ConversationMemory[]> {
  const { limit = 20, offset = 0 } = options;

  // Join with conversations table to filter by user
  const memories = await db
    .select({
      id: conversationMemory.id,
      conversationId: conversationMemory.conversationId,
      keyTopics: conversationMemory.keyTopics,
      userPreferences: conversationMemory.userPreferences,
      proposedTopics: conversationMemory.proposedTopics,
      contextSummary: conversationMemory.contextSummary,
      extractionMetadata: conversationMemory.extractionMetadata,
      createdAt: conversationMemory.createdAt,
      updatedAt: conversationMemory.updatedAt,
    })
    .from(conversationMemory)
    .innerJoin(
      // Import conversations from chat schema
      (await import('../schema/chat')).conversations,
      eq(conversationMemory.conversationId, (await import('../schema/chat')).conversations.id)
    )
    .where(eq((await import('../schema/chat')).conversations.userId, userId))
    .orderBy(desc(conversationMemory.updatedAt))
    .limit(limit)
    .offset(offset);

  return memories;
}

/**
 * Delete conversation memory
 */
export async function deleteConversationMemory(
  db: Database,
  conversationId: string
): Promise<boolean> {
  try {
    await db
      .delete(conversationMemory)
      .where(eq(conversationMemory.conversationId, conversationId));

    log.info('Deleted conversation memory', {
      conversationId,
    });

    return true;
  } catch (error) {
    log.error('Failed to delete conversation memory', {
      conversationId,
      error: error instanceof Error ? error.message : String(error),
    });
    return false;
  }
}

/**
 * Update conversation memory topics status
 */
export async function updateProposedTopicStatus(
  db: Database,
  conversationId: string,
  topic: string,
  status: 'accepted' | 'rejected'
): Promise<ConversationMemory | null> {
  try {
    const memory = await getConversationMemory(db, conversationId);
    
    if (!memory) {
      return null;
    }

    // Update the proposed topics array
    const updatedProposedTopics = memory.proposedTopics.map(proposedTopic => {
      if (proposedTopic.topic === topic && proposedTopic.status === 'proposed') {
        return {
          ...proposedTopic,
          status,
          timestamp: new Date().toISOString(),
        };
      }
      return proposedTopic;
    });

    const [updatedMemory] = await db
      .update(conversationMemory)
      .set({
        proposedTopics: updatedProposedTopics,
        updatedAt: new Date(),
      })
      .where(eq(conversationMemory.conversationId, conversationId))
      .returning();

    log.info('Updated proposed topic status', {
      conversationId,
      topic,
      status,
    });

    return updatedMemory;
  } catch (error) {
    log.error('Failed to update proposed topic status', {
      conversationId,
      topic,
      status,
      error: error instanceof Error ? error.message : String(error),
    });
    return null;
  }
}