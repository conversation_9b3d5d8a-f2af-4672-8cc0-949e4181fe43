/**
 * Chat database service
 * Provides functions to manage conversations and messages for RAG-powered chat functionality
 */

import { eq, and, desc, asc } from 'drizzle-orm';
import {
  conversations,
  messages,
  contentEmbeddings,
  type Conversation,
  type NewConversation,
  type Message,
  type NewMessage,
  type ContentEmbedding,
} from '../schema/chat';
import type { Database } from '../connection';
import { log } from '~/lib/logger';

export interface ConversationWithMessages extends Conversation {
  messages: Message[];
  messageCount?: number;
}

export interface CreateConversationOptions {
  title?: string;
  generateTitle?: boolean;
}

export interface MessageWithSources extends Message {
  sources?: Array<{
    stepId: string;
    stepTitle: string;
    chunkIndex: number;
    score: number;
    contentId?: string;
    contentTitle?: string;
  }>;
}

/**
 * Create a new conversation
 */
export async function createConversation(
  db: Database,
  data: {
    userId: string;
    learningContentId: string;
    title?: string;
  }
): Promise<Conversation> {
  const conversationId = crypto.randomUUID();
  const now = new Date();

  const newConversation: NewConversation = {
    id: conversationId,
    userId: data.userId,
    learningContentId: data.learningContentId,
    title: data.title || 'New Conversation',
    createdAt: now,
    updatedAt: now,
  };

  const [conversation] = await db
    .insert(conversations)
    .values(newConversation)
    .returning();

  log.info('Created new conversation', {
    conversationId,
    userId: data.userId,
    learningContentId: data.learningContentId,
    title: conversation.title,
  });

  return conversation;
}

/**
 * Get conversation by ID with access control
 */
export async function getConversation(
  db: Database,
  conversationId: string,
  userId: string
): Promise<Conversation | null> {
  const [conversation] = await db
    .select()
    .from(conversations)
    .where(
      and(
        eq(conversations.id, conversationId),
        eq(conversations.userId, userId)
      )
    )
    .limit(1);

  return conversation || null;
}

/**
 * Get conversation with messages
 */
export async function getConversationWithMessages(
  db: Database,
  conversationId: string,
  userId: string,
  options: {
    limit?: number;
    offset?: number;
    orderBy?: 'asc' | 'desc';
  } = {}
): Promise<ConversationWithMessages | null> {
  const conversation = await getConversation(db, conversationId, userId);
  
  if (!conversation) {
    return null;
  }

  const { limit = 50, offset = 0, orderBy = 'asc' } = options;
  const sortOrder = orderBy === 'asc' ? asc : desc;

  const conversationMessages = await db
    .select()
    .from(messages)
    .where(eq(messages.conversationId, conversationId))
    .orderBy(sortOrder(messages.createdAt))
    .limit(limit)
    .offset(offset);

  return {
    ...conversation,
    messages: conversationMessages,
    messageCount: conversationMessages.length,
  };
}

/**
 * Get user's conversations for a specific learning content
 */
export async function getUserConversationsForContent(
  db: Database,
  userId: string,
  learningContentId: string,
  options: {
    limit?: number;
    offset?: number;
  } = {}
): Promise<ConversationWithMessages[]> {
  const { limit = 20, offset = 0 } = options;

  const userConversations = await db
    .select()
    .from(conversations)
    .where(
      and(
        eq(conversations.userId, userId),
        eq(conversations.learningContentId, learningContentId)
      )
    )
    .orderBy(desc(conversations.updatedAt))
    .limit(limit)
    .offset(offset);

  // Get recent messages for each conversation
  const conversationsWithMessages = await Promise.all(
    userConversations.map(async (conversation) => {
      const recentMessages = await db
        .select()
        .from(messages)
        .where(eq(messages.conversationId, conversation.id))
        .orderBy(desc(messages.createdAt))
        .limit(5);

      return {
        ...conversation,
        messages: recentMessages.reverse(), // Show in chronological order
        messageCount: recentMessages.length,
      };
    })
  );

  return conversationsWithMessages;
}

/**
 * Get all user's conversations across all content
 */
export async function getUserConversations(
  db: Database,
  userId: string,
  options: {
    limit?: number;
    offset?: number;
  } = {}
): Promise<ConversationWithMessages[]> {
  const { limit = 20, offset = 0 } = options;

  const userConversations = await db
    .select()
    .from(conversations)
    .where(eq(conversations.userId, userId))
    .orderBy(desc(conversations.updatedAt))
    .limit(limit)
    .offset(offset);

  // Get recent messages for each conversation
  const conversationsWithMessages = await Promise.all(
    userConversations.map(async (conversation) => {
      const recentMessages = await db
        .select()
        .from(messages)
        .where(eq(messages.conversationId, conversation.id))
        .orderBy(desc(messages.createdAt))
        .limit(3);

      return {
        ...conversation,
        messages: recentMessages.reverse(),
        messageCount: recentMessages.length,
      };
    })
  );

  return conversationsWithMessages;
}

/**
 * Add a message to a conversation
 */
export async function addMessage(
  db: Database,
  data: {
    conversationId: string;
    content: string;
    senderRole: 'user' | 'assistant';
    messageType?: 'text' | 'system';
    metadata?: {
      sources?: Array<{
        stepId: string;
        stepTitle: string;
        chunkIndex: number;
        score: number;
      }>;
      systemContext?: string;
      processingTime?: number;
      model?: string;
    };
  }
): Promise<Message> {
  const messageId = crypto.randomUUID();
  const now = new Date();

  const newMessage: NewMessage = {
    id: messageId,
    conversationId: data.conversationId,
    content: data.content,
    senderRole: data.senderRole,
    messageType: data.messageType || 'text',
    metadata: data.metadata,
    createdAt: now,
  };

  const [message] = await db
    .insert(messages)
    .values(newMessage)
    .returning();

  // Update conversation timestamp
  await db
    .update(conversations)
    .set({ updatedAt: now })
    .where(eq(conversations.id, data.conversationId));

  log.info('Added message to conversation', {
    messageId,
    conversationId: data.conversationId,
    senderRole: data.senderRole,
    contentLength: data.content.length,
    hasSources: !!data.metadata?.sources?.length,
  });

  return message;
}

/**
 * Update conversation title
 */
export async function updateConversationTitle(
  db: Database,
  conversationId: string,
  userId: string,
  title: string
): Promise<Conversation | null> {
  const [updatedConversation] = await db
    .update(conversations)
    .set({
      title,
      updatedAt: new Date(),
    })
    .where(
      and(
        eq(conversations.id, conversationId),
        eq(conversations.userId, userId)
      )
    )
    .returning();

  if (updatedConversation) {
    log.info('Updated conversation title', {
      conversationId,
      newTitle: title,
    });
  }

  return updatedConversation || null;
}

/**
 * Delete a conversation and all its messages
 */
export async function deleteConversation(
  db: Database,
  conversationId: string,
  userId: string
): Promise<boolean> {
  // Verify ownership
  const conversation = await getConversation(db, conversationId, userId);
  if (!conversation) {
    return false;
  }

  // Delete messages first (due to foreign key constraints)
  await db
    .delete(messages)
    .where(eq(messages.conversationId, conversationId));

  // Delete conversation
  await db
    .delete(conversations)
    .where(eq(conversations.id, conversationId));

  log.info('Deleted conversation', {
    conversationId,
    userId,
  });

  return true;
}

/**
 * Get conversation messages with pagination
 */
export async function getConversationMessages(
  db: Database,
  conversationId: string,
  userId: string,
  options: {
    limit?: number;
    offset?: number;
    orderBy?: 'asc' | 'desc';
  } = {}
): Promise<Message[]> {
  // Verify access to conversation
  const conversation = await getConversation(db, conversationId, userId);
  if (!conversation) {
    return [];
  }

  const { limit = 50, offset = 0, orderBy = 'asc' } = options;
  const sortOrder = orderBy === 'asc' ? asc : desc;

  const conversationMessages = await db
    .select()
    .from(messages)
    .where(eq(messages.conversationId, conversationId))
    .orderBy(sortOrder(messages.createdAt))
    .limit(limit)
    .offset(offset);

  return conversationMessages;
}

/**
 * Generate conversation title from first few messages
 */
export async function generateConversationTitle(
  db: Database,
  conversationId: string,
  userId: string
): Promise<string | null> {
  // Get first few messages to generate a title
  const firstMessages = await getConversationMessages(db, conversationId, userId, {
    limit: 3,
    orderBy: 'asc',
  });

  if (firstMessages.length === 0) {
    return null;
  }

  // Simple title generation based on first user message
  const firstUserMessage = firstMessages.find(msg => msg.senderRole === 'user');
  if (firstUserMessage) {
    // Take first few words as title
    const words = firstUserMessage.content.split(' ').slice(0, 6);
    let title = words.join(' ');
    
    // Add ellipsis if truncated
    if (firstUserMessage.content.split(' ').length > 6) {
      title += '...';
    }
    
    // Update the conversation title
    await updateConversationTitle(db, conversationId, userId, title);
    return title;
  }

  return null;
}

/**
 * Get embedding metadata for content chunks (for source attribution)
 */
export async function getContentEmbeddingsByIds(
  db: Database,
  vectorIds: string[]
): Promise<ContentEmbedding[]> {
  if (vectorIds.length === 0) {
    return [];
  }

  // Note: This would require an 'in' operation
  // For now, we'll get them one by one or implement batch query
  const embeddings: ContentEmbedding[] = [];
  
  for (const vectorId of vectorIds) {
    const [embedding] = await db
      .select()
      .from(contentEmbeddings)
      .where(eq(contentEmbeddings.vectorId, vectorId))
      .limit(1);
    
    if (embedding) {
      embeddings.push(embedding);
    }
  }

  return embeddings;
}

/**
 * Create content embedding record
 */
export async function createContentEmbedding(
  db: Database,
  data: {
    learningContentId: string;
    stepId: string;
    stepTitle: string;
    chunkIndex: string;
    chunkText: string;
    vectorId: string;
    embeddingModel?: string;
    embeddingDimension?: string;
    processingMetadata?: {
      chunkingStrategy?: string;
      chunkSize?: number;
      chunkOverlap?: number;
      tokenCount?: number;
    };
  }
): Promise<ContentEmbedding> {
  const embeddingId = crypto.randomUUID();
  const now = new Date();

  const newEmbedding = {
    id: embeddingId,
    learningContentId: data.learningContentId,
    stepId: data.stepId,
    stepTitle: data.stepTitle,
    chunkIndex: data.chunkIndex,
    chunkText: data.chunkText,
    vectorId: data.vectorId,
    embeddingModel: data.embeddingModel || 'voyage-3.5',
    embeddingDimension: data.embeddingDimension || '1024',
    processingMetadata: data.processingMetadata,
    createdAt: now,
    updatedAt: now,
  };

  const [embedding] = await db
    .insert(contentEmbeddings)
    .values(newEmbedding)
    .returning();

  return embedding;
}