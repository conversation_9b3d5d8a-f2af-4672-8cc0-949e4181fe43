CREATE TABLE "conversation_memory" (
	"id" text PRIMARY KEY NOT NULL,
	"conversation_id" text NOT NULL,
	"key_topics" json DEFAULT '[]'::json NOT NULL,
	"user_preferences" json DEFAULT '{}'::json NOT NULL,
	"proposed_topics" json DEFAULT '[]'::json NOT NULL,
	"context_summary" text DEFAULT '' NOT NULL,
	"extraction_metadata" json,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
ALTER TABLE "conversation_memory" ADD CONSTRAINT "conversation_memory_conversation_id_conversations_id_fk" FOREIGN KEY ("conversation_id") REFERENCES "public"."conversations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "conversation_memory_conversation_idx" ON "conversation_memory" USING btree ("conversation_id");--> statement-breakpoint
CREATE INDEX "conversation_memory_updated_at_idx" ON "conversation_memory" USING btree ("updated_at");