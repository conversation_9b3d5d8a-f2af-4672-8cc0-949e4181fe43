{"id": "3c822c08-b987-4b92-b570-eb2c9bb59df1", "prevId": "bd26742c-df14-4c13-8f62-2fc08bcf4ade", "version": "7", "dialect": "postgresql", "tables": {"public.learning_content_analytics": {"name": "learning_content_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content_id": {"name": "content_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": false}, "step_id": {"name": "step_id", "type": "text", "primaryKey": false, "notNull": false}, "step_index": {"name": "step_index", "type": "integer", "primaryKey": false, "notNull": false}, "time_spent": {"name": "time_spent", "type": "integer", "primaryKey": false, "notNull": false}, "completion_percentage": {"name": "completion_percentage", "type": "integer", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"learning_content_analytics_content_idx": {"name": "learning_content_analytics_content_idx", "columns": [{"expression": "content_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_content_analytics_user_idx": {"name": "learning_content_analytics_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_content_analytics_event_type_idx": {"name": "learning_content_analytics_event_type_idx", "columns": [{"expression": "event_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_content_analytics_session_idx": {"name": "learning_content_analytics_session_idx", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_content_analytics_created_at_idx": {"name": "learning_content_analytics_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"learning_content_analytics_content_id_learning_content_id_fk": {"name": "learning_content_analytics_content_id_learning_content_id_fk", "tableFrom": "learning_content_analytics", "tableTo": "learning_content", "columnsFrom": ["content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_content_feedback": {"name": "learning_content_feedback", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content_id": {"name": "content_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "feedback_text": {"name": "feedback_text", "type": "text", "primaryKey": false, "notNull": false}, "is_helpful": {"name": "is_helpful", "type": "boolean", "primaryKey": false, "notNull": false}, "suggested_improvements": {"name": "suggested_improvements", "type": "json", "primaryKey": false, "notNull": false}, "request_regeneration": {"name": "request_regeneration", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "regeneration_reason": {"name": "regeneration_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"learning_content_feedback_content_id_learning_content_id_fk": {"name": "learning_content_feedback_content_id_learning_content_id_fk", "tableFrom": "learning_content_feedback", "tableTo": "learning_content", "columnsFrom": ["content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_content_feedback_user_id_user_id_fk": {"name": "learning_content_feedback_user_id_user_id_fk", "tableFrom": "learning_content_feedback", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_progress": {"name": "learning_progress", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content_id": {"name": "content_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "current_step_index": {"name": "current_step_index", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "completed_steps": {"name": "completed_steps", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "total_time_spent": {"name": "total_time_spent", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "completion_percentage": {"name": "completion_percentage", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "bookmarks": {"name": "bookmarks", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "notes": {"name": "notes", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "session_count": {"name": "session_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"learning_progress_content_id_learning_content_id_fk": {"name": "learning_progress_content_id_learning_content_id_fk", "tableFrom": "learning_progress", "tableTo": "learning_content", "columnsFrom": ["content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_progress_user_id_user_id_fk": {"name": "learning_progress_user_id_user_id_fk", "tableFrom": "learning_progress", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"learning_progress_content_id_user_id_unique": {"name": "learning_progress_content_id_user_id_unique", "nullsNotDistinct": false, "columns": ["content_id", "user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.content_embeddings": {"name": "content_embeddings", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "learning_content_id": {"name": "learning_content_id", "type": "text", "primaryKey": false, "notNull": true}, "step_id": {"name": "step_id", "type": "text", "primaryKey": false, "notNull": true}, "step_title": {"name": "step_title", "type": "text", "primaryKey": false, "notNull": true}, "chunk_index": {"name": "chunk_index", "type": "text", "primaryKey": false, "notNull": true}, "chunk_text": {"name": "chunk_text", "type": "text", "primaryKey": false, "notNull": true}, "vector_id": {"name": "vector_id", "type": "text", "primaryKey": false, "notNull": true}, "embedding_model": {"name": "embedding_model", "type": "text", "primaryKey": false, "notNull": true, "default": "'voyage-3.5'"}, "embedding_dimension": {"name": "embedding_dimension", "type": "text", "primaryKey": false, "notNull": true, "default": "'1024'"}, "processing_metadata": {"name": "processing_metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"content_embeddings_learning_content_idx": {"name": "content_embeddings_learning_content_idx", "columns": [{"expression": "learning_content_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "content_embeddings_step_idx": {"name": "content_embeddings_step_idx", "columns": [{"expression": "step_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "content_embeddings_vector_idx": {"name": "content_embeddings_vector_idx", "columns": [{"expression": "vector_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "content_embeddings_learning_content_step_idx": {"name": "content_embeddings_learning_content_step_idx", "columns": [{"expression": "learning_content_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "step_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "content_embeddings_created_at_idx": {"name": "content_embeddings_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"content_embeddings_learning_content_id_learning_content_id_fk": {"name": "content_embeddings_learning_content_id_learning_content_id_fk", "tableFrom": "content_embeddings", "tableTo": "learning_content", "columnsFrom": ["learning_content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.conversations": {"name": "conversations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "learning_content_id": {"name": "learning_content_id", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"conversations_user_idx": {"name": "conversations_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "conversations_learning_content_idx": {"name": "conversations_learning_content_idx", "columns": [{"expression": "learning_content_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "conversations_user_content_idx": {"name": "conversations_user_content_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "learning_content_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "conversations_created_at_idx": {"name": "conversations_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"conversations_user_id_user_id_fk": {"name": "conversations_user_id_user_id_fk", "tableFrom": "conversations", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "conversations_learning_content_id_learning_content_id_fk": {"name": "conversations_learning_content_id_learning_content_id_fk", "tableFrom": "conversations", "tableTo": "learning_content", "columnsFrom": ["learning_content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "conversation_id": {"name": "conversation_id", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "sender_role": {"name": "sender_role", "type": "sender_role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "message_type": {"name": "message_type", "type": "message_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'text'"}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"messages_conversation_idx": {"name": "messages_conversation_idx", "columns": [{"expression": "conversation_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_sender_role_idx": {"name": "messages_sender_role_idx", "columns": [{"expression": "sender_role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_created_at_idx": {"name": "messages_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_conversation_created_at_idx": {"name": "messages_conversation_created_at_idx", "columns": [{"expression": "conversation_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"messages_conversation_id_conversations_id_fk": {"name": "messages_conversation_id_conversations_id_fk", "tableFrom": "messages", "tableTo": "conversations", "columnsFrom": ["conversation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.explainer_templates": {"name": "explainer_templates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "steps": {"name": "steps", "type": "json", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"explainer_templates_user_id_user_id_fk": {"name": "explainer_templates_user_id_user_id_fk", "tableFrom": "explainer_templates", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_content": {"name": "learning_content", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "steps": {"name": "steps", "type": "json", "primaryKey": false, "notNull": true}, "learning_level": {"name": "learning_level", "type": "text", "primaryKey": false, "notNull": true}, "estimated_reading_time": {"name": "estimated_reading_time", "type": "integer", "primaryKey": false, "notNull": true}, "content_type": {"name": "content_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'standard'"}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": true, "default": "'[]'::json"}, "ai_metadata": {"name": "ai_metadata", "type": "json", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "recommendation_id": {"name": "recommendation_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"learning_content_user_id_user_id_fk": {"name": "learning_content_user_id_user_id_fk", "tableFrom": "learning_content", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_content_recommendation_id_learning_recommendations_id_fk": {"name": "learning_content_recommendation_id_learning_recommendations_id_fk", "tableFrom": "learning_content", "tableTo": "learning_recommendations", "columnsFrom": ["recommendation_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.releases": {"name": "releases", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "version": {"name": "version", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": true}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "release_date": {"name": "release_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"releases_created_by_user_id_fk": {"name": "releases_created_by_user_id_fk", "tableFrom": "releases", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"releases_version_unique": {"name": "releases_version_unique", "nullsNotDistinct": false, "columns": ["version"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_release_notifications": {"name": "user_release_notifications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "release_id": {"name": "release_id", "type": "text", "primaryKey": false, "notNull": true}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": true}, "read_at": {"name": "read_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_release_notifications_user_id_user_id_fk": {"name": "user_release_notifications_user_id_user_id_fk", "tableFrom": "user_release_notifications", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_release_notifications_release_id_releases_id_fk": {"name": "user_release_notifications_release_id_releases_id_fk", "tableFrom": "user_release_notifications", "tableTo": "releases", "columnsFrom": ["release_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz": {"name": "quiz", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "learning_content_id": {"name": "learning_content_id", "type": "text", "primaryKey": false, "notNull": true}, "difficulty": {"name": "difficulty", "type": "text", "primaryKey": false, "notNull": true}, "estimated_duration": {"name": "estimated_duration", "type": "integer", "primaryKey": false, "notNull": true}, "total_points": {"name": "total_points", "type": "integer", "primaryKey": false, "notNull": true}, "questions": {"name": "questions", "type": "json", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "allow_retakes": {"name": "allow_retakes", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_correct_answers": {"name": "show_correct_answers", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "shuffle_questions": {"name": "shuffle_questions", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "time_limit": {"name": "time_limit", "type": "integer", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"quiz_learning_content_idx": {"name": "quiz_learning_content_idx", "columns": [{"expression": "learning_content_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_created_by_idx": {"name": "quiz_created_by_idx", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_difficulty_idx": {"name": "quiz_difficulty_idx", "columns": [{"expression": "difficulty", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_created_at_idx": {"name": "quiz_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quiz_learning_content_id_learning_content_id_fk": {"name": "quiz_learning_content_id_learning_content_id_fk", "tableFrom": "quiz", "tableTo": "learning_content", "columnsFrom": ["learning_content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "quiz_created_by_user_id_fk": {"name": "quiz_created_by_user_id_fk", "tableFrom": "quiz", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz_analytics": {"name": "quiz_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "quiz_id": {"name": "quiz_id", "type": "text", "primaryKey": false, "notNull": true}, "total_attempts": {"name": "total_attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "average_score": {"name": "average_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "average_time_spent": {"name": "average_time_spent", "type": "integer", "primaryKey": false, "notNull": false}, "completion_rate": {"name": "completion_rate", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "question_analytics": {"name": "question_analytics", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"quiz_analytics_quiz_idx": {"name": "quiz_analytics_quiz_idx", "columns": [{"expression": "quiz_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quiz_analytics_quiz_id_quiz_id_fk": {"name": "quiz_analytics_quiz_id_quiz_id_fk", "tableFrom": "quiz_analytics", "tableTo": "quiz", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz_attempt": {"name": "quiz_attempt", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "quiz_id": {"name": "quiz_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "answers": {"name": "answers", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "total_time_spent": {"name": "total_time_spent", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "score": {"name": "score", "type": "json", "primaryKey": false, "notNull": false}, "question_results": {"name": "question_results", "type": "json", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"quiz_attempt_quiz_user_idx": {"name": "quiz_attempt_quiz_user_idx", "columns": [{"expression": "quiz_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_attempt_user_idx": {"name": "quiz_attempt_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_attempt_completed_at_idx": {"name": "quiz_attempt_completed_at_idx", "columns": [{"expression": "completed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_attempt_is_completed_idx": {"name": "quiz_attempt_is_completed_idx", "columns": [{"expression": "is_completed", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quiz_attempt_quiz_id_quiz_id_fk": {"name": "quiz_attempt_quiz_id_quiz_id_fk", "tableFrom": "quiz_attempt", "tableTo": "quiz", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "quiz_attempt_user_id_user_id_fk": {"name": "quiz_attempt_user_id_user_id_fk", "tableFrom": "quiz_attempt", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz_feedback": {"name": "quiz_feedback", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "quiz_id": {"name": "quiz_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "attempt_id": {"name": "attempt_id", "type": "text", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "feedback_text": {"name": "feedback_text", "type": "text", "primaryKey": false, "notNull": false}, "difficulty": {"name": "difficulty", "type": "text", "primaryKey": false, "notNull": false}, "suggested_improvements": {"name": "suggested_improvements", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"quiz_feedback_quiz_idx": {"name": "quiz_feedback_quiz_idx", "columns": [{"expression": "quiz_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_feedback_user_idx": {"name": "quiz_feedback_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_feedback_attempt_idx": {"name": "quiz_feedback_attempt_idx", "columns": [{"expression": "attempt_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quiz_feedback_quiz_id_quiz_id_fk": {"name": "quiz_feedback_quiz_id_quiz_id_fk", "tableFrom": "quiz_feedback", "tableTo": "quiz", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "quiz_feedback_user_id_user_id_fk": {"name": "quiz_feedback_user_id_user_id_fk", "tableFrom": "quiz_feedback", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "quiz_feedback_attempt_id_quiz_attempt_id_fk": {"name": "quiz_feedback_attempt_id_quiz_attempt_id_fk", "tableFrom": "quiz_feedback", "tableTo": "quiz_attempt", "columnsFrom": ["attempt_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz_progress": {"name": "quiz_progress", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "attempt_id": {"name": "attempt_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "quiz_id": {"name": "quiz_id", "type": "text", "primaryKey": false, "notNull": true}, "current_question_index": {"name": "current_question_index", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "answered_questions": {"name": "answered_questions", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "bookmarked_questions": {"name": "bookmarked_questions", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "time_spent": {"name": "time_spent", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_activity_at": {"name": "last_activity_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"quiz_progress_attempt_idx": {"name": "quiz_progress_attempt_idx", "columns": [{"expression": "attempt_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_progress_user_idx": {"name": "quiz_progress_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_progress_quiz_idx": {"name": "quiz_progress_quiz_idx", "columns": [{"expression": "quiz_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quiz_progress_attempt_id_quiz_attempt_id_fk": {"name": "quiz_progress_attempt_id_quiz_attempt_id_fk", "tableFrom": "quiz_progress", "tableTo": "quiz_attempt", "columnsFrom": ["attempt_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "quiz_progress_user_id_user_id_fk": {"name": "quiz_progress_user_id_user_id_fk", "tableFrom": "quiz_progress", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "quiz_progress_quiz_id_quiz_id_fk": {"name": "quiz_progress_quiz_id_quiz_id_fk", "tableFrom": "quiz_progress", "tableTo": "quiz", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.focus_areas": {"name": "focus_areas", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"focus_areas_order_idx": {"name": "focus_areas_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_content_topics": {"name": "learning_content_topics", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "learning_content_id": {"name": "learning_content_id", "type": "text", "primaryKey": false, "notNull": true}, "topic_id": {"name": "topic_id", "type": "text", "primaryKey": false, "notNull": true}, "relevance_score": {"name": "relevance_score", "type": "integer", "primaryKey": false, "notNull": true, "default": 100}, "is_auto_tagged": {"name": "is_auto_tagged", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"learning_content_topics_content_idx": {"name": "learning_content_topics_content_idx", "columns": [{"expression": "learning_content_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_content_topics_topic_idx": {"name": "learning_content_topics_topic_idx", "columns": [{"expression": "topic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_content_topics_content_topic_idx": {"name": "learning_content_topics_content_topic_idx", "columns": [{"expression": "learning_content_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "topic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_content_topics_relevance_idx": {"name": "learning_content_topics_relevance_idx", "columns": [{"expression": "relevance_score", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_content_topics_auto_tagged_idx": {"name": "learning_content_topics_auto_tagged_idx", "columns": [{"expression": "is_auto_tagged", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"learning_content_topics_topic_id_topics_id_fk": {"name": "learning_content_topics_topic_id_topics_id_fk", "tableFrom": "learning_content_topics", "tableTo": "topics", "columnsFrom": ["topic_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_recommendations": {"name": "learning_recommendations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "topic_id": {"name": "topic_id", "type": "text", "primaryKey": false, "notNull": true}, "recommendation_type": {"name": "recommendation_type", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "json", "primaryKey": false, "notNull": true}, "ai_model": {"name": "ai_model", "type": "text", "primaryKey": false, "notNull": true}, "generated_at": {"name": "generated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_viewed": {"name": "is_viewed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "viewed_at": {"name": "viewed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_bookmarked": {"name": "is_bookmarked", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "bookmarked_at": {"name": "bookmarked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"learning_recommendations_user_idx": {"name": "learning_recommendations_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_recommendations_topic_idx": {"name": "learning_recommendations_topic_idx", "columns": [{"expression": "topic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_recommendations_type_idx": {"name": "learning_recommendations_type_idx", "columns": [{"expression": "recommendation_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_recommendations_user_topic_idx": {"name": "learning_recommendations_user_topic_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "topic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_recommendations_user_type_idx": {"name": "learning_recommendations_user_type_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "recommendation_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_recommendations_generated_at_idx": {"name": "learning_recommendations_generated_at_idx", "columns": [{"expression": "generated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_recommendations_is_viewed_idx": {"name": "learning_recommendations_is_viewed_idx", "columns": [{"expression": "is_viewed", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_recommendations_is_bookmarked_idx": {"name": "learning_recommendations_is_bookmarked_idx", "columns": [{"expression": "is_bookmarked", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_recommendations_rating_idx": {"name": "learning_recommendations_rating_idx", "columns": [{"expression": "rating", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"learning_recommendations_user_id_user_id_fk": {"name": "learning_recommendations_user_id_user_id_fk", "tableFrom": "learning_recommendations", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_recommendations_topic_id_topics_id_fk": {"name": "learning_recommendations_topic_id_topics_id_fk", "tableFrom": "learning_recommendations", "tableTo": "topics", "columnsFrom": ["topic_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.themes": {"name": "themes", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "focus_area_id": {"name": "focus_area_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"themes_focus_area_idx": {"name": "themes_focus_area_idx", "columns": [{"expression": "focus_area_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "themes_order_idx": {"name": "themes_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "themes_focus_area_order_idx": {"name": "themes_focus_area_order_idx", "columns": [{"expression": "focus_area_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"themes_focus_area_id_focus_areas_id_fk": {"name": "themes_focus_area_id_focus_areas_id_fk", "tableFrom": "themes", "tableTo": "focus_areas", "columnsFrom": ["focus_area_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.topics": {"name": "topics", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "theme_id": {"name": "theme_id", "type": "text", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"topics_theme_idx": {"name": "topics_theme_idx", "columns": [{"expression": "theme_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "topics_order_idx": {"name": "topics_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "topics_theme_order_idx": {"name": "topics_theme_order_idx", "columns": [{"expression": "theme_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "topics_name_idx": {"name": "topics_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"topics_theme_id_themes_id_fk": {"name": "topics_theme_id_themes_id_fk", "tableFrom": "topics", "tableTo": "themes", "columnsFrom": ["theme_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_learning_interests": {"name": "user_learning_interests", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "topic_id": {"name": "topic_id", "type": "text", "primaryKey": false, "notNull": true}, "interest_level": {"name": "interest_level", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "priority": {"name": "priority", "type": "integer", "primaryKey": false, "notNull": true, "default": 5}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"user_learning_interests_user_idx": {"name": "user_learning_interests_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_learning_interests_topic_idx": {"name": "user_learning_interests_topic_idx", "columns": [{"expression": "topic_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_learning_interests_interest_level_idx": {"name": "user_learning_interests_interest_level_idx", "columns": [{"expression": "interest_level", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_learning_interests_priority_idx": {"name": "user_learning_interests_priority_idx", "columns": [{"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_learning_interests_user_interest_level_idx": {"name": "user_learning_interests_user_interest_level_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "interest_level", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_learning_interests_user_priority_idx": {"name": "user_learning_interests_user_priority_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "priority", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_learning_interests_user_id_user_id_fk": {"name": "user_learning_interests_user_id_user_id_fk", "tableFrom": "user_learning_interests", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_learning_interests_topic_id_topics_id_fk": {"name": "user_learning_interests_topic_id_topics_id_fk", "tableFrom": "user_learning_interests", "tableTo": "topics", "columnsFrom": ["topic_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_learning_interests_user_id_topic_id_unique": {"name": "user_learning_interests_user_id_topic_id_unique", "nullsNotDistinct": false, "columns": ["user_id", "topic_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_path_enrollments": {"name": "learning_path_enrollments", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "path_id": {"name": "path_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'enrolled'"}, "current_step_id": {"name": "current_step_id", "type": "text", "primaryKey": false, "notNull": false}, "completed_steps": {"name": "completed_steps", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "progress_percentage": {"name": "progress_percentage", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "total_time_spent": {"name": "total_time_spent", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "milestones_reached": {"name": "milestones_reached", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "user_notes": {"name": "user_notes", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "enrolled_at": {"name": "enrolled_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"learning_path_enrollments_user_status_idx": {"name": "learning_path_enrollments_user_status_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_path_enrollments_path_status_idx": {"name": "learning_path_enrollments_path_status_idx", "columns": [{"expression": "path_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_path_enrollments_progress_idx": {"name": "learning_path_enrollments_progress_idx", "columns": [{"expression": "progress_percentage", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"learning_path_enrollments_path_id_learning_paths_id_fk": {"name": "learning_path_enrollments_path_id_learning_paths_id_fk", "tableFrom": "learning_path_enrollments", "tableTo": "learning_paths", "columnsFrom": ["path_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_path_enrollments_user_id_user_id_fk": {"name": "learning_path_enrollments_user_id_user_id_fk", "tableFrom": "learning_path_enrollments", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"learning_path_enrollments_path_id_user_id_unique": {"name": "learning_path_enrollments_path_id_user_id_unique", "nullsNotDistinct": false, "columns": ["path_id", "user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_path_prerequisites": {"name": "learning_path_prerequisites", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "path_id": {"name": "path_id", "type": "text", "primaryKey": false, "notNull": true}, "prerequisite_path_id": {"name": "prerequisite_path_id", "type": "text", "primaryKey": false, "notNull": true}, "is_required": {"name": "is_required", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "minimum_completion_percentage": {"name": "minimum_completion_percentage", "type": "integer", "primaryKey": false, "notNull": false, "default": 100}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"learning_path_prerequisites_path_idx": {"name": "learning_path_prerequisites_path_idx", "columns": [{"expression": "path_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_path_prerequisites_prerequisite_idx": {"name": "learning_path_prerequisites_prerequisite_idx", "columns": [{"expression": "prerequisite_path_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"learning_path_prerequisites_path_id_learning_paths_id_fk": {"name": "learning_path_prerequisites_path_id_learning_paths_id_fk", "tableFrom": "learning_path_prerequisites", "tableTo": "learning_paths", "columnsFrom": ["path_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_path_prerequisites_prerequisite_path_id_learning_paths_id_fk": {"name": "learning_path_prerequisites_prerequisite_path_id_learning_paths_id_fk", "tableFrom": "learning_path_prerequisites", "tableTo": "learning_paths", "columnsFrom": ["prerequisite_path_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"learning_path_prerequisites_path_id_prerequisite_path_id_unique": {"name": "learning_path_prerequisites_path_id_prerequisite_path_id_unique", "nullsNotDistinct": false, "columns": ["path_id", "prerequisite_path_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_path_recommendations": {"name": "learning_path_recommendations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "path_id": {"name": "path_id", "type": "text", "primaryKey": false, "notNull": true}, "recommendation_reason": {"name": "recommendation_reason", "type": "text", "primaryKey": false, "notNull": true}, "confidence": {"name": "confidence", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "reasoning": {"name": "reasoning", "type": "text", "primaryKey": false, "notNull": false}, "ai_model": {"name": "ai_model", "type": "text", "primaryKey": false, "notNull": false}, "generated_at": {"name": "generated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_viewed": {"name": "is_viewed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "viewed_at": {"name": "viewed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_bookmarked": {"name": "is_bookmarked", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "bookmarked_at": {"name": "bookmarked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_enrolled": {"name": "is_enrolled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "enrolled_at": {"name": "enrolled_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"learning_path_recommendations_user_reason_idx": {"name": "learning_path_recommendations_user_reason_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "recommendation_reason", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_path_recommendations_path_idx": {"name": "learning_path_recommendations_path_idx", "columns": [{"expression": "path_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_path_recommendations_confidence_idx": {"name": "learning_path_recommendations_confidence_idx", "columns": [{"expression": "confidence", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_path_recommendations_generated_at_idx": {"name": "learning_path_recommendations_generated_at_idx", "columns": [{"expression": "generated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"learning_path_recommendations_user_id_user_id_fk": {"name": "learning_path_recommendations_user_id_user_id_fk", "tableFrom": "learning_path_recommendations", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_path_recommendations_path_id_learning_paths_id_fk": {"name": "learning_path_recommendations_path_id_learning_paths_id_fk", "tableFrom": "learning_path_recommendations", "tableTo": "learning_paths", "columnsFrom": ["path_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_paths": {"name": "learning_paths", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "difficulty": {"name": "difficulty", "type": "text", "primaryKey": false, "notNull": true}, "estimated_duration": {"name": "estimated_duration", "type": "integer", "primaryKey": false, "notNull": true}, "path_steps": {"name": "path_steps", "type": "json", "primaryKey": false, "notNull": true}, "learning_objectives": {"name": "learning_objectives", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "skills_gained": {"name": "skills_gained", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "topic_ids": {"name": "topic_ids", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "is_template": {"name": "is_template", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": false}, "enrollment_count": {"name": "enrollment_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "completion_rate": {"name": "completion_rate", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "average_rating": {"name": "average_rating", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"learning_paths_difficulty_idx": {"name": "learning_paths_difficulty_idx", "columns": [{"expression": "difficulty", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_paths_public_idx": {"name": "learning_paths_public_idx", "columns": [{"expression": "is_public", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "learning_paths_created_by_idx": {"name": "learning_paths_created_by_idx", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"learning_paths_created_by_user_id_fk": {"name": "learning_paths_created_by_user_id_fk", "tableFrom": "learning_paths", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.conversation_memory": {"name": "conversation_memory", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "conversation_id": {"name": "conversation_id", "type": "text", "primaryKey": false, "notNull": true}, "key_topics": {"name": "key_topics", "type": "json", "primaryKey": false, "notNull": true, "default": "'[]'::json"}, "user_preferences": {"name": "user_preferences", "type": "json", "primaryKey": false, "notNull": true, "default": "'{}'::json"}, "proposed_topics": {"name": "proposed_topics", "type": "json", "primaryKey": false, "notNull": true, "default": "'[]'::json"}, "context_summary": {"name": "context_summary", "type": "text", "primaryKey": false, "notNull": true, "default": "''"}, "extraction_metadata": {"name": "extraction_metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"conversation_memory_conversation_idx": {"name": "conversation_memory_conversation_idx", "columns": [{"expression": "conversation_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "conversation_memory_updated_at_idx": {"name": "conversation_memory_updated_at_idx", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"conversation_memory_conversation_id_conversations_id_fk": {"name": "conversation_memory_conversation_id_conversations_id_fk", "tableFrom": "conversation_memory", "tableTo": "conversations", "columnsFrom": ["conversation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.message_type": {"name": "message_type", "schema": "public", "values": ["text", "system"]}, "public.sender_role": {"name": "sender_role", "schema": "public", "values": ["user", "assistant"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}