CREATE TYPE "public"."message_type" AS ENUM('text', 'system');--> statement-breakpoint
CREATE TYPE "public"."sender_role" AS ENUM('user', 'assistant');--> statement-breakpoint
CREATE TABLE "learning_content_analytics" (
	"id" text PRIMARY KEY NOT NULL,
	"content_id" text NOT NULL,
	"user_id" text,
	"event_type" text NOT NULL,
	"session_id" text,
	"step_id" text,
	"step_index" integer,
	"time_spent" integer,
	"completion_percentage" integer,
	"metadata" json,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "learning_content_feedback" (
	"id" text PRIMARY KEY NOT NULL,
	"content_id" text NOT NULL,
	"user_id" text NOT NULL,
	"rating" integer NOT NULL,
	"feedback_text" text,
	"is_helpful" boolean,
	"suggested_improvements" json,
	"request_regeneration" boolean DEFAULT false,
	"regeneration_reason" text,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "learning_progress" (
	"id" text PRIMARY KEY NOT NULL,
	"content_id" text NOT NULL,
	"user_id" text NOT NULL,
	"current_step_index" integer DEFAULT 0,
	"completed_steps" json DEFAULT '[]'::json,
	"total_time_spent" integer DEFAULT 0,
	"completion_percentage" integer DEFAULT 0,
	"is_completed" boolean DEFAULT false,
	"bookmarks" json DEFAULT '[]'::json,
	"notes" json DEFAULT '[]'::json,
	"last_accessed_at" timestamp NOT NULL,
	"session_count" integer DEFAULT 1,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	CONSTRAINT "learning_progress_content_id_user_id_unique" UNIQUE("content_id","user_id")
);
--> statement-breakpoint
CREATE TABLE "user" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"email_verified" boolean NOT NULL,
	"image" text,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	"avatar" text,
	CONSTRAINT "user_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "content_embeddings" (
	"id" text PRIMARY KEY NOT NULL,
	"learning_content_id" text NOT NULL,
	"step_id" text NOT NULL,
	"step_title" text NOT NULL,
	"chunk_index" text NOT NULL,
	"chunk_text" text NOT NULL,
	"vector_id" text NOT NULL,
	"embedding_model" text DEFAULT 'voyage-3.5' NOT NULL,
	"embedding_dimension" text DEFAULT '1024' NOT NULL,
	"processing_metadata" json,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "conversations" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"learning_content_id" text NOT NULL,
	"title" text NOT NULL,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "messages" (
	"id" text PRIMARY KEY NOT NULL,
	"conversation_id" text NOT NULL,
	"content" text NOT NULL,
	"sender_role" "sender_role" NOT NULL,
	"message_type" "message_type" DEFAULT 'text' NOT NULL,
	"metadata" json,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "explainer_templates" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text DEFAULT '',
	"steps" json NOT NULL,
	"user_id" text NOT NULL,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "learning_content" (
	"id" text PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"description" text DEFAULT '' NOT NULL,
	"steps" json NOT NULL,
	"learning_level" text NOT NULL,
	"estimated_reading_time" integer NOT NULL,
	"content_type" text DEFAULT 'standard' NOT NULL,
	"is_public" boolean DEFAULT false NOT NULL,
	"tags" json DEFAULT '[]'::json NOT NULL,
	"ai_metadata" json,
	"user_id" text NOT NULL,
	"recommendation_id" text,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "releases" (
	"id" text PRIMARY KEY NOT NULL,
	"version" text NOT NULL,
	"description" text NOT NULL,
	"is_published" boolean NOT NULL,
	"published_at" timestamp,
	"release_date" timestamp NOT NULL,
	"created_by" text NOT NULL,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	CONSTRAINT "releases_version_unique" UNIQUE("version")
);
--> statement-breakpoint
CREATE TABLE "user_release_notifications" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"release_id" text NOT NULL,
	"is_read" boolean NOT NULL,
	"read_at" timestamp,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "quiz" (
	"id" text PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"description" text DEFAULT '',
	"learning_content_id" text NOT NULL,
	"difficulty" text NOT NULL,
	"estimated_duration" integer NOT NULL,
	"total_points" integer NOT NULL,
	"questions" json NOT NULL,
	"metadata" json,
	"is_public" boolean DEFAULT false NOT NULL,
	"allow_retakes" boolean DEFAULT true NOT NULL,
	"show_correct_answers" boolean DEFAULT true NOT NULL,
	"shuffle_questions" boolean DEFAULT false NOT NULL,
	"time_limit" integer,
	"created_by" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "quiz_analytics" (
	"id" text PRIMARY KEY NOT NULL,
	"quiz_id" text NOT NULL,
	"total_attempts" integer DEFAULT 0 NOT NULL,
	"average_score" numeric(5, 2),
	"average_time_spent" integer,
	"completion_rate" numeric(5, 2),
	"question_analytics" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "quiz_attempt" (
	"id" text PRIMARY KEY NOT NULL,
	"quiz_id" text NOT NULL,
	"user_id" text NOT NULL,
	"started_at" timestamp DEFAULT now() NOT NULL,
	"completed_at" timestamp,
	"is_completed" boolean DEFAULT false NOT NULL,
	"answers" json DEFAULT '[]'::json,
	"total_time_spent" integer DEFAULT 0,
	"score" json,
	"question_results" json,
	"metadata" json
);
--> statement-breakpoint
CREATE TABLE "quiz_feedback" (
	"id" text PRIMARY KEY NOT NULL,
	"quiz_id" text NOT NULL,
	"user_id" text NOT NULL,
	"attempt_id" text,
	"rating" integer NOT NULL,
	"feedback_text" text,
	"difficulty" text,
	"suggested_improvements" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "quiz_progress" (
	"id" text PRIMARY KEY NOT NULL,
	"attempt_id" text NOT NULL,
	"user_id" text NOT NULL,
	"quiz_id" text NOT NULL,
	"current_question_index" integer DEFAULT 0 NOT NULL,
	"answered_questions" json DEFAULT '[]'::json,
	"bookmarked_questions" json DEFAULT '[]'::json,
	"time_spent" integer DEFAULT 0,
	"is_completed" boolean DEFAULT false NOT NULL,
	"last_activity_at" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "focus_areas" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text NOT NULL,
	"order" integer NOT NULL,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "learning_content_topics" (
	"id" text PRIMARY KEY NOT NULL,
	"learning_content_id" text NOT NULL,
	"topic_id" text NOT NULL,
	"relevance_score" integer DEFAULT 100 NOT NULL,
	"is_auto_tagged" boolean DEFAULT true NOT NULL,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "learning_recommendations" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"topic_id" text NOT NULL,
	"recommendation_type" text NOT NULL,
	"title" text NOT NULL,
	"description" text NOT NULL,
	"content" json NOT NULL,
	"ai_model" text NOT NULL,
	"generated_at" timestamp NOT NULL,
	"is_viewed" boolean DEFAULT false,
	"viewed_at" timestamp,
	"is_bookmarked" boolean DEFAULT false,
	"bookmarked_at" timestamp,
	"is_completed" boolean DEFAULT false,
	"completed_at" timestamp,
	"rating" integer,
	"feedback" text,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "themes" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text NOT NULL,
	"order" integer NOT NULL,
	"focus_area_id" text NOT NULL,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "topics" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text NOT NULL,
	"order" integer NOT NULL,
	"theme_id" text NOT NULL,
	"metadata" json,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_learning_interests" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"topic_id" text NOT NULL,
	"interest_level" integer DEFAULT 3 NOT NULL,
	"priority" integer DEFAULT 5 NOT NULL,
	"notes" text,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	CONSTRAINT "user_learning_interests_user_id_topic_id_unique" UNIQUE("user_id","topic_id")
);
--> statement-breakpoint
CREATE TABLE "learning_path_enrollments" (
	"id" text PRIMARY KEY NOT NULL,
	"path_id" text NOT NULL,
	"user_id" text NOT NULL,
	"status" text DEFAULT 'enrolled' NOT NULL,
	"current_step_id" text,
	"completed_steps" json DEFAULT '[]'::json,
	"progress_percentage" integer DEFAULT 0,
	"total_time_spent" integer DEFAULT 0,
	"milestones_reached" json DEFAULT '[]'::json,
	"user_notes" json DEFAULT '[]'::json,
	"rating" integer,
	"feedback" text,
	"enrolled_at" timestamp NOT NULL,
	"started_at" timestamp,
	"completed_at" timestamp,
	"last_accessed_at" timestamp NOT NULL,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL,
	CONSTRAINT "learning_path_enrollments_path_id_user_id_unique" UNIQUE("path_id","user_id")
);
--> statement-breakpoint
CREATE TABLE "learning_path_prerequisites" (
	"id" text PRIMARY KEY NOT NULL,
	"path_id" text NOT NULL,
	"prerequisite_path_id" text NOT NULL,
	"is_required" boolean DEFAULT true,
	"minimum_completion_percentage" integer DEFAULT 100,
	"created_at" timestamp NOT NULL,
	CONSTRAINT "learning_path_prerequisites_path_id_prerequisite_path_id_unique" UNIQUE("path_id","prerequisite_path_id")
);
--> statement-breakpoint
CREATE TABLE "learning_path_recommendations" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"path_id" text NOT NULL,
	"recommendation_reason" text NOT NULL,
	"confidence" integer NOT NULL,
	"title" text NOT NULL,
	"description" text NOT NULL,
	"reasoning" text,
	"ai_model" text,
	"generated_at" timestamp NOT NULL,
	"is_viewed" boolean DEFAULT false,
	"viewed_at" timestamp,
	"is_bookmarked" boolean DEFAULT false,
	"bookmarked_at" timestamp,
	"is_enrolled" boolean DEFAULT false,
	"enrolled_at" timestamp,
	"rating" integer,
	"feedback" text,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "learning_paths" (
	"id" text PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"description" text NOT NULL,
	"difficulty" text NOT NULL,
	"estimated_duration" integer NOT NULL,
	"path_steps" json NOT NULL,
	"learning_objectives" json DEFAULT '[]'::json,
	"skills_gained" json DEFAULT '[]'::json,
	"topic_ids" json DEFAULT '[]'::json,
	"tags" json DEFAULT '[]'::json,
	"is_public" boolean DEFAULT false,
	"is_template" boolean DEFAULT false,
	"created_by" text,
	"enrollment_count" integer DEFAULT 0,
	"completion_rate" integer DEFAULT 0,
	"average_rating" integer DEFAULT 0,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "user_points_leaderboard_mv" (
	"user_id" uuid NOT NULL,
	"user_name" text,
	"user_email" text,
	"total_points" integer DEFAULT 0 NOT NULL,
	"quiz_points" integer DEFAULT 0 NOT NULL,
	"progress_points" integer DEFAULT 0 NOT NULL,
	"analytics_points" integer DEFAULT 0 NOT NULL,
	"rank" integer,
	"last_updated" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "learning_content_analytics" ADD CONSTRAINT "learning_content_analytics_content_id_learning_content_id_fk" FOREIGN KEY ("content_id") REFERENCES "public"."learning_content"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_content_feedback" ADD CONSTRAINT "learning_content_feedback_content_id_learning_content_id_fk" FOREIGN KEY ("content_id") REFERENCES "public"."learning_content"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_content_feedback" ADD CONSTRAINT "learning_content_feedback_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_progress" ADD CONSTRAINT "learning_progress_content_id_learning_content_id_fk" FOREIGN KEY ("content_id") REFERENCES "public"."learning_content"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_progress" ADD CONSTRAINT "learning_progress_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "content_embeddings" ADD CONSTRAINT "content_embeddings_learning_content_id_learning_content_id_fk" FOREIGN KEY ("learning_content_id") REFERENCES "public"."learning_content"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_learning_content_id_learning_content_id_fk" FOREIGN KEY ("learning_content_id") REFERENCES "public"."learning_content"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_conversation_id_conversations_id_fk" FOREIGN KEY ("conversation_id") REFERENCES "public"."conversations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "explainer_templates" ADD CONSTRAINT "explainer_templates_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_content" ADD CONSTRAINT "learning_content_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_content" ADD CONSTRAINT "learning_content_recommendation_id_learning_recommendations_id_fk" FOREIGN KEY ("recommendation_id") REFERENCES "public"."learning_recommendations"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "releases" ADD CONSTRAINT "releases_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_release_notifications" ADD CONSTRAINT "user_release_notifications_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_release_notifications" ADD CONSTRAINT "user_release_notifications_release_id_releases_id_fk" FOREIGN KEY ("release_id") REFERENCES "public"."releases"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz" ADD CONSTRAINT "quiz_learning_content_id_learning_content_id_fk" FOREIGN KEY ("learning_content_id") REFERENCES "public"."learning_content"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz" ADD CONSTRAINT "quiz_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_analytics" ADD CONSTRAINT "quiz_analytics_quiz_id_quiz_id_fk" FOREIGN KEY ("quiz_id") REFERENCES "public"."quiz"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_attempt" ADD CONSTRAINT "quiz_attempt_quiz_id_quiz_id_fk" FOREIGN KEY ("quiz_id") REFERENCES "public"."quiz"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_attempt" ADD CONSTRAINT "quiz_attempt_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_feedback" ADD CONSTRAINT "quiz_feedback_quiz_id_quiz_id_fk" FOREIGN KEY ("quiz_id") REFERENCES "public"."quiz"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_feedback" ADD CONSTRAINT "quiz_feedback_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_feedback" ADD CONSTRAINT "quiz_feedback_attempt_id_quiz_attempt_id_fk" FOREIGN KEY ("attempt_id") REFERENCES "public"."quiz_attempt"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_progress" ADD CONSTRAINT "quiz_progress_attempt_id_quiz_attempt_id_fk" FOREIGN KEY ("attempt_id") REFERENCES "public"."quiz_attempt"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_progress" ADD CONSTRAINT "quiz_progress_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_progress" ADD CONSTRAINT "quiz_progress_quiz_id_quiz_id_fk" FOREIGN KEY ("quiz_id") REFERENCES "public"."quiz"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_content_topics" ADD CONSTRAINT "learning_content_topics_topic_id_topics_id_fk" FOREIGN KEY ("topic_id") REFERENCES "public"."topics"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_recommendations" ADD CONSTRAINT "learning_recommendations_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_recommendations" ADD CONSTRAINT "learning_recommendations_topic_id_topics_id_fk" FOREIGN KEY ("topic_id") REFERENCES "public"."topics"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "themes" ADD CONSTRAINT "themes_focus_area_id_focus_areas_id_fk" FOREIGN KEY ("focus_area_id") REFERENCES "public"."focus_areas"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "topics" ADD CONSTRAINT "topics_theme_id_themes_id_fk" FOREIGN KEY ("theme_id") REFERENCES "public"."themes"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_learning_interests" ADD CONSTRAINT "user_learning_interests_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_learning_interests" ADD CONSTRAINT "user_learning_interests_topic_id_topics_id_fk" FOREIGN KEY ("topic_id") REFERENCES "public"."topics"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_path_enrollments" ADD CONSTRAINT "learning_path_enrollments_path_id_learning_paths_id_fk" FOREIGN KEY ("path_id") REFERENCES "public"."learning_paths"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_path_enrollments" ADD CONSTRAINT "learning_path_enrollments_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_path_prerequisites" ADD CONSTRAINT "learning_path_prerequisites_path_id_learning_paths_id_fk" FOREIGN KEY ("path_id") REFERENCES "public"."learning_paths"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_path_prerequisites" ADD CONSTRAINT "learning_path_prerequisites_prerequisite_path_id_learning_paths_id_fk" FOREIGN KEY ("prerequisite_path_id") REFERENCES "public"."learning_paths"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_path_recommendations" ADD CONSTRAINT "learning_path_recommendations_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_path_recommendations" ADD CONSTRAINT "learning_path_recommendations_path_id_learning_paths_id_fk" FOREIGN KEY ("path_id") REFERENCES "public"."learning_paths"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_paths" ADD CONSTRAINT "learning_paths_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "learning_content_analytics_content_idx" ON "learning_content_analytics" USING btree ("content_id");--> statement-breakpoint
CREATE INDEX "learning_content_analytics_user_idx" ON "learning_content_analytics" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "learning_content_analytics_event_type_idx" ON "learning_content_analytics" USING btree ("event_type");--> statement-breakpoint
CREATE INDEX "learning_content_analytics_session_idx" ON "learning_content_analytics" USING btree ("session_id");--> statement-breakpoint
CREATE INDEX "learning_content_analytics_created_at_idx" ON "learning_content_analytics" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "content_embeddings_learning_content_idx" ON "content_embeddings" USING btree ("learning_content_id");--> statement-breakpoint
CREATE INDEX "content_embeddings_step_idx" ON "content_embeddings" USING btree ("step_id");--> statement-breakpoint
CREATE INDEX "content_embeddings_vector_idx" ON "content_embeddings" USING btree ("vector_id");--> statement-breakpoint
CREATE INDEX "content_embeddings_learning_content_step_idx" ON "content_embeddings" USING btree ("learning_content_id","step_id");--> statement-breakpoint
CREATE INDEX "content_embeddings_created_at_idx" ON "content_embeddings" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "conversations_user_idx" ON "conversations" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "conversations_learning_content_idx" ON "conversations" USING btree ("learning_content_id");--> statement-breakpoint
CREATE INDEX "conversations_user_content_idx" ON "conversations" USING btree ("user_id","learning_content_id");--> statement-breakpoint
CREATE INDEX "conversations_created_at_idx" ON "conversations" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "messages_conversation_idx" ON "messages" USING btree ("conversation_id");--> statement-breakpoint
CREATE INDEX "messages_sender_role_idx" ON "messages" USING btree ("sender_role");--> statement-breakpoint
CREATE INDEX "messages_created_at_idx" ON "messages" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "messages_conversation_created_at_idx" ON "messages" USING btree ("conversation_id","created_at");--> statement-breakpoint
CREATE INDEX "quiz_learning_content_idx" ON "quiz" USING btree ("learning_content_id");--> statement-breakpoint
CREATE INDEX "quiz_created_by_idx" ON "quiz" USING btree ("created_by");--> statement-breakpoint
CREATE INDEX "quiz_difficulty_idx" ON "quiz" USING btree ("difficulty");--> statement-breakpoint
CREATE INDEX "quiz_created_at_idx" ON "quiz" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "quiz_analytics_quiz_idx" ON "quiz_analytics" USING btree ("quiz_id");--> statement-breakpoint
CREATE INDEX "quiz_attempt_quiz_user_idx" ON "quiz_attempt" USING btree ("quiz_id","user_id");--> statement-breakpoint
CREATE INDEX "quiz_attempt_user_idx" ON "quiz_attempt" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "quiz_attempt_completed_at_idx" ON "quiz_attempt" USING btree ("completed_at");--> statement-breakpoint
CREATE INDEX "quiz_attempt_is_completed_idx" ON "quiz_attempt" USING btree ("is_completed");--> statement-breakpoint
CREATE INDEX "quiz_feedback_quiz_idx" ON "quiz_feedback" USING btree ("quiz_id");--> statement-breakpoint
CREATE INDEX "quiz_feedback_user_idx" ON "quiz_feedback" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "quiz_feedback_attempt_idx" ON "quiz_feedback" USING btree ("attempt_id");--> statement-breakpoint
CREATE INDEX "quiz_progress_attempt_idx" ON "quiz_progress" USING btree ("attempt_id");--> statement-breakpoint
CREATE INDEX "quiz_progress_user_idx" ON "quiz_progress" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "quiz_progress_quiz_idx" ON "quiz_progress" USING btree ("quiz_id");--> statement-breakpoint
CREATE INDEX "focus_areas_order_idx" ON "focus_areas" USING btree ("order");--> statement-breakpoint
CREATE INDEX "learning_content_topics_content_idx" ON "learning_content_topics" USING btree ("learning_content_id");--> statement-breakpoint
CREATE INDEX "learning_content_topics_topic_idx" ON "learning_content_topics" USING btree ("topic_id");--> statement-breakpoint
CREATE INDEX "learning_content_topics_content_topic_idx" ON "learning_content_topics" USING btree ("learning_content_id","topic_id");--> statement-breakpoint
CREATE INDEX "learning_content_topics_relevance_idx" ON "learning_content_topics" USING btree ("relevance_score");--> statement-breakpoint
CREATE INDEX "learning_content_topics_auto_tagged_idx" ON "learning_content_topics" USING btree ("is_auto_tagged");--> statement-breakpoint
CREATE INDEX "learning_recommendations_user_idx" ON "learning_recommendations" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "learning_recommendations_topic_idx" ON "learning_recommendations" USING btree ("topic_id");--> statement-breakpoint
CREATE INDEX "learning_recommendations_type_idx" ON "learning_recommendations" USING btree ("recommendation_type");--> statement-breakpoint
CREATE INDEX "learning_recommendations_user_topic_idx" ON "learning_recommendations" USING btree ("user_id","topic_id");--> statement-breakpoint
CREATE INDEX "learning_recommendations_user_type_idx" ON "learning_recommendations" USING btree ("user_id","recommendation_type");--> statement-breakpoint
CREATE INDEX "learning_recommendations_generated_at_idx" ON "learning_recommendations" USING btree ("generated_at");--> statement-breakpoint
CREATE INDEX "learning_recommendations_is_viewed_idx" ON "learning_recommendations" USING btree ("is_viewed");--> statement-breakpoint
CREATE INDEX "learning_recommendations_is_bookmarked_idx" ON "learning_recommendations" USING btree ("is_bookmarked");--> statement-breakpoint
CREATE INDEX "learning_recommendations_rating_idx" ON "learning_recommendations" USING btree ("rating");--> statement-breakpoint
CREATE INDEX "themes_focus_area_idx" ON "themes" USING btree ("focus_area_id");--> statement-breakpoint
CREATE INDEX "themes_order_idx" ON "themes" USING btree ("order");--> statement-breakpoint
CREATE INDEX "themes_focus_area_order_idx" ON "themes" USING btree ("focus_area_id","order");--> statement-breakpoint
CREATE INDEX "topics_theme_idx" ON "topics" USING btree ("theme_id");--> statement-breakpoint
CREATE INDEX "topics_order_idx" ON "topics" USING btree ("order");--> statement-breakpoint
CREATE INDEX "topics_theme_order_idx" ON "topics" USING btree ("theme_id","order");--> statement-breakpoint
CREATE INDEX "topics_name_idx" ON "topics" USING btree ("name");--> statement-breakpoint
CREATE INDEX "user_learning_interests_user_idx" ON "user_learning_interests" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "user_learning_interests_topic_idx" ON "user_learning_interests" USING btree ("topic_id");--> statement-breakpoint
CREATE INDEX "user_learning_interests_interest_level_idx" ON "user_learning_interests" USING btree ("interest_level");--> statement-breakpoint
CREATE INDEX "user_learning_interests_priority_idx" ON "user_learning_interests" USING btree ("priority");--> statement-breakpoint
CREATE INDEX "user_learning_interests_user_interest_level_idx" ON "user_learning_interests" USING btree ("user_id","interest_level");--> statement-breakpoint
CREATE INDEX "user_learning_interests_user_priority_idx" ON "user_learning_interests" USING btree ("user_id","priority");--> statement-breakpoint
CREATE INDEX "learning_path_enrollments_user_status_idx" ON "learning_path_enrollments" USING btree ("user_id","status");--> statement-breakpoint
CREATE INDEX "learning_path_enrollments_path_status_idx" ON "learning_path_enrollments" USING btree ("path_id","status");--> statement-breakpoint
CREATE INDEX "learning_path_enrollments_progress_idx" ON "learning_path_enrollments" USING btree ("progress_percentage");--> statement-breakpoint
CREATE INDEX "learning_path_prerequisites_path_idx" ON "learning_path_prerequisites" USING btree ("path_id");--> statement-breakpoint
CREATE INDEX "learning_path_prerequisites_prerequisite_idx" ON "learning_path_prerequisites" USING btree ("prerequisite_path_id");--> statement-breakpoint
CREATE INDEX "learning_path_recommendations_user_reason_idx" ON "learning_path_recommendations" USING btree ("user_id","recommendation_reason");--> statement-breakpoint
CREATE INDEX "learning_path_recommendations_path_idx" ON "learning_path_recommendations" USING btree ("path_id");--> statement-breakpoint
CREATE INDEX "learning_path_recommendations_confidence_idx" ON "learning_path_recommendations" USING btree ("confidence");--> statement-breakpoint
CREATE INDEX "learning_path_recommendations_generated_at_idx" ON "learning_path_recommendations" USING btree ("generated_at");--> statement-breakpoint
CREATE INDEX "learning_paths_difficulty_idx" ON "learning_paths" USING btree ("difficulty");--> statement-breakpoint
CREATE INDEX "learning_paths_public_idx" ON "learning_paths" USING btree ("is_public");--> statement-breakpoint
CREATE INDEX "learning_paths_created_by_idx" ON "learning_paths" USING btree ("created_by");