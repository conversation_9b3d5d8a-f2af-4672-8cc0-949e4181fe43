import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

/**
 * Database connection configuration
 */
export interface DatabaseConfig {
  connectionString: string;
  max?: number;
  idle_timeout?: number;
  connect_timeout?: number;
}

/**
 * Cloudflare Hyperdrive binding interface
 */
export interface HyperdriveBinding {
  connectionString: string;
}

/**
 * Cloudflare Workers environment with Hyperdrive binding
 */
export interface CloudflareEnvWithHyperdrive {
  HYPERDRIVE?: HyperdriveBinding;
  DATABASE_URL?: string;
}

/**
 * Database type for TypeScript inference
 */
export type Database = ReturnType<typeof drizzle<typeof schema>>;

/**
 * Create a database connection using the provided configuration
 */
export function createDatabaseConnection(config: DatabaseConfig) {
  // Create postgres client
  const client = postgres(config.connectionString, {
    max: config.max ?? 10,
    idle_timeout: config.idle_timeout ?? 20,
    connect_timeout: config.connect_timeout ?? 10,
  });

  // Create drizzle instance with schema
  const db = drizzle(client, { schema });

  return { db, client };
}

/**
 * Create a database connection using Cloudflare Hyperdrive
 * Hyperdrive handles connection pooling, so we use max: 1 per Worker instance
 */
export function createHyperdriveConnection(hyperdrive: HyperdriveBinding) {
  console.log('🚀 Using Cloudflare Hyperdrive for database connection');

  // Create postgres client with Hyperdrive connection string
  // Hyperdrive handles pooling, so we use minimal local pooling
  const client = postgres(hyperdrive.connectionString, {
    max: 1, // Single connection per Worker - Hyperdrive handles the pooling
    idle_timeout: 20, // Short timeout since Hyperdrive manages connections
    connect_timeout: 10, // 10 seconds connection timeout
    fetch_types: false, // Disable type fetching for better performance
    prepare: false, // Disable prepared statements for better compatibility
    debug: false, // Disable debug logging in production
  });

  // Create drizzle instance with schema
  const db = drizzle(client, { schema });

  return { db, client };
}

/**
 * Safely access process.env in environments where it might not exist
 */
function getProcessEnv(key: string): string | undefined {
  try {
    return (globalThis as any).process?.env?.[key];
  } catch {
    return undefined;
  }
}

/**
 * Get environment variable from Cloudflare Workers context or process.env
 * Returns undefined during build phase to prevent module loading issues
 */
function getEnvVar(key: string): string | undefined {
  // During build phase, return undefined to prevent module loading issues
  if (typeof window === 'undefined' && typeof process !== 'undefined' && process.env.NODE_ENV === undefined) {
    return undefined;
  }

  // Try to get from Cloudflare Workers context first
  try {
    const { getEnvVar: getCloudflareEnvVar } = require('~/lib/context.server');
    return getCloudflareEnvVar(key);
  } catch {
    // Fall back to process.env for local development
    return getProcessEnv(key);
  }
}

/**
 * Create a database connection using environment variables
 * Supports both Node.js (process.env) and Cloudflare Workers (env binding)
 * Prefers Hyperdrive when available in Cloudflare Workers environment
 */
export function createDatabaseConnectionFromEnv(env?: CloudflareEnvWithHyperdrive) {
  // Check if Hyperdrive is available (Cloudflare Workers environment)
  if (env?.HYPERDRIVE) {
    console.log('🚀 Hyperdrive binding detected - using Hyperdrive connection');
    return createHyperdriveConnection(env.HYPERDRIVE);
  }

  // Fall back to direct DATABASE_URL connection
  const connectionString = env?.DATABASE_URL || getEnvVar('DATABASE_URL') || getProcessEnv('DATABASE_URL');

  if (!connectionString) {
    // During build phase, return a placeholder connection to prevent module loading issues
    if (typeof window === 'undefined' && typeof process !== 'undefined' && process.env.NODE_ENV === undefined) {
      console.warn('DATABASE_URL not available during build phase - using placeholder connection');
      return createPlaceholderConnection();
    }

    throw new Error(
      'DATABASE_URL environment variable is required. ' +
      'Please set it to your PostgreSQL connection string. ' +
      'For Cloudflare Workers, ensure it\'s defined as a secret or use Hyperdrive binding.'
    );
  }

  // For demo/development with dummy connection string, create a mock connection
  if (connectionString.includes('demo:demo@demo')) {
    console.warn('Using demo database connection - authentication features will be limited');
    return createPlaceholderConnection();
  }

  console.log('📡 Using direct DATABASE_URL connection (Hyperdrive not available)');
  return createDatabaseConnection({ connectionString });
}

/**
 * Create a placeholder connection for build phase or demo mode
 */
function createPlaceholderConnection() {
  // Create a mock postgres client that doesn't actually connect
  const mockClient = {
    end: () => Promise.resolve(),
  } as any;

  // Create a mock drizzle instance
  const mockDb = {
    select: () => ({ from: () => ({ where: () => Promise.resolve([]) }) }),
    insert: () => ({ values: () => Promise.resolve({ insertId: 'mock-id' }) }),
    update: () => ({ set: () => ({ where: () => Promise.resolve() }) }),
    delete: () => ({ where: () => Promise.resolve() }),
  } as any;

  return { db: mockDb, client: mockClient };
}

/**
 * Create a database connection specifically for Cloudflare Workers
 * This function accepts the Cloudflare Workers env binding and prefers Hyperdrive
 */
export function createDatabaseConnectionForWorkers(env: CloudflareEnvWithHyperdrive) {
  return createDatabaseConnectionFromEnv(env);
}

/**
 * Request-scoped database instance cache
 * In Cloudflare Workers, we want to create connections per request context
 */
let _requestDb: any = null;
let _requestClient: any = null;
let _lastRequestId: string | null = null;

function getDatabase() {
  try {
    // Try to get Cloudflare Workers environment (including Hyperdrive binding)
    let env: CloudflareEnvWithHyperdrive | undefined;
    let requestId: string | undefined;

    try {
      const { getEnv, getRequest } = require('~/lib/context.server');
      env = getEnv();
      const request = getRequest();
      requestId = request.headers.get('cf-ray') || request.url;

      // If this is a new request, create a new connection
      if (requestId && requestId !== _lastRequestId) {
        console.log('🔍 New request detected, creating fresh database connection');
        _requestDb = null;
        _requestClient = null;
        _lastRequestId = requestId;
      }
    } catch {
      // Fall back to undefined if context is not available (e.g., during build)
      console.log('📡 Cloudflare Workers context not available, using fallback connection');
    }

    // Create connection if not cached for this request
    if (!_requestDb) {
      const result = createDatabaseConnectionFromEnv(env);
      _requestDb = result.db;
      _requestClient = result.client;
    }

    return { db: _requestDb, client: _requestClient };
  } catch (error) {
    console.warn('Failed to create database connection, using placeholder:', error);
    const result = createPlaceholderConnection();
    return { db: result.db, client: result.client };
  }
}

/**
 * Default database instance using environment variables
 * This is the main export that most applications should use
 */
export const db = new Proxy({} as any, {
  get(_target, prop) {
    return getDatabase().db[prop];
  }
});

export const client = new Proxy({} as any, {
  get(_target, prop) {
    return getDatabase().client[prop];
  }
});

/**
 * Test database connection health
 * Works with both Hyperdrive and direct connections
 */
export async function testDatabaseConnection(): Promise<{ success: boolean; error?: string; info?: any }> {
  try {
    const { db } = getDatabase();

    // Simple query to test connection
    const result = await db.execute('SELECT 1 as test');

    return {
      success: true,
      info: {
        testQuery: 'SELECT 1',
        result: result,
        timestamp: new Date().toISOString(),
        connectionType: 'Hyperdrive or Direct',
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      info: {
        timestamp: new Date().toISOString(),
      }
    };
  }
}

// Export the database instance as default
export default db;
