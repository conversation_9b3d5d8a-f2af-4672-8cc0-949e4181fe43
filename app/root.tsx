import { <PERSON><PERSON>, <PERSON>a, <PERSON>let, <PERSON><PERSON><PERSON>, ScrollRestoration, useLoaderData } from "react-router";
import type { ClientLoaderFunctionArgs } from "react-router";
import { Providers } from "~/lib/providers";
import { ErrorBoundary, RouteErrorBoundary } from "~/lib/error-handling/error-boundary";
import "./tailwind.css";

export { ErrorBoundary };

// Route ID for accessing loader data
export const id = 'root';

// Client-side loader to handle theme during hydration
export async function clientLoader({ request }: ClientLoaderFunctionArgs) {
  // This runs during hydration on the client
  const stored = localStorage.getItem('theme');
  const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
  
  let resolvedTheme = 'light'; // default
  
  if (stored) {
    if (stored === 'system') {
      resolvedTheme = systemTheme;
    } else {
      resolvedTheme = stored;
    }
  }
  
  // Apply theme immediately during hydration
  const root = document.documentElement;
  root.classList.remove('light', 'dark');
  root.classList.add(resolvedTheme);
  
  return {
    theme: stored || 'system',
    resolvedTheme,
  };
}

// Enable hydration for this loader
clientLoader.hydrate = true;

export default function App() {
  const { resolvedTheme } = useLoaderData<typeof clientLoader>();
  
  return (
    <html lang="en" className={resolvedTheme}>
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <Providers>
          <Outlet />
        </Providers>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}