import React from 'react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Check, X } from 'lucide-react';
import { useTopicActions } from '../../lib/hooks/use-topic-actions';
import { useConversationMemory } from '../../lib/hooks/use-conversation-memory';
import { log } from '../../lib/logger';

export interface ProposedTopic {
  topic: string;
  status: 'proposed' | 'accepted' | 'rejected';
}

export interface TopicProposalsProps {
  conversationId: string;
  className?: string;
}

/**
 * Component for displaying and managing topic proposals
 */
export function TopicProposals({ conversationId, className }: TopicProposalsProps) {
  const { acceptTopic, rejectTopic, isAccepting, isRejecting } = useTopicActions();
  const { data: memoryData, isLoading } = useConversationMemory({ conversationId });
  
  const proposedTopics = memoryData?.data?.proposedTopics || [];

  // Filter to only show proposed topics that haven't been accepted or rejected
  const pendingTopics = proposedTopics.filter(t => t.status === 'proposed');

  if (isLoading || pendingTopics.length === 0) {
    return null;
  }

  const handleAcceptTopic = (topic: string) => {
    log.info('User accepting topic', { conversationId, topic });
    acceptTopic({ conversationId, topic });
  };

  const handleRejectTopic = (topic: string) => {
    log.info('User rejecting topic', { conversationId, topic });
    rejectTopic({ conversationId, topic });
  };

  return (
    <Card className={`mb-4 border-blue-200 bg-blue-50 ${className || ''}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium text-blue-900">
          💡 Learning Topics Discovered
        </CardTitle>
        <CardDescription className="text-xs text-blue-700">
          I've identified some key topics from our conversation. Would you like me to remember these for future discussions?
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-2">
        {pendingTopics.map((proposedTopic, index) => (
          <div
            key={`${proposedTopic.topic}-${index}`}
            className="p-3 bg-white rounded-lg border border-blue-200 space-y-3"
          >
            <div>
              <span className="text-sm font-medium text-gray-900">
                {proposedTopic.topic}
              </span>
            </div>
            <div className="flex items-center space-x-2 w-full">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleAcceptTopic(proposedTopic.topic)}
                disabled={isAccepting || isRejecting}
                className="h-8 px-3 text-green-700 border-green-300 hover:bg-green-50 flex-1"
              >
                <Check className="w-4 h-4 mr-1" />
                Remember
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleRejectTopic(proposedTopic.topic)}
                disabled={isAccepting || isRejecting}
                className="h-8 px-3 text-red-700 border-red-300 hover:bg-red-50 flex-1"
              >
                <X className="w-4 h-4 mr-1" />
                Skip
              </Button>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}