import { cn } from "~/lib/utils";
import { Clock, CheckCircle, Circle, AlertTriangle } from "lucide-react";

interface QuizProgressBarProps {
  currentQuestion: number;
  totalQuestions: number;
  answeredQuestions: number;
  timeSpent: number;
  timeLimit?: number;
  timeRemaining?: number;
  className?: string;
}

export function QuizProgressBar({
  currentQuestion,
  totalQuestions,
  answeredQuestions,
  timeSpent,
  timeLimit,
  timeRemaining,
  className
}: QuizProgressBarProps) {
  // Calculate progress percentage
  const progressPercentage = (answeredQuestions / totalQuestions) * 100;
  const currentPercentage = (currentQuestion / totalQuestions) * 100;

  // Format time
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Time warning logic
  const isTimeWarning = timeRemaining !== undefined && timeRemaining <= 300; // 5 minutes
  const isTimeCritical = timeRemaining !== undefined && timeRemaining <= 60; // 1 minute
  
  const timeColor = isTimeCritical
    ? "text-red-600 dark:text-red-400"
    : isTimeWarning
    ? "text-orange-600 dark:text-orange-400"
    : timeLimit && timeSpent > timeLimit * 60 * 0.8
    ? "text-red-600 dark:text-red-400"
    : "text-gray-700 dark:text-gray-300";

  const remainingQuestions = totalQuestions - answeredQuestions;

  return (
    <div className={cn("bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-4", className)}>
      {/* Header with stats */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {answeredQuestions} of {totalQuestions} answered
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Circle className="h-5 w-5 text-blue-500 dark:text-blue-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Question {currentQuestion}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Time Spent */}
          <div className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-gray-500 dark:text-gray-400" />
            <span className={cn("text-sm font-medium", timeColor)}>
              {formatTime(timeSpent)}
            </span>
          </div>
          
          {/* Countdown Timer (only when time limit is set) */}
          {timeLimit && timeRemaining !== undefined && (
            <div className="flex items-center space-x-2">
              {(isTimeWarning || isTimeCritical) && (
                <AlertTriangle className={cn(
                  "h-5 w-5",
                  isTimeCritical ? "text-red-500 animate-pulse" : "text-orange-500"
                )} />
              )}
              <span className={cn(
                "text-sm font-medium",
                isTimeCritical ? "text-red-600 dark:text-red-400 animate-pulse" :
                isTimeWarning ? "text-orange-600 dark:text-orange-400" :
                "text-blue-600 dark:text-blue-400"
              )}>
                {timeRemaining > 0 ? formatTime(timeRemaining) : "00:00"} left
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Progress bars */}
      <div className="space-y-3">
        {/* Overall progress */}
        <div>
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Overall Progress</span>
            <span className="text-xs text-gray-500 dark:text-gray-400">{progressPercentage.toFixed(1)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-green-500 dark:bg-green-400 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>

        {/* Current position indicator */}
        <div>
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Current Position</span>
            <span className="text-xs text-gray-500 dark:text-gray-400">Question {currentQuestion}</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 relative">
            {/* Background progress */}
            <div
              className="bg-blue-200 dark:bg-blue-600/50 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${currentPercentage}%` }}
            />
          </div>
        </div>
        
        {/* Time Progress Bar (only when time limit is set) */}
        {timeLimit && timeRemaining !== undefined && (
          <div className="mt-3">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Time Remaining</span>
              <span className={cn(
                "text-xs font-medium",
                isTimeCritical ? "text-red-600 dark:text-red-400" :
                isTimeWarning ? "text-orange-600 dark:text-orange-400" :
                "text-blue-600 dark:text-blue-400"
              )}>
                {((timeRemaining / (timeLimit * 60)) * 100).toFixed(1)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={cn(
                  "h-2 rounded-full transition-all duration-300 ease-out",
                  isTimeCritical ? "bg-red-500 dark:bg-red-400 animate-pulse" :
                  isTimeWarning ? "bg-orange-500 dark:bg-orange-400" :
                  "bg-blue-500 dark:bg-blue-400"
                )}
                style={{ width: `${Math.max(0, (timeRemaining / (timeLimit * 60)) * 100)}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Bottom stats */}
      <div className="mt-3 text-center">
        <span className="text-xs text-gray-500 dark:text-gray-400">
          {remainingQuestions} questions remaining
        </span>
      </div>
    </div>
  );
}
