import { cn } from "~/lib/utils";
import { ChevronLeft, ChevronRight, Bookmark, BookmarkCheck, CheckCircle, Circle } from "lucide-react";
import { Button } from "~/components/ui/button";

interface QuizNavigationProps {
  currentQuestionIndex: number;
  totalQuestions: number;
  answeredQuestions: Set<number>;
  bookmarkedQuestions: Set<number>;
  onQuestionSelect: (index: number) => void;
  onPrevious: () => void;
  onNext: () => void;
  onBookmarkToggle: () => void;
  canGoNext: boolean;
  canGoPrevious: boolean;
  onSaveProgress?: () => void;
  onExit?: () => void;
  className?: string;
}

export function QuizNavigation({
  currentQuestionIndex,
  totalQuestions,
  answeredQuestions,
  bookmarkedQuestions,
  onQuestionSelect,
  onPrevious,
  onNext,
  onBookmarkToggle,
  canGoNext,
  canGoPrevious,
  onSaveProgress,
  onExit,
  className
}: QuizNavigationProps) {
  const isCurrentBookmarked = bookmarkedQuestions.has(currentQuestionIndex);

  return (
    <div className={cn("bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-4", className)}>
      {/* Navigation buttons */}
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={onPrevious}
          disabled={!canGoPrevious}
          className={cn(
            "flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors",
            canGoPrevious
              ? "bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300"
              : "bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 cursor-not-allowed"
          )}
        >
          <ChevronLeft className="h-4 w-4" />
          <span>Previous</span>
        </button>

        <button
          onClick={onBookmarkToggle}
          className={cn(
            "flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors",
            isCurrentBookmarked
              ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 hover:bg-yellow-200 dark:hover:bg-yellow-900/50"
              : "bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300"
          )}
        >
          {isCurrentBookmarked ? (
            <BookmarkCheck className="h-4 w-4" />
          ) : (
            <Bookmark className="h-4 w-4" />
          )}
          <span>Bookmark</span>
        </button>

        <button
          onClick={onNext}
          disabled={!canGoNext}
          className={cn(
            "flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors",
            canGoNext
              ? "bg-blue-600 hover:bg-blue-700 text-white"
              : "bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500 cursor-not-allowed"
          )}
        >
          <span>Next</span>
          <ChevronRight className="h-4 w-4" />
        </button>
      </div>

      {/* Question grid */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Questions</h3>
        <div className="grid grid-cols-8 sm:grid-cols-10 md:grid-cols-12 lg:grid-cols-15 gap-2">
          {Array.from({ length: totalQuestions }, (_, index) => {
            const isAnswered = answeredQuestions.has(index);
            const isBookmarked = bookmarkedQuestions.has(index);
            const isCurrent = index === currentQuestionIndex;

            return (
              <button
                key={index}
                onClick={() => onQuestionSelect(index)}
                className={cn(
                  "relative w-8 h-8 rounded-lg text-xs font-medium transition-all duration-200 hover:scale-105",
                  isCurrent
                    ? "bg-blue-600 text-white shadow-md"
                    : isAnswered
                    ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400 hover:bg-green-200 dark:hover:bg-green-900/50"
                    : "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600"
                )}
                title={`Question ${index + 1}${isAnswered ? ' (Answered)' : ''}${isBookmarked ? ' (Bookmarked)' : ''}`}
              >
                <span>{index + 1}</span>
                
                {/* Status indicators */}
                <div className="absolute -top-1 -right-1 flex flex-col space-y-0.5">
                  {isAnswered && (
                    <CheckCircle className="h-3 w-3 text-green-500 dark:text-green-400 bg-white dark:bg-gray-800 rounded-full" />
                  )}
                  {isBookmarked && (
                    <Bookmark className="h-3 w-3 text-yellow-500 dark:text-yellow-400 bg-white dark:bg-gray-800 rounded-full p-0.5" />
                  )}
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Legend */}
      <div className="mt-4 flex items-center justify-center space-x-6 text-xs text-gray-500 dark:text-gray-400">
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 bg-blue-600 rounded"></div>
          <span>Current</span>
        </div>
        <div className="flex items-center space-x-1">
          <CheckCircle className="h-3 w-3 text-green-500" />
          <span>Answered</span>
        </div>
        <div className="flex items-center space-x-1">
          <Bookmark className="h-3 w-3 text-yellow-500" />
          <span>Bookmarked</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded"></div>
          <span>Unanswered</span>
        </div>
      </div>

      {/* Statistics */}
      <div className="mt-4 flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
        <span>{answeredQuestions.size} answered</span>
        <span>{bookmarkedQuestions.size} bookmarked</span>
        <span>{totalQuestions - answeredQuestions.size} remaining</span>
      </div>

      {/* Action buttons */}
      <div className="mt-6 flex items-center justify-between">
        {onSaveProgress && (
          <Button onClick={onSaveProgress} variant="default">
            Save Progress
          </Button>
        )}
        {onExit && (
          <Button onClick={onExit} variant="outline">
            Exit Quiz
          </Button>
        )}
      </div>
    </div>
  );
}
