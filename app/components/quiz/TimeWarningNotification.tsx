'use client';

import { useEffect, useState } from 'react';
import { AlertTriangle, Clock, X } from 'lucide-react';
import { cn } from '~/lib/utils';

interface TimeWarningNotificationProps {
  timeRemaining?: number;
  timeLimit?: number;
  onDismiss?: () => void;
}

export function TimeWarningNotification({
  timeRemaining,
  timeLimit,
  onDismiss
}: TimeWarningNotificationProps) {
  const [dismissed, setDismissed] = useState(false);
  const [lastWarningShown, setLastWarningShown] = useState<number | null>(null);

  // Reset dismissed state when timeRemaining changes significantly
  useEffect(() => {
    if (timeRemaining !== undefined && timeRemaining > 300) {
      setDismissed(false);
      setLastWarningShown(null);
    }
  }, [timeRemaining]);

  // Determine warning level and visibility
  const isTimeUp = timeRemaining !== undefined && timeRemaining <= 0;
  const isCritical = timeRemaining !== undefined && timeRemaining <= 60; // 1 minute
  const isWarning = timeRemaining !== undefined && timeRemaining <= 300; // 5 minutes
  const isFirstWarning = timeRemaining !== undefined && timeRemaining <= 600; // 10 minutes

  // Only show notifications at specific thresholds
  let shouldShow = false;
  let warningLevel = 0;

  if (timeLimit && timeRemaining !== undefined && !dismissed) {
    if (isTimeUp) {
      shouldShow = true;
      warningLevel = 4;
    } else if (isCritical && lastWarningShown !== 3) {
      shouldShow = true;
      warningLevel = 3;
    } else if (isWarning && lastWarningShown !== 2) {
      shouldShow = true;
      warningLevel = 2;
    } else if (isFirstWarning && lastWarningShown !== 1) {
      shouldShow = true;
      warningLevel = 1;
    }
  }

  useEffect(() => {
    if (timeLimit && timeRemaining !== undefined && !dismissed) {
      let currentWarningLevel = 0;
      
      if (timeRemaining <= 0) {
        currentWarningLevel = 4;
      } else if (timeRemaining <= 60 && lastWarningShown !== 3) {
        currentWarningLevel = 3;
      } else if (timeRemaining <= 300 && lastWarningShown !== 2) {
        currentWarningLevel = 2;
      } else if (timeRemaining <= 600 && lastWarningShown !== 1) {
        currentWarningLevel = 1;
      }
      
      if (currentWarningLevel > 0 && currentWarningLevel !== lastWarningShown) {
        setLastWarningShown(currentWarningLevel);
      }
    }
  }, [timeRemaining, timeLimit, dismissed]);

  // Early return after all hooks have been called
  if (!shouldShow) {
    return null;
  }

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleDismiss = () => {
    setDismissed(true);
    onDismiss?.();
  };

  // Get warning content based on time remaining
  const getWarningContent = () => {
    const safeTimeRemaining = timeRemaining ?? 0;
    
    if (isTimeUp) {
      return {
        title: 'Time\'s Up!',
        message: 'Your quiz time has expired. The quiz will be automatically submitted.',
        bgColor: 'bg-red-50 dark:bg-red-900/20',
        borderColor: 'border-red-200 dark:border-red-800',
        textColor: 'text-red-800 dark:text-red-200',
        iconColor: 'text-red-500'
      };
    } else if (isCritical) {
      return {
        title: 'Critical: 1 Minute Left!',
        message: `Only ${formatTime(safeTimeRemaining)} remaining. Please finish your current question quickly.`,
        bgColor: 'bg-red-50 dark:bg-red-900/20',
        borderColor: 'border-red-200 dark:border-red-800',
        textColor: 'text-red-800 dark:text-red-200',
        iconColor: 'text-red-500'
      };
    } else if (isWarning) {
      return {
        title: 'Warning: 5 Minutes Left',
        message: `${formatTime(safeTimeRemaining)} remaining. Please start wrapping up your answers.`,
        bgColor: 'bg-orange-50 dark:bg-orange-900/20',
        borderColor: 'border-orange-200 dark:border-orange-800',
        textColor: 'text-orange-800 dark:text-orange-200',
        iconColor: 'text-orange-500'
      };
    } else {
      return {
        title: 'Time Notice: 10 Minutes Left',
        message: `${formatTime(safeTimeRemaining)} remaining. You\'re doing great, keep going!`,
        bgColor: 'bg-blue-50 dark:bg-blue-900/20',
        borderColor: 'border-blue-200 dark:border-blue-800',
        textColor: 'text-blue-800 dark:text-blue-200',
        iconColor: 'text-blue-500'
      };
    }
  };

  const warning = getWarningContent();

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm animate-in slide-in-from-right-full duration-300">
      <div className={cn(
        "rounded-lg border p-4 shadow-lg",
        warning.bgColor,
        warning.borderColor
      )}>
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            {isTimeUp || isCritical ? (
              <AlertTriangle className={cn("h-5 w-5", warning.iconColor, isCritical && "animate-pulse")} />
            ) : (
              <Clock className={cn("h-5 w-5", warning.iconColor)} />
            )}
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className={cn("text-sm font-medium", warning.textColor)}>
              {warning.title}
            </h3>
            <p className={cn("text-sm mt-1", warning.textColor)}>
              {warning.message}
            </p>
          </div>
          
          {!isTimeUp && (
            <button
              onClick={handleDismiss}
              className={cn(
                "flex-shrink-0 rounded-md p-1.5 hover:bg-black/5 dark:hover:bg-white/5 transition-colors",
                warning.textColor
              )}
            >
              <X className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
}