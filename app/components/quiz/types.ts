/**
 * Quiz component types and interfaces
 * Matches the AI-generated quiz schemas for type safety
 */

// Quiz difficulty levels
export type QuizDifficulty = 'easy' | 'medium' | 'hard';

// Quiz types
export type QuizType =
  | 'flashcard'
  | 'multipleChoice'
  | 'trueFalse'
  | 'fillInBlank'
  | 'matching'
  | 'freeText'
  | 'ordering';

// Base quiz question interface
export interface BaseQuestion {
  id: string;
  type: QuizType;
  difficulty: QuizDifficulty;
  sourceStepId: string;
  sourceContent: string;
  points: number;
}

// Specific question types
export interface FlashcardQuestion extends BaseQuestion {
  type: 'flashcard';
  front: string;
  back: string;
  hint?: string;
}

export interface MultipleChoiceQuestion extends BaseQuestion {
  type: 'multipleChoice';
  question: string;
  options: string[];
  correctAnswerIndex: number;
  explanation?: string;
  hint?: string;
}

export interface TrueFalseQuestion extends BaseQuestion {
  type: 'trueFalse';
  statement: string;
  correctAnswer: boolean;
  explanation?: string;
  hint?: string;
}

export interface FillInBlankQuestion extends BaseQuestion {
  type: 'fillInBlank';
  text: string;
  blanks: Array<{
    position: number;
    correctAnswer: string;
    acceptableAnswers?: string[];
    caseSensitive: boolean;
  }>;
  hint?: string;
}

export interface MatchingQuestion extends BaseQuestion {
  type: 'matching';
  instruction: string;
  pairs: Array<{
    left: string;
    right: string;
  }>;
  hint?: string;
}

export interface FreeTextQuestion extends BaseQuestion {
  type: 'freeText';
  question: string;
  answerType: 'short' | 'long';
  maxLength: number;
  sampleAnswer: string;
  evaluationCriteria: string[];
  hint?: string;
}

export interface OrderingQuestion extends BaseQuestion {
  type: 'ordering';
  instruction: string;
  items: string[];
  correctOrder: number[];
  orderType: 'chronological' | 'logical' | 'priority' | 'process';
  hint?: string;
}

// Union type for all questions
export type QuizQuestion =
  | FlashcardQuestion
  | MultipleChoiceQuestion
  | TrueFalseQuestion
  | FillInBlankQuestion
  | MatchingQuestion
  | FreeTextQuestion
  | OrderingQuestion;

// Quiz data structure
export interface Quiz {
  id: string;
  title: string;
  description: string;
  learningContentId: string;
  questions: QuizQuestion[];
  estimatedDuration: number;
  totalPoints: number;
  difficulty: QuizDifficulty;
  isPublic: boolean;
  allowRetakes: boolean;
  showCorrectAnswers: boolean;
  shuffleQuestions: boolean;
  timeLimit?: number;
  metadata: {
    generatedAt: string;
    aiModel: string;
    sourceStepsUsed: string[];
    difficultyDistribution: Record<string, number>;
    typeDistribution: Record<string, number>;
  };
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// Quiz attempt and progress
export interface QuizAttempt {
  id: string;
  quizId: string;
  userId: string;
  startedAt: string;
  completedAt?: string;
  isCompleted: boolean;
  answers: QuizAnswer[];
  totalTimeSpent: number;
  score?: QuizScore;
  questionResults?: QuestionResult[];
  metadata?: {
    userAgent?: string;
    ipAddress?: string;
    deviceType?: string;
    browserInfo?: string;
    questionOrder?: string[];
    pausedDurations?: number[];
  };
}

export interface QuizAnswer {
  questionId: string;
  questionType: QuizType;
  answer: any;
  timeSpent: number;
  isCorrect?: boolean;
  pointsEarned?: number;
}

export interface QuizScore {
  totalPoints: number;
  earnedPoints: number;
  percentage: number;
  correctAnswers: number;
  totalQuestions: number;
}

export interface QuestionResult {
  questionId: string;
  isCorrect: boolean;
  pointsEarned: number;
  feedback?: string;
}

export interface QuizProgress {
  id: string;
  attemptId: string;
  currentQuestionIndex: number;
  questionsAnswered: number;
  totalQuestions: number;
  timeSpentSoFar: number;
  lastActiveAt: string;
  currentAnswers: Array<{
    questionId: string;
    answer: any;
    timeSpent: number;
    isTemporary?: boolean;
  }>;
  bookmarkedQuestions: string[];
  questionNotes: Record<string, string>;
}

// Quiz generation and configuration
export interface QuizGenerationConfig {
  learningContentId: string;
  quizTypes: QuizType[];
  difficulty: QuizDifficulty;
  questionsPerType: number;
  includeHints: boolean;
  includeExplanations: boolean;
  timeLimit?: number;
  shuffleQuestions: boolean;
}

export interface QuizSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (config: QuizGenerationConfig) => void;
  learningContentId: string;
  isGenerating: boolean;
}

// Legacy types for backward compatibility
export interface QuizHistoryItem {
  id: string;
  quizId: string;
  userId: string;
  learningContentId?: string;
  score: number;
  totalQuestions: number;
  correctAnswers: number;
  timeSpent: number;
  completed: boolean;
  startedAt: string;
  completedAt?: string;
  answers: any[];
  metadata?: any;
}

export interface QuizStats {
  totalAttempts: number;
  completedAttempts: number;
  averageScore: number;
  bestScore: number;
  totalTimeSpent: number;
}

export interface QuizHistoryResponse {
  attempts: QuizHistoryItem[];
  stats: QuizStats;
  hasMore: boolean;
  total: number;
}

// Component props interfaces
export interface QuestionComponentProps<T extends QuizQuestion = QuizQuestion> {
  question: T;
  userAnswer?: any;
  isAnswered: boolean;
  isReviewMode: boolean;
  showCorrectAnswer: boolean;
  onAnswerChange: (answer: any) => void;
  onAnswerSubmit: (answer: any, timeSpent: number) => void;
  className?: string;
}

// Quiz feedback
export interface QuizFeedback {
  quizId: string;
  attemptId?: string;
  difficultyRating?: number;
  clarityRating?: number;
  relevanceRating?: number;
  overallRating?: number;
  comments?: string;
  suggestions?: string;
  questionFeedback: Array<{
    questionId: string;
    rating: number;
    comment?: string;
    reportedIssue?: 'unclear' | 'incorrect' | 'too_hard' | 'too_easy' | 'irrelevant';
  }>;
  isAnonymous: boolean;
}

export interface QuizFeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (feedback: QuizFeedback) => void;
  quiz: Quiz;
  attempt?: QuizAttempt;
  isSubmitting: boolean;
}

// API Response types
export interface QuizGenerationResponse {
  success: boolean;
  data?: {
    quizId: string;
    message: string;
  };
  error?: string;
  details?: any;
}

// Unwrapped response type (what API client actually returns after processing)
export interface QuizGenerationData {
  quizId: string;
  message: string;
}

export interface QuizListResponse {
  success: boolean;
  data?: Quiz[];
  error?: string;
}

export interface QuizResponse {
  success: boolean;
  data?: Quiz;
  error?: string;
}
