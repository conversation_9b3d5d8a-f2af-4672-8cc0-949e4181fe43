import { useState } from "react";
import { <PERSON> } from "react-router";
import { 
  ChevronDown, 
  ChevronRight, 
  BookOpen, 
  Target, 
  Clock,
  Trophy,
  Plus,
  ExternalLink
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "~/components/ui/collapsible";
import { QuizCard } from "./QuizCard";
import type { GroupedQuizzes, QuizWithProgress } from "~/routes/api.quizzes";
import { cn } from "~/lib/utils";

interface EnhancedGroupedQuizViewProps {
  groupedQuizzes: GroupedQuizzes[];
  viewMode?: 'grid' | 'list';
  className?: string;
}

export function EnhancedGroupedQuizView({
  groupedQuizzes,
  viewMode = 'grid',
  className = "",
}: EnhancedGroupedQuizViewProps) {
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(
    new Set(groupedQuizzes.map(group => group.learningContentId))
  );

  const toggleGroup = (contentId: string) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(contentId)) {
      newExpanded.delete(contentId);
    } else {
      newExpanded.add(contentId);
    }
    setExpandedGroups(newExpanded);
  };

  const getGroupStats = (quizzes: QuizWithProgress[]) => {
    const total = quizzes.length;
    const completed = quizzes.filter(q => q.progress?.status === 'completed').length;
    const inProgress = quizzes.filter(q => q.progress?.status === 'in-progress').length;
    const notStarted = quizzes.filter(q => q.progress?.status === 'not-started').length;
    
    const totalQuestions = quizzes.reduce((sum, quiz) => sum + quiz.questions.length, 0);
    const totalDuration = quizzes.reduce((sum, quiz) => sum + quiz.estimatedDuration, 0);
    
    const completionPercentage = total > 0 ? Math.round((completed / total) * 100) : 0;

    return {
      total,
      completed,
      inProgress,
      notStarted,
      totalQuestions,
      totalDuration,
      completionPercentage,
    };
  };

  const getStatusColor = (status: 'completed' | 'in-progress' | 'not-started') => {
    switch (status) {
      case 'completed':
        return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/20';
      case 'in-progress':
        return 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/20';
      case 'not-started':
        return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/20';
      default:
        return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/20';
    }
  };

  if (groupedQuizzes.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No quiz groups found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Create some learning content and generate quizzes to see them grouped here.
          </p>
          <Link to="/dashboard/learn">
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Content
            </Button>
          </Link>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {groupedQuizzes.map((group) => {
        const stats = getGroupStats(group.quizzes);
        const isExpanded = expandedGroups.has(group.learningContentId);

        return (
          <Card key={group.learningContentId} className="overflow-hidden">
            <Collapsible
              open={isExpanded}
              onOpenChange={() => toggleGroup(group.learningContentId)}
            >
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        {isExpanded ? (
                          <ChevronDown className="h-5 w-5 text-gray-500" />
                        ) : (
                          <ChevronRight className="h-5 w-5 text-gray-500" />
                        )}
                        <BookOpen className="h-5 w-5 text-blue-500" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white">
                          {group.learningContentTitle}
                        </CardTitle>
                        <CardDescription className="mt-1">
                          {group.learningContentDescription}
                        </CardDescription>
                      </div>
                    </div>

                    <div className="flex items-center gap-4">
                      {/* Quick Stats */}
                      <div className="hidden sm:flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center gap-1">
                          <Target className="w-4 h-4" />
                          <span>{stats.total} quizzes</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          <span>{stats.totalDuration} min</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Trophy className="w-4 h-4" />
                          <span>{stats.completionPercentage}% complete</span>
                        </div>
                      </div>

                      {/* Status Badges */}
                      <div className="flex items-center gap-2">
                        {stats.completed > 0 && (
                          <Badge className={getStatusColor('completed')}>
                            {stats.completed} completed
                          </Badge>
                        )}
                        {stats.inProgress > 0 && (
                          <Badge className={getStatusColor('in-progress')}>
                            {stats.inProgress} in progress
                          </Badge>
                        )}
                        {stats.notStarted > 0 && (
                          <Badge className={getStatusColor('not-started')}>
                            {stats.notStarted} not started
                          </Badge>
                        )}
                      </div>

                      {/* Link to Learning Content */}
                      <Link 
                        to={`/dashboard/learn/${group.learningContentId}`}
                        onClick={(e) => e.stopPropagation()}
                        className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                      >
                        <ExternalLink className="h-4 w-4" />
                      </Link>
                    </div>
                  </div>

                  {/* Mobile Stats */}
                  <div className="sm:hidden flex items-center gap-4 mt-2 text-sm text-gray-600 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <Target className="w-4 h-4" />
                      <span>{stats.total} quizzes</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{stats.totalDuration} min</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Trophy className="w-4 h-4" />
                      <span>{stats.completionPercentage}% complete</span>
                    </div>
                  </div>
                </CardHeader>
              </CollapsibleTrigger>

              <CollapsibleContent>
                <CardContent className="pt-0">
                  {/* Detailed Stats Bar */}
                  <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                      <div>
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          {stats.total}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Total Quizzes</p>
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          {stats.totalQuestions}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Questions</p>
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-gray-900 dark:text-white">
                          {stats.totalDuration}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Minutes</p>
                      </div>
                      <div>
                        <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                          {stats.completionPercentage}%
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Complete</p>
                      </div>
                    </div>
                  </div>

                  {/* Quiz Grid/List */}
                  <div className={
                    viewMode === 'grid'
                      ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4"
                      : "space-y-4"
                  }>
                    {group.quizzes.map((quiz) => (
                      <QuizCard
                        key={quiz.id}
                        quiz={quiz}
                        viewMode={viewMode}
                      />
                    ))}
                  </div>

                  {/* Generate More Quizzes Button */}
                  <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                    <Link to={`/dashboard/learn/${group.learningContentId}`}>
                      <Button variant="outline" className="w-full flex items-center justify-center gap-2">
                        <Plus className="h-4 w-4" />
                        Generate More Quizzes for this Content
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>
        );
      })}
    </div>
  );
}
