'use client';

import { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { cn } from "~/lib/utils";
import { QuizNavigation } from './QuizNavigation';
import { QuizProgressBar } from './QuizProgressBar';
import { QuizResults } from './QuizResults';
import { QuestionRenderer } from './QuestionRenderer';
import { QuizFeedbackModal } from './QuizFeedbackModal';
import { TimeWarningNotification } from './TimeWarningNotification';
import type {
  Quiz,
  QuizAttempt,
  QuizProgress,
  QuizAnswer,
  QuizFeedback
} from './types';

interface QuizComponentProps {
  quiz: Quiz;
  attempt?: QuizAttempt;
  progress?: QuizProgress;
  onAnswerSubmit: (questionId: string, answer: any, timeSpent: number) => void;
  onQuizComplete: () => void;
  onQuizExit: () => void;
  onSaveProgress?: (data: {
    attemptId: string;
    currentQuestionIndex: number;
    timeSpentOnCurrentQuestion: number;
    totalSessionTime: number;
    bookmarkedQuestions?: string[];
  }) => Promise<void>;
  className?: string;
}

interface QuizContainerProps extends QuizComponentProps {
  onFeedbackSubmit?: (feedback: QuizFeedback) => void;
  onRetake?: () => void;
  onReviewAnswers?: () => void; // Add proper review navigation function
  isSubmittingAnswer?: boolean;
  isCompletingQuiz?: boolean;
  isRetaking?: boolean;
}

export function QuizContainer({
  quiz,
  attempt,
  progress,
  onAnswerSubmit,
  onQuizComplete,
  onQuizExit,
  onSaveProgress,
  onFeedbackSubmit,
  onRetake,
  onReviewAnswers,
  isSubmittingAnswer = false,
  isCompletingQuiz = false,
  isRetaking = false,
  className
}: QuizContainerProps) {
  // State management
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(
    progress?.currentQuestionIndex || 0
  );
  const [userAnswers, setUserAnswers] = useState<Map<string, any>>(new Map());
  const [answeredQuestions, setAnsweredQuestions] = useState<Set<number>>(new Set());
  const [bookmarkedQuestions, setBookmarkedQuestions] = useState<Set<number>>(
    new Set(progress?.bookmarkedQuestions?.map(id =>
      quiz.questions.findIndex(q => q.id === id)
    ).filter(index => index !== -1) || [])
  );
  const [questionStartTime, setQuestionStartTime] = useState<number>(Date.now());
  const [totalTimeSpent, setTotalTimeSpent] = useState(attempt?.totalTimeSpent || progress?.timeSpentSoFar || 0);
  const [showResultsView, setShowResultsView] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);
  const [isSavingProgress, setIsSavingProgress] = useState(false);

  // Refs to track current values for auto-save without causing re-renders
  const currentQuestionIndexRef = useRef(currentQuestionIndex);
  const totalTimeSpentRef = useRef(totalTimeSpent);
  const questionStartTimeRef = useRef(questionStartTime);
  const bookmarkedQuestionsRef = useRef(bookmarkedQuestions);

  // Update refs when values change
  useEffect(() => {
    currentQuestionIndexRef.current = currentQuestionIndex;
  }, [currentQuestionIndex]);

  useEffect(() => {
    totalTimeSpentRef.current = totalTimeSpent;
  }, [totalTimeSpent]);

  useEffect(() => {
    bookmarkedQuestionsRef.current = bookmarkedQuestions;
  }, [bookmarkedQuestions]);

  useEffect(() => {
    questionStartTimeRef.current = questionStartTime;
  }, [questionStartTime]);

  // Reset results view when attempt changes (important for retakes)
  useEffect(() => {
    if (attempt && !attempt.isCompleted) {
      setShowResultsView(false);
    }
  }, [attempt?.id, attempt?.isCompleted]);

  // Auto-show results when quiz is completed with score
  useEffect(() => {
    if (attempt?.isCompleted && attempt?.score !== undefined && attempt?.score !== null) {
      setShowResultsView(true);
    }
  }, [attempt?.isCompleted, attempt?.score]);



  // Sync local timer with backend totalTimeSpent when attempt updates
  useEffect(() => {
    if (attempt?.totalTimeSpent !== undefined) {
      setTotalTimeSpent(attempt.totalTimeSpent);
    } else if (progress?.timeSpentSoFar !== undefined) {
      setTotalTimeSpent(progress.timeSpentSoFar);
    }
  }, [attempt?.totalTimeSpent, progress?.timeSpentSoFar]);

  // Initialize answers from progress or attempt
  useEffect(() => {
    const initialAnswers = new Map<string, any>();
    const initialAnswered = new Set<number>();

    // Load from progress (current session)
    if (progress?.currentAnswers) {
      progress.currentAnswers.forEach(answer => {
        initialAnswers.set(answer.questionId, answer.answer);
        const questionIndex = quiz.questions.findIndex(q => q.id === answer.questionId);
        if (questionIndex !== -1) {
          initialAnswered.add(questionIndex);
        }
      });
    }

    // Load from completed attempt (review mode)
    if (attempt?.answers) {
      attempt.answers.forEach(answer => {
        initialAnswers.set(answer.questionId, answer.answer);
        const questionIndex = quiz.questions.findIndex(q => q.id === answer.questionId);
        if (questionIndex !== -1) {
          initialAnswered.add(questionIndex);
        }
      });
    }

    setUserAnswers(initialAnswers);
    setAnsweredQuestions(initialAnswered);
  }, [quiz.questions, progress, attempt]);

  // Timer effect with auto-save progress
  useEffect(() => {
    if (attempt?.isCompleted) return;

    const interval = setInterval(() => {
      setTotalTimeSpent(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [attempt?.isCompleted]);

  // Auto-save progress every 30 seconds
  useEffect(() => {
    if (!attempt || !attempt.id || attempt.isCompleted || !onSaveProgress) return;

    const autoSaveInterval = setInterval(async () => {
      // Calculate time spent on current question using ref values
      const currentQuestionTime = Math.floor((Date.now() - questionStartTimeRef.current) / 1000);
      const bookmarkedQuestionIds = Array.from(bookmarkedQuestionsRef.current).map(index => quiz.questions[index]?.id).filter(Boolean);

      try {
        await onSaveProgress({
          attemptId: attempt.id,
          currentQuestionIndex: currentQuestionIndexRef.current,
          timeSpentOnCurrentQuestion: currentQuestionTime,
          totalSessionTime: totalTimeSpentRef.current,
          bookmarkedQuestions: bookmarkedQuestionIds,
        });
        console.log('Auto-saved progress at', new Date().toLocaleTimeString());
      } catch (error) {
        console.warn('Failed to auto-save progress:', error);
        // Don't show error to user for auto-save failures
      }
    }, 30000); // Save every 30 seconds

    return () => clearInterval(autoSaveInterval);
  }, [attempt, onSaveProgress]); // Only depend on attempt and onSaveProgress

  // Save progress on page unload
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      if (attempt && attempt.id && !attempt.isCompleted && onSaveProgress) {
        // Calculate time spent on current question using ref values
        const currentQuestionTime = Math.floor((Date.now() - questionStartTimeRef.current) / 1000);

        // Use navigator.sendBeacon for reliable data transmission during page unload
        const bookmarkedQuestionIds = Array.from(bookmarkedQuestionsRef.current).map(index => quiz.questions[index]?.id).filter(Boolean);
        const progressData = {
          attemptId: attempt.id,
          currentQuestionIndex: currentQuestionIndexRef.current,
          timeSpentOnCurrentQuestion: currentQuestionTime,
          totalSessionTime: totalTimeSpentRef.current,
          bookmarkedQuestions: bookmarkedQuestionIds,
        };

        // Try to send data using sendBeacon (more reliable for page unload)
        try {
          const blob = new Blob([JSON.stringify(progressData)], { type: 'application/json' });
          navigator.sendBeacon('/api/quiz-attempts/save-progress', blob);
        } catch (error) {
          console.warn('Failed to save progress on unload:', error);
        }

        // Show confirmation dialog to user
        event.preventDefault();
        event.returnValue = 'You have unsaved progress. Are you sure you want to leave?';
        return event.returnValue;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [attempt, onSaveProgress]); // Only depend on attempt and onSaveProgress

  // Navigation handlers
  const handleQuestionSelect = useCallback((index: number) => {
    if (index >= 0 && index < quiz.questions.length) {
      setCurrentQuestionIndex(index);
      setQuestionStartTime(Date.now());
    }
  }, [quiz.questions.length]);

  const handlePrevious = useCallback(() => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
      setQuestionStartTime(Date.now());
    }
  }, [currentQuestionIndex]);

  const handleNext = useCallback(() => {
    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      setQuestionStartTime(Date.now());
    }
  }, [currentQuestionIndex, quiz.questions.length]);

  const handleBookmarkToggle = useCallback((index: number) => {
    setBookmarkedQuestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  }, []);

  // Answer handling
  const handleAnswerChange = useCallback((answer: any) => {
    const currentQuestion = quiz.questions[currentQuestionIndex];
    setUserAnswers(prev => new Map(prev.set(currentQuestion.id, answer)));
  }, [quiz.questions, currentQuestionIndex]);

  // Manual save progress function
  const saveCurrentProgress = useCallback(async () => {
    if (!attempt || attempt.isCompleted || !onSaveProgress) return;

    const currentQuestionTime = Math.floor((Date.now() - questionStartTime) / 1000);
    const bookmarkedQuestionIds = Array.from(bookmarkedQuestions).map(index => quiz.questions[index]?.id).filter(Boolean);

    setIsSavingProgress(true);
    try {
      await onSaveProgress({
        attemptId: attempt.id,
        currentQuestionIndex,
        timeSpentOnCurrentQuestion: currentQuestionTime,
        totalSessionTime: totalTimeSpent,
        bookmarkedQuestions: bookmarkedQuestionIds,
      });
    } catch (error) {
      console.error('Failed to save progress:', error);
      throw error; // Re-throw for manual saves so caller can handle
    } finally {
      setIsSavingProgress(false);
    }
  }, [attempt, currentQuestionIndex, totalTimeSpent, questionStartTime, onSaveProgress, bookmarkedQuestions, quiz.questions]);

  // Create ordered questions based on shuffled questionOrder from attempt metadata
  const orderedQuestions = useMemo(() => {
    const questionOrder = attempt?.metadata?.questionOrder;
    return questionOrder && questionOrder.length === quiz.questions.length
      ? questionOrder
          .filter((questionId): questionId is string => questionId != null)
          .map((questionId: string) => quiz.questions.find(q => q.id === questionId))
          .filter((question): question is NonNullable<typeof question> => question != null)
      : quiz.questions;
  }, [quiz.questions, attempt?.metadata?.questionOrder]);

  const handleAnswerSubmit = useCallback((answer: any, timeSpent: number) => {
    const currentQuestion = orderedQuestions[currentQuestionIndex];

    // Update local state
    setUserAnswers(prev => new Map(prev.set(currentQuestion.id, answer)));
    setAnsweredQuestions(prev => new Set(prev.add(currentQuestionIndex)));

    // Submit to parent
    onAnswerSubmit(currentQuestion.id, answer, timeSpent);

    // Auto-advance to next question if not on last question
    if (currentQuestionIndex < orderedQuestions.length - 1) {
      setTimeout(() => {
        handleNext();
      }, 500); // Small delay to show feedback
    }
  }, [orderedQuestions, currentQuestionIndex, onAnswerSubmit, handleNext]);

  const handleQuizComplete = useCallback(() => {
    // Results will show automatically when attempt.isCompleted && attempt.score becomes true
    onQuizComplete();
  }, [onQuizComplete]);

  const handleFeedbackSubmit = useCallback((feedback: QuizFeedback) => {
    if (onFeedbackSubmit) {
      onFeedbackSubmit(feedback);
    }
    setShowFeedbackModal(false);
  }, [onFeedbackSubmit]);

  // Enhanced exit handler that saves progress before exiting
  const handleQuizExit = useCallback(async () => {
    // Save progress before exiting if quiz is not completed
    if (attempt && !attempt.isCompleted) {
      try {
        await saveCurrentProgress();
      } catch (error) {
        console.warn('Failed to save progress before exit:', error);
        // Continue with exit even if save fails
      }
    }

    onQuizExit();
  }, [attempt, saveCurrentProgress, onQuizExit]);

  // Check if quiz is complete
  const isQuizComplete = answeredQuestions.size === orderedQuestions.length;
  const canGoNext = currentQuestionIndex < orderedQuestions.length - 1;
  const canGoPrevious = currentQuestionIndex > 0;

  // Current question (using shuffled order if available)
  const currentQuestion = orderedQuestions[currentQuestionIndex];
  const currentAnswer = userAnswers.get(currentQuestion?.id);
  const isCurrentAnswered = answeredQuestions.has(currentQuestionIndex);

  // Review mode check
  const isReviewMode = attempt?.isCompleted || false;

  // Time limit check
  const timeRemaining = quiz.timeLimit ? (quiz.timeLimit * 60) - totalTimeSpent : undefined;
  const isTimeUp = timeRemaining !== undefined && timeRemaining <= 0;

  // Auto-complete if time is up
  useEffect(() => {
    if (isTimeUp && !attempt?.isCompleted) {
      handleQuizComplete();
    }
  }, [isTimeUp, attempt?.isCompleted, handleQuizComplete]);

  // Show results if quiz is completed with score OR user explicitly wants to see results
  const shouldShowResults = (attempt?.isCompleted && attempt?.score !== undefined && attempt?.score !== null) || (showResultsView && attempt?.score !== undefined && attempt?.score !== null);

  if (shouldShowResults && attempt?.score !== undefined && attempt?.score !== null) {

    return (
      <div className={cn("max-w-4xl mx-auto p-6", className)}>
        <QuizResults
          quiz={quiz}
          attempt={attempt}
          score={attempt.score}
          questionResults={attempt.questionResults || []}
          onRetake={quiz.allowRetakes && onRetake ? onRetake : undefined}
          onReview={onReviewAnswers || (() => setShowResultsView(false))}
          onExit={handleQuizExit}
          isRetaking={isRetaking}
        />

        {onFeedbackSubmit && (
          <div className="mt-6 text-center">
            <button
              onClick={() => setShowFeedbackModal(true)}
              className="text-blue-600 hover:text-blue-800 underline"
            >
              Provide Feedback
            </button>
          </div>
        )}

        <QuizFeedbackModal
          isOpen={showFeedbackModal}
          onClose={() => setShowFeedbackModal(false)}
          onSubmit={handleFeedbackSubmit}
          quiz={quiz}
          attempt={attempt}
          isSubmitting={false}
        />
      </div>
    );
  }

  return (
    <div className={cn("max-w-4xl mx-auto p-6", className)}>
      {/* Progress Bar */}
      <QuizProgressBar
        currentQuestion={currentQuestionIndex + 1}
        totalQuestions={quiz.questions.length}
        answeredQuestions={answeredQuestions.size}
        timeSpent={totalTimeSpent}
        timeLimit={quiz.timeLimit}
        timeRemaining={timeRemaining}
        className="mb-6"
      />

      {/* Quiz Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6 mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{quiz.title}</h1>
        <p className="text-gray-600 dark:text-gray-300 mb-4">{quiz.description}</p>

        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
          <span>Question {currentQuestionIndex + 1} of {quiz.questions.length}</span>
          <span>
            {quiz.estimatedDuration} min • {quiz.totalPoints} points
          </span>
        </div>
      </div>

      {/* Question Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border dark:border-gray-700 p-6 mb-6">
        {currentQuestion && (
          <QuestionRenderer
            key={currentQuestion.id}
            question={currentQuestion}
            userAnswer={currentAnswer}
            isAnswered={isCurrentAnswered}
            isReviewMode={isReviewMode}
            showCorrectAnswer={isCurrentAnswered || (isReviewMode && quiz.showCorrectAnswers)}
            onAnswerChange={handleAnswerChange}
            onAnswerSubmit={handleAnswerSubmit}
          />
        )}
      </div>

      {/* Navigation */}
      <QuizNavigation
        currentQuestionIndex={currentQuestionIndex}
        totalQuestions={quiz.questions.length}
        answeredQuestions={answeredQuestions}
        bookmarkedQuestions={bookmarkedQuestions}
        onQuestionSelect={handleQuestionSelect}
        onPrevious={handlePrevious}
        onNext={handleNext}
        onBookmarkToggle={() => handleBookmarkToggle(currentQuestionIndex)}
        canGoNext={canGoNext}
        canGoPrevious={canGoPrevious}
        className="mb-6"
      />

      {/* Complete Quiz Button or Review Results Button */}
      {isQuizComplete && !attempt?.isCompleted && (
        <div className="text-center">
          <button
            onClick={handleQuizComplete}
            disabled={isCompletingQuiz}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-8 py-3 rounded-lg font-medium transition-colors"
          >
            {isCompletingQuiz ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                Completing Quiz...
              </>
            ) : (
              'Complete Quiz'
            )}
          </button>
        </div>
      )}

      {/* Review Results Button for completed quizzes - only show when results are hidden */}
      {attempt?.isCompleted && attempt?.score !== undefined && attempt?.score !== null && showResultsView === false && (
        <div className="text-center">
          <button
            onClick={() => setShowResultsView(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors"
          >
            Review Results
          </button>
        </div>
      )}

      {/* Save Progress and Exit Buttons */}
      <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mt-4">
        {/* Save Progress Button - only show for incomplete quizzes */}
        {!attempt?.isCompleted && onSaveProgress && (
          <button
            onClick={saveCurrentProgress}
            disabled={isSavingProgress}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors"
          >
            {isSavingProgress ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Saving...</span>
              </>
            ) : (
              <>
                <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <span>Save Progress</span>
              </>
            )}
          </button>
        )}

        {/* Exit Button */}
        <button
          onClick={handleQuizExit}
          className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 underline"
        >
          Exit Quiz
        </button>
      </div>
      
      {/* Time Warning Notifications (only when time limit is set) */}
      <TimeWarningNotification
        timeRemaining={timeRemaining}
        timeLimit={quiz.timeLimit}
      />
    </div>
  );
}