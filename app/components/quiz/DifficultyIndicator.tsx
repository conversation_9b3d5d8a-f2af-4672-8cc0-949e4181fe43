import { Badge } from "~/components/ui/badge";
import { cn } from "~/lib/utils";
import type { QuizDifficulty } from "./types";

interface DifficultyIndicatorProps {
  difficulty: QuizDifficulty;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'badge' | 'dots' | 'bar';
  className?: string;
}

export function DifficultyIndicator({
  difficulty,
  size = 'md',
  variant = 'badge',
  className = "",
}: DifficultyIndicatorProps) {
  const getDifficultyConfig = (difficulty: QuizDifficulty) => {
    switch (difficulty) {
      case 'easy':
        return {
          label: 'Easy',
          color: 'green',
          bgClass: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
          dotClass: 'bg-green-500',
          barClass: 'bg-green-500',
          level: 1,
        };
      case 'medium':
        return {
          label: 'Medium',
          color: 'yellow',
          bgClass: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
          dotClass: 'bg-yellow-500',
          barClass: 'bg-yellow-500',
          level: 2,
        };
      case 'hard':
        return {
          label: 'Hard',
          color: 'red',
          bgClass: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
          dotClass: 'bg-red-500',
          barClass: 'bg-red-500',
          level: 3,
        };
      default:
        return {
          label: 'Unknown',
          color: 'gray',
          bgClass: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
          dotClass: 'bg-gray-500',
          barClass: 'bg-gray-500',
          level: 1,
        };
    }
  };

  const config = getDifficultyConfig(difficulty);

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return {
          badge: 'text-xs px-2 py-1',
          dot: 'w-2 h-2',
          bar: 'h-1',
        };
      case 'lg':
        return {
          badge: 'text-base px-3 py-2',
          dot: 'w-4 h-4',
          bar: 'h-3',
        };
      default: // md
        return {
          badge: 'text-sm px-2.5 py-1.5',
          dot: 'w-3 h-3',
          bar: 'h-2',
        };
    }
  };

  const sizeClasses = getSizeClasses(size);

  if (variant === 'badge') {
    return (
      <Badge 
        className={cn(
          config.bgClass,
          sizeClasses.badge,
          'font-medium border-0',
          className
        )}
      >
        {config.label}
      </Badge>
    );
  }

  if (variant === 'dots') {
    return (
      <div className={cn('flex items-center gap-1', className)}>
        {[1, 2, 3].map((level) => (
          <div
            key={level}
            className={cn(
              sizeClasses.dot,
              'rounded-full',
              level <= config.level 
                ? config.dotClass 
                : 'bg-gray-200 dark:bg-gray-600'
            )}
          />
        ))}
        <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
          {config.label}
        </span>
      </div>
    );
  }

  if (variant === 'bar') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
          <div
            className={cn(
              sizeClasses.bar,
              config.barClass,
              'rounded-full transition-all duration-300'
            )}
            style={{ width: `${(config.level / 3) * 100}%` }}
          />
        </div>
        <span className="text-sm text-gray-600 dark:text-gray-400 min-w-0">
          {config.label}
        </span>
      </div>
    );
  }

  return null;
}

// Utility function to get difficulty color for other components
export function getDifficultyColor(difficulty: QuizDifficulty): string {
  switch (difficulty) {
    case 'easy':
      return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800';
    case 'medium':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800';
    case 'hard':
      return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800';
  }
}

// Utility function to get difficulty icon
export function getDifficultyIcon(difficulty: QuizDifficulty): string {
  switch (difficulty) {
    case 'easy':
      return '🟢';
    case 'medium':
      return '🟡';
    case 'hard':
      return '🔴';
    default:
      return '⚪';
  }
}
