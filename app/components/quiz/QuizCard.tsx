import { <PERSON> } from "react-router";
import { 
  Clock, 
  Target, 
  Calendar, 
  CheckCircle, 
  RotateCcw, 
  AlertCircle,
  Play,
  BookO<PERSON>,
  Trophy
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Progress } from "~/components/ui/progress";
import { DifficultyIndicator } from "./DifficultyIndicator";
import type { QuizWithProgress } from "~/routes/api.quizzes";
import { cn } from "~/lib/utils";

interface QuizCardProps {
  quiz: QuizWithProgress;
  viewMode?: 'grid' | 'list';
  className?: string;
}

export function QuizCard({ quiz, viewMode = 'grid', className = "" }: QuizCardProps) {
  const progress = quiz.progress;
  
  // Get progress indicator based on status
  const getProgressIndicator = () => {
    if (!progress) {
      return {
        icon: AlertCircle,
        color: 'text-gray-500',
        bgColor: 'bg-gray-100 dark:bg-gray-800',
        text: 'Not Started',
        description: 'Ready to begin',
      };
    }

    switch (progress.status) {
      case 'completed':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50 dark:bg-green-900/20',
          text: 'Completed',
          description: progress.bestScore 
            ? `Best: ${progress.bestScore.percentage}% (${progress.bestScore.earnedPoints}/${progress.bestScore.totalPoints})${progress.latestScore && progress.latestScore.percentage !== progress.bestScore.percentage ? ` | Latest: ${progress.latestScore.percentage}% (${progress.latestScore.earnedPoints}/${progress.latestScore.totalPoints})` : ''}`
            : 'Quiz completed',
        };
      case 'in-progress':
        return {
          icon: RotateCcw,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50 dark:bg-blue-900/20',
          text: 'In Progress',
          description: `${progress.completionPercentage}% complete`,
        };
      default:
        return {
          icon: AlertCircle,
          color: 'text-gray-500',
          bgColor: 'bg-gray-100 dark:bg-gray-800',
          text: 'Not Started',
          description: 'Ready to begin',
        };
    }
  };

  // Get button props based on quiz status
  const getButtonProps = () => {
    if (!progress || progress.status === 'not-started') {
      return {
        text: 'Take Quiz',
        variant: 'default' as const,
        icon: Play,
      };
    }

    if (progress.status === 'in-progress') {
      return {
        text: 'Continue Quiz',
        variant: 'default' as const,
        icon: RotateCcw,
      };
    }

    if (progress.status === 'completed') {
      return {
        text: 'Retake Quiz',
        variant: 'outline' as const,
        icon: RotateCcw,
      };
    }

    return {
      text: 'Take Quiz',
      variant: 'default' as const,
      icon: Play,
    };
  };

  const progressIndicator = getProgressIndicator();
  const buttonProps = getButtonProps();

  // Format time spent
  const formatTimeSpent = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    return `${hours}h ${minutes % 60}m`;
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    }).format(new Date(date));
  };

  if (viewMode === 'list') {
    return (
      <Card className={cn('hover:shadow-md transition-shadow', className)}>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-start gap-4">
                {/* Status Icon */}
                <div className={cn(
                  'flex items-center justify-center w-12 h-12 rounded-lg',
                  progressIndicator.bgColor
                )}>
                  <progressIndicator.icon className={cn('w-6 h-6', progressIndicator.color)} />
                </div>

                {/* Quiz Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                      {quiz.title}
                    </h3>
                    <DifficultyIndicator difficulty={quiz.difficulty} size="sm" />
                  </div>
                  
                  <p className="text-gray-600 dark:text-gray-400 text-sm mb-2 line-clamp-2">
                    {quiz.description || 'No description available'}
                  </p>

                  <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center gap-1">
                      <BookOpen className="w-4 h-4" />
                      <span>{quiz.learningContent.title}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Target className="w-4 h-4" />
                      <span>{quiz.questions.length} questions</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{quiz.estimatedDuration} min</span>
                    </div>
                    {progress && progress.timeSpent > 0 && (
                      <div className="flex items-center gap-1">
                        <Trophy className="w-4 h-4" />
                        <span>{formatTimeSpent(progress.timeSpent)} spent</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center gap-3 ml-4">
              <div className="text-right">
                <p className={cn('text-sm font-medium', progressIndicator.color)}>
                  {progressIndicator.text}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {progressIndicator.description}
                </p>
              </div>
              
              <Link to={`/dashboard/quiz/${quiz.id}`}>
                <Button variant={buttonProps.variant} className="flex items-center gap-2">
                  <buttonProps.icon className="w-4 h-4" />
                  {buttonProps.text}
                </Button>
              </Link>
            </div>
          </div>

          {/* Progress Bar for In-Progress Quizzes */}
          {progress && progress.status === 'in-progress' && (
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">Progress</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {progress.completionPercentage}%
                </span>
              </div>
              <Progress value={progress.completionPercentage} className="h-2" />
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // Grid view
  return (
    <Card className={cn('hover:shadow-lg transition-all duration-200 group', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white line-clamp-2 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
              {quiz.title}
            </CardTitle>
            <CardDescription className="mt-1 line-clamp-2">
              {quiz.description || 'No description available'}
            </CardDescription>
          </div>
          <DifficultyIndicator difficulty={quiz.difficulty} size="sm" />
        </div>

        {/* Learning Content Badge */}
        <Badge variant="secondary" className="w-fit">
          <BookOpen className="w-3 h-3 mr-1" />
          {quiz.learningContent.title}
        </Badge>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Quiz Stats */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Target className="w-4 h-4" />
            <span>{quiz.questions.length} questions</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Clock className="w-4 h-4" />
            <span>{quiz.estimatedDuration} min</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
            <Calendar className="w-4 h-4" />
            <span>{formatDate(quiz.createdAt)}</span>
          </div>
          {progress && progress.totalAttempts > 0 && (
            <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
              <Trophy className="w-4 h-4" />
              <span>{progress.totalAttempts} attempts</span>
            </div>
          )}
        </div>

        {/* Progress Section */}
        <div className={cn(
          'rounded-lg p-3 mb-4',
          progressIndicator.bgColor
        )}>
          <div className="flex items-center gap-2 mb-2">
            <progressIndicator.icon className={cn('w-4 h-4', progressIndicator.color)} />
            <span className={cn('text-sm font-medium', progressIndicator.color)}>
              {progressIndicator.text}
            </span>
          </div>
          <p className="text-xs text-gray-600 dark:text-gray-400">
            {progressIndicator.description}
          </p>
          
          {progress && progress.status === 'in-progress' && (
            <Progress value={progress.completionPercentage} className="h-1.5 mt-2" />
          )}
          

        </div>

        {/* Action Button */}
        <Link to={`/dashboard/quiz/${quiz.id}`} className="block">
          <Button 
            variant={buttonProps.variant} 
            className="w-full flex items-center justify-center gap-2"
          >
            <buttonProps.icon className="w-4 h-4" />
            {buttonProps.text}
          </Button>
        </Link>
      </CardContent>
    </Card>
  );
}
