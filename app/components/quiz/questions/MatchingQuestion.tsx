'use client';

import { useState, useEffect, useLayoutEffect, useRef } from 'react';
import { cn } from '~/lib/utils';
import { CheckCircle, XCircle, AlertCircle, Send, ArrowRight, Lightbulb, Eye, EyeOff } from 'lucide-react';
import type { QuestionComponentProps, MatchingQuestion as MatchingQuestionType } from '../types';

type MatchingQuestionProps = QuestionComponentProps<MatchingQuestionType>

interface MatchPair {
  left: string;
  right: string;
}

export function MatchingQuestion({
  question,
  userAnswer,
  isAnswered,
  isReviewMode,
  showCorrectAnswer,
  onAnswerChange,
  onAnswerSubmit,
  className
}: MatchingQuestionProps) {
  
  const [matches, setMatches] = useState<MatchPair[]>(
    Array.isArray(userAnswer) ? userAnswer : []
  );
  const [startTime] = useState(Date.now());
  const [hasSubmitted, setHasSubmitted] = useState(isAnswered);
  const [selectedLeftItem, setSelectedLeftItem] = useState<string | null>(null);
  const [selectedRightItem, setSelectedRightItem] = useState<string | null>(null);
  const [showHint, setShowHint] = useState(false);
  const [forceArrowUpdate, setForceArrowUpdate] = useState(0);
  const leftItemRefs = useRef<Record<string, HTMLDivElement | null>>({});
  const rightItemRefs = useRef<Record<string, HTMLDivElement | null>>({});

  // Ref callbacks to prevent infinite re-renders
  const setLeftItemRef = (item: string) => (el: HTMLDivElement | null) => {
    leftItemRefs.current[item] = el;
  };

  const setRightItemRef = (item: string) => (el: HTMLDivElement | null) => {
    rightItemRefs.current[item] = el;
  };

  // Shuffle right items for display
  const [shuffledRightItems] = useState(() => {
    if (!question.pairs || !Array.isArray(question.pairs)) {
      return [];
    }
    const items = [...question.pairs.map(p => p.right)];
    return items.sort(() => Math.random() - 0.5);
  });

  // Update matches when userAnswer changes
  useEffect(() => {
    if (Array.isArray(userAnswer)) {
      setMatches(userAnswer);
    }
  }, [userAnswer]);

  // Force arrow update after DOM elements are positioned
  useEffect(() => {
    const timer = setTimeout(() => {
      setForceArrowUpdate(prev => prev + 1);
    }, 100);
    return () => clearTimeout(timer);
  }, [matches, isReviewMode]);

  // Additional effect to ensure arrows are drawn after layout is complete
  useEffect(() => {
    if (matches.length > 0) {
      const timer = setTimeout(() => {
        setForceArrowUpdate(prev => prev + 1);
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [matches.length]);

  // Use layoutEffect for more reliable DOM measurements
  useLayoutEffect(() => {
    if (matches.length > 0 && (isReviewMode || hasSubmitted)) {
      // In review mode, ensure arrows are drawn after layout
      const timer = setTimeout(() => {
        setForceArrowUpdate(prev => prev + 1);
      }, 50);
      return () => clearTimeout(timer);
    }
  }, [matches, isReviewMode, hasSubmitted]);

  const handleLeftItemClick = (leftItem: string) => {
    if (isReviewMode || hasSubmitted) return;
    
    // If this item is already selected, deselect it
    if (selectedLeftItem === leftItem) {
      setSelectedLeftItem(null);
      return;
    }
    
    setSelectedLeftItem(leftItem);
    setSelectedRightItem(null);
  };

  const handleRightItemClick = (rightItem: string) => {
    if (isReviewMode || hasSubmitted) return;
    
    // If no left item is selected, select this right item
    if (!selectedLeftItem) {
      setSelectedRightItem(rightItem);
      return;
    }
    
    // Create the match
    const newMatches = matches.filter(m => m.left !== selectedLeftItem);
    newMatches.push({ left: selectedLeftItem, right: rightItem });
    
    setMatches(newMatches);
    onAnswerChange(newMatches);
    setSelectedLeftItem(null);
    setSelectedRightItem(null);
  };

  const handleRemoveMatch = (leftItem: string) => {
    if (isReviewMode || hasSubmitted) return;
    
    const newMatches = matches.filter(m => m.left !== leftItem);
    setMatches(newMatches);
    onAnswerChange(newMatches);
  };

  const handleSubmit = () => {
    if (hasSubmitted || isReviewMode || matches.length === 0) return;
    
    const timeSpent = Math.round((Date.now() - startTime) / 1000);
    setHasSubmitted(true);
    onAnswerSubmit(matches, timeSpent);
  };

  // Check if a match is correct
  const isMatchCorrect = (leftItem: string, rightItem: string) => {
    if (!hasSubmitted && !showCorrectAnswer) return null;
    if (!question.pairs || !Array.isArray(question.pairs)) return null;
    
    const correctPair = question.pairs.find(p => p.left === leftItem);
    return correctPair?.right === rightItem;
  };

  // Get the user's match for a left item
  const getUserMatch = (leftItem: string) => {
    return matches.find(m => m.left === leftItem)?.right;
  };

  // Get available right items (not yet matched)
  const getAvailableRightItems = () => {
    const matchedRightItems = matches.map(m => m.right);
    return shuffledRightItems.filter(item => !matchedRightItems.includes(item));
  };

  // Get all right items (including matched ones for arrow rendering)
  const getAllRightItems = () => {
    return shuffledRightItems;
  };

  // Calculate arrow path between two elements
  const calculateArrowPath = (leftItem: string, rightItem: string) => {
    const leftEl = leftItemRefs.current[leftItem];
    const rightEl = rightItemRefs.current[rightItem];
    
    if (!leftEl || !rightEl) return null;
    
    const leftRect = leftEl.getBoundingClientRect();
    const rightRect = rightEl.getBoundingClientRect();
    const containerRect = leftEl.closest('.grid')?.getBoundingClientRect();
    
    if (!containerRect) return null;
    
    const startX = leftRect.right - containerRect.left;
    const startY = leftRect.top + leftRect.height / 2 - containerRect.top;
    const endX = rightRect.left - containerRect.left;
    const endY = rightRect.top + rightRect.height / 2 - containerRect.top;
    
    const controlX1 = startX + (endX - startX) * 0.3;
    const controlY1 = startY;
    const controlX2 = startX + (endX - startX) * 0.7;
    const controlY2 = endY;
    
    return {
      path: `M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`,
      startX,
      startY,
      endX,
      endY
    };
  };

  const correctMatches = matches.filter(match => 
    isMatchCorrect(match.left, match.right)
  ).length;
  const totalPairs = question.pairs?.length || 0;
  const allCorrect = correctMatches === totalPairs && matches.length === totalPairs;
  const canSubmit = matches.length > 0 && !hasSubmitted && !isReviewMode;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Question */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Matching
        </h2>
        
        {/* Question metadata */}
        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
          <span>Matching Pairs</span>
          <span>•</span>
          <span>{question.points} point{question.points !== 1 ? 's' : ''}</span>
          <span>•</span>
          <span className="capitalize">{question.difficulty}</span>
          <span>•</span>
          <span>{totalPairs} pair{totalPairs !== 1 ? 's' : ''}</span>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4 mb-6">
          <p className="text-blue-800 dark:text-blue-200 text-sm">
            {question.instruction}
          </p>
        </div>
      </div>

      {/* Matching Interface */}
      <div className="relative grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* SVG overlay for arrows */}
        <svg 
          className="absolute inset-0 w-full h-full pointer-events-none z-10"
          style={{ overflow: 'visible' }}
          key={forceArrowUpdate} // Force re-render when arrows need updating
        >
          {matches.map((match, index) => {
            const arrowData = calculateArrowPath(match.left, match.right);
            if (!arrowData) return null;
            
            const isCorrect = isMatchCorrect(match.left, match.right);
            const strokeColor = isCorrect === true ? '#10b981' : isCorrect === false ? '#ef4444' : '#3b82f6';
            
            return (
              <g key={`${index}-${forceArrowUpdate}`}>
                <path
                  d={arrowData.path}
                  stroke={strokeColor}
                  strokeWidth="2"
                  fill="none"
                  markerEnd="url(#arrowhead)"
                  className="drop-shadow-sm"
                />
              </g>
            );
          })}
          
          {/* Arrow marker definition */}
          <defs>
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto"
            >
              <polygon
                points="0 0, 10 3.5, 0 7"
                fill="currentColor"
                className="text-current"
              />
            </marker>
          </defs>
        </svg>
        {/* Left Column - Items to match */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Items</h3>
          {question.pairs && Array.isArray(question.pairs) ? question.pairs.map((pair, index) => {
            const userMatch = getUserMatch(pair.left);
            const isCorrect = userMatch ? isMatchCorrect(pair.left, userMatch) : null;
            
            return (
              <div
                key={index}
                ref={setLeftItemRef(pair.left)}
                className={cn(
                  "p-4 rounded-lg border-2 transition-all duration-200 cursor-pointer relative z-20",
                  selectedLeftItem === pair.left ? "border-blue-500 bg-blue-100 dark:border-blue-400 dark:bg-blue-900/50 ring-2 ring-blue-300 dark:ring-blue-600" :
                  isCorrect === true ? "border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900/30" :
                  isCorrect === false ? "border-red-300 bg-red-50 dark:border-red-600 dark:bg-red-900/30" :
                  userMatch ? "border-blue-300 bg-blue-50 dark:border-blue-600 dark:bg-blue-900/30" :
                  "border-gray-300 bg-white dark:border-gray-600 dark:bg-gray-800 hover:border-blue-300 hover:bg-blue-50 dark:hover:border-blue-600 dark:hover:bg-blue-900/20",
                  (isReviewMode || hasSubmitted) && "cursor-default"
                )}
                onClick={() => handleLeftItemClick(pair.left)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                      {pair.left}
                    </div>
                    {userMatch && (
                      <div className="flex items-center space-x-2">
                        <ArrowRight className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                        <span className={cn(
                          "text-sm",
                          isCorrect === true ? "text-green-700 dark:text-green-300" :
                          isCorrect === false ? "text-red-700 dark:text-red-300" :
                          "text-blue-700 dark:text-blue-300"
                        )}>
                          {userMatch}
                        </span>
                        {isCorrect !== null && (
                          <div>
                            {isCorrect ? (
                              <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-500 dark:text-red-400" />
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  
                  {userMatch && !isReviewMode && !hasSubmitted && (
                    <button
                      onClick={() => handleRemoveMatch(pair.left)}
                      className="text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 transition-colors"
                    >
                      <XCircle className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            );
          }) : null}
        </div>

        {/* Center Column - Visual separator */}
        <div className="hidden lg:flex items-center justify-center">
          <div className="text-gray-400 dark:text-gray-500">
            <ArrowRight className="h-8 w-8" />
          </div>
        </div>

        {/* Right Column - All options */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Options</h3>
          {getAllRightItems().map((item, index) => {
            const isMatched = matches.some(m => m.right === item);
            const isAvailable = !isMatched;
            
            return (
              <div
                key={index}
                ref={setRightItemRef(item)}
                onClick={() => isAvailable ? handleRightItemClick(item) : undefined}
                className={cn(
                  "p-4 rounded-lg border-2 transition-all duration-200 relative z-20",
                  !isAvailable ? "opacity-50 cursor-default" :
                  selectedRightItem === item ? "border-blue-500 bg-blue-100 dark:border-blue-400 dark:bg-blue-900/50 ring-2 ring-blue-300 dark:ring-blue-600 cursor-pointer" :
                  selectedLeftItem ? "bg-green-50 border-green-300 text-gray-900 dark:bg-green-900/20 dark:border-green-600 dark:text-gray-100 hover:bg-green-100 hover:border-green-400 dark:hover:bg-green-900/30 cursor-pointer" :
                  "bg-gray-50 border-gray-300 text-gray-900 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100 hover:bg-gray-100 hover:border-gray-400 dark:hover:bg-gray-600 dark:hover:border-gray-500 cursor-pointer",
                  (isReviewMode || hasSubmitted) && "cursor-default"
                )}
              >
                <div className="flex items-center space-x-2">
                  {selectedLeftItem && isAvailable && (
                    <ArrowRight className="h-4 w-4 text-green-600 dark:text-green-400" />
                  )}
                  <span>{item}</span>
                  {isMatched && (
                    <div className="ml-auto">
                      {(() => {
                        const match = matches.find(m => m.right === item);
                        if (!match) return null;
                        const isCorrect = isMatchCorrect(match.left, match.right);
                        return isCorrect ? (
                          <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
                        ) : isCorrect === false ? (
                          <XCircle className="h-4 w-4 text-red-500 dark:text-red-400" />
                        ) : null;
                      })()} 
                    </div>
                  )}
                </div>
              </div>
            );
          })}
          

        </div>
      </div>

      {/* Show correct answers in review mode - Similar layout to main question */}
      {(hasSubmitted || showCorrectAnswer) && (
        <div className="mt-6 p-6 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <h4 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-6 flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
            <span>Correct Matches</span>
          </h4>
          
          <div className="relative">
            {/* Display each pair with arrow */}
            <div className="space-y-4">
              {question.pairs.map((pair, index) => (
                <div key={index} className="grid grid-cols-1 lg:grid-cols-3 gap-4 items-center">
                  {/* Left Item */}
                  <div className="p-4 rounded-lg border-2 border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900/20">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                      <span className="font-medium text-gray-900 dark:text-gray-100">{pair.left}</span>
                    </div>
                  </div>
                  
                  {/* Arrow */}
                  <div className="flex justify-center">
                    <div className="flex items-center text-green-600 dark:text-green-400">
                      <ArrowRight className="h-6 w-6" />
                    </div>
                  </div>
                  
                  {/* Right Item */}
                  <div className="p-4 rounded-lg border-2 border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900/20">
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
                      <span className="text-gray-900 dark:text-gray-100">{pair.right}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Hint Section */}
      {question.hint && (
        <div className="text-center">
          <button
            onClick={() => setShowHint(!showHint)}
            className="flex items-center space-x-2 mx-auto px-3 py-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
          >
            {showHint ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            <span>{showHint ? 'Hide Hint' : 'Show Hint'}</span>
          </button>
          
          {showHint && (
            <div className="mt-3 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
              <div className="flex items-start space-x-2">
                <Lightbulb className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-yellow-800 dark:text-yellow-200">
                  {question.hint}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Submit button */}
      {!isReviewMode && !hasSubmitted && (
        <div className="flex justify-center">
          <button
            onClick={handleSubmit}
            disabled={!canSubmit}
            className={cn(
              "flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors",
              canSubmit
                ? "bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-600 dark:hover:bg-blue-700"
                : "bg-gray-300 text-gray-500 cursor-not-allowed dark:bg-gray-600 dark:text-gray-400"
            )}
          >
            <Send className="h-4 w-4" />
            <span>Submit Matches</span>
          </button>
        </div>
      )}

      {/* Feedback */}
      {(hasSubmitted || showCorrectAnswer) && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          {allCorrect ? (
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-green-800 dark:text-green-200 mb-1">Perfect!</div>
                <div className="text-sm text-green-700 dark:text-green-300">
                  All pairs matched correctly ({correctMatches}/{totalPairs})
                </div>
              </div>
            </div>
          ) : correctMatches > 0 ? (
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-6 w-6 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">Partially Correct</div>
                <div className="text-sm text-yellow-700 dark:text-yellow-300">
                  {correctMatches} out of {totalPairs} pairs matched correctly
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-start space-x-3">
              <XCircle className="h-6 w-6 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-red-800 dark:text-red-200 mb-1">Incorrect</div>
                <div className="text-sm text-red-700 dark:text-red-300">
                  None of the pairs were matched correctly
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      {!hasSubmitted && !isReviewMode && (
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          {selectedLeftItem ? (
            <p className="text-blue-600 dark:text-blue-400 font-medium">
              Now click the matching option on the right for "{selectedLeftItem}"
            </p>
          ) : (
            <p>Click an item on the left, then click its matching option on the right</p>
          )}
        </div>
      )}

      {/* Review mode indicator */}
      {isReviewMode && (
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <AlertCircle className="h-4 w-4" />
          <span>Review Mode - Matches cannot be changed</span>
        </div>
      )}
    </div>
  );
}