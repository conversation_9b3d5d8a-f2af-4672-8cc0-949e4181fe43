'use client';

import { useState, useEffect } from 'react';
import { cn } from '~/lib/utils';
import { CheckCircle, XCircle, AlertCircle, Send, Lightbulb, Eye, EyeOff } from 'lucide-react';
import type { QuestionComponentProps, MultipleChoiceQuestion as MultipleChoiceQuestionType } from '../types';

type MultipleChoiceQuestionProps = QuestionComponentProps<MultipleChoiceQuestionType>

export function MultipleChoiceQuestion({
  question,
  userAnswer,
  isAnswered,
  isReviewMode,
  showCorrectAnswer,
  onAnswerChange,
  onAnswerSubmit,
  className
}: MultipleChoiceQuestionProps) {

  
  const [selectedOption, setSelectedOption] = useState<number | null>(
    typeof userAnswer === 'number' ? userAnswer : null
  );
  const [startTime] = useState(Date.now());
  const [hasSubmitted, setHasSubmitted] = useState(isAnswered);
  const [showHint, setShowHint] = useState(false);

  // Update selected option when userAnswer changes
  useEffect(() => {
    if (typeof userAnswer === 'number') {
      setSelectedOption(userAnswer);
    }
  }, [userAnswer]);

  // Update hasSubmitted when isAnswered prop changes
  useEffect(() => {
    setHasSubmitted(isAnswered);
  }, [isAnswered]);

  const handleOptionSelect = (optionIndex: number) => {
    if (isReviewMode || hasSubmitted) return;
    
    setSelectedOption(optionIndex);
    onAnswerChange(optionIndex);
  };

  const handleSubmit = () => {
    if (selectedOption === null || hasSubmitted || isReviewMode) return;
    
    const timeSpent = Math.round((Date.now() - startTime) / 1000);
    setHasSubmitted(true);
    onAnswerSubmit(selectedOption, timeSpent);
  };

  const getOptionStatus = (optionIndex: number) => {
    if (!showCorrectAnswer && !hasSubmitted) {
      return selectedOption === optionIndex ? 'selected' : 'default';
    }

    const isCorrect = optionIndex === question.correctAnswerIndex;
    const isSelected = selectedOption === optionIndex;

    if (showCorrectAnswer || hasSubmitted) {
      if (isCorrect) return 'correct';
      if (isSelected && !isCorrect) return 'incorrect';
      return 'default';
    }

    return selectedOption === optionIndex ? 'selected' : 'default';
  };

  const getOptionStyles = (status: string) => {
    switch (status) {
      case 'correct':
        return 'bg-green-100 dark:bg-green-900/30 border-green-300 dark:border-green-600 text-green-800 dark:text-green-200';
      case 'incorrect':
        return 'bg-red-100 dark:bg-red-900/30 border-red-300 dark:border-red-600 text-red-800 dark:text-red-200';
      case 'selected':
        return 'bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-600 text-blue-800 dark:text-blue-200';
      default:
        return 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700';
    }
  };

  const getOptionIcon = (status: string) => {
    switch (status) {
      case 'correct':
        return <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />;
      case 'incorrect':
        return <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />;
      case 'selected':
        return <div className="h-5 w-5 bg-blue-600 dark:bg-blue-500 rounded-full" />;
      default:
        return <div className="h-5 w-5 border-2 border-gray-300 dark:border-gray-600 rounded-full" />;
    }
  };

  const canSubmit = selectedOption !== null && !hasSubmitted && !isReviewMode;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Question */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          {question.question}
        </h2>
        
        {/* Question metadata */}
        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
          <span>Multiple Choice</span>
          <span>•</span>
          <span>{question.points} point{question.points !== 1 ? 's' : ''}</span>
          <span>•</span>
          <span className="capitalize">{question.difficulty}</span>
        </div>
      </div>

      {/* Options */}
      <div className="space-y-3">
        {question.options.map((option, index) => {
          const status = getOptionStatus(index);
          const optionLetter = String.fromCharCode(65 + index); // A, B, C, D
          
          return (
            <button
              key={index}
              onClick={() => handleOptionSelect(index)}
              disabled={isReviewMode || hasSubmitted}
              className={cn(
                "w-full p-4 rounded-lg border-2 text-left transition-all duration-200",
                "flex items-center space-x-3",
                getOptionStyles(status),
                (isReviewMode || hasSubmitted) ? "cursor-default" : "cursor-pointer hover:shadow-sm"
              )}
            >
              {/* Option letter */}
              <div className={cn(
                "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
                status === 'correct' ? "bg-green-600 dark:bg-green-500 text-white" :
                status === 'incorrect' ? "bg-red-600 dark:bg-red-500 text-white" :
                status === 'selected' ? "bg-blue-600 dark:bg-blue-500 text-white" :
                "bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300"
              )}>
                {optionLetter}
              </div>

              {/* Option text */}
              <div className="flex-1 text-left">
                {option}
              </div>

              {/* Status icon */}
              <div className="flex-shrink-0">
                {getOptionIcon(status)}
              </div>
            </button>
          );
        })}
      </div>

      {/* Hint Section */}
      {question.hint && (
        <div className="text-center">
          <button
            onClick={() => setShowHint(!showHint)}
            className="flex items-center space-x-2 mx-auto px-3 py-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
          >
            {showHint ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            <span>{showHint ? 'Hide Hint' : 'Show Hint'}</span>
          </button>
          
          {showHint && (
            <div className="mt-3 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
              <div className="flex items-start space-x-2">
                <Lightbulb className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-yellow-800 dark:text-yellow-200">
                  {question.hint}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Submit button */}
      {!isReviewMode && !hasSubmitted && (
        <div className="flex justify-center">
          <button
            onClick={handleSubmit}
            disabled={!canSubmit}
            className={cn(
              "flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors",
              canSubmit
                ? "bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white"
                : "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
            )}
          >
            <Send className="h-4 w-4" />
            <span>Submit Answer</span>
          </button>
        </div>
      )}

      {/* Feedback */}
      {(hasSubmitted || showCorrectAnswer) && (
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
          {selectedOption === question.correctAnswerIndex ? (
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-green-800 dark:text-green-200 mb-1">Correct!</div>
                <div className="text-sm text-green-700 dark:text-green-300 mb-2">
                  The correct answer is: <strong>{question.options[question.correctAnswerIndex]}</strong>
                </div>
                {question.explanation && (
                  <div className="text-sm text-green-700 dark:text-green-300">
                    {question.explanation}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex items-start space-x-3">
              <XCircle className="h-6 w-6 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-red-800 dark:text-red-200 mb-1">Incorrect</div>
                <div className="text-sm text-red-700 dark:text-red-300 mb-2">
                  The correct answer was: <strong>{question.options[question.correctAnswerIndex]}</strong>
                </div>
                {question.explanation && (
                  <div className="text-sm text-red-700 dark:text-red-300">
                    {question.explanation}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      {!hasSubmitted && !isReviewMode && (
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          <p>Select one answer and click submit</p>
        </div>
      )}

      {/* Review mode indicator */}
      {isReviewMode && (
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <AlertCircle className="h-4 w-4" />
          <span>Review Mode - Answer cannot be changed</span>
        </div>
      )}
    </div>
  );
}