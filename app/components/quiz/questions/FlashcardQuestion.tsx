'use client';

import { useState, useEffect } from 'react';
import { cn } from '~/lib/utils';
import { RotateCcw, Eye, EyeOff, CheckCircle, XCircle, Lightbulb, AlertCircle } from 'lucide-react';
import type { QuestionComponentProps, FlashcardQuestion as FlashcardQuestionType } from '../types';

type FlashcardQuestionProps = QuestionComponentProps<FlashcardQuestionType>

export function FlashcardQuestion({
  question,
  userAnswer,
  isAnswered,
  isReviewMode,
  showCorrectAnswer,
  onAnswerChange,
  onAnswerSubmit,
  className
}: FlashcardQuestionProps) {
  console.log('Flashcard question data:', {
    front: question.front,
    back: question.back,
    hint: question.hint,
    type: question.type
  });
  const [isFlipped, setIsFlipped] = useState(false);
  const [showHint, setShowHint] = useState(false);
  const [startTime] = useState(Date.now());
  const [selfAssessment, setSelfAssessment] = useState<'correct' | 'incorrect' | null>(
    userAnswer || null
  );
  const [hasSubmitted, setHasSubmitted] = useState(false);

  // Reset state when question changes
  useEffect(() => {
    setIsFlipped(false);
    setShowHint(false);
    setSelfAssessment(userAnswer || null);
    setHasSubmitted(false);
  }, [question.id, userAnswer]);

  // Sync hasSubmitted state with isAnswered prop
  useEffect(() => {
    setHasSubmitted(isAnswered);
  }, [isAnswered]);

  // Auto-flip only when showCorrectAnswer is true (not just review mode)
  useEffect(() => {
    if (showCorrectAnswer) {
      setIsFlipped(true);
    }
  }, [showCorrectAnswer]);

  const handleFlip = () => {
    console.log('Flipping card from', isFlipped, 'to', !isFlipped);
    setIsFlipped(!isFlipped);
  };

  const handleSelfAssessment = (assessment: 'correct' | 'incorrect') => {
    if (hasSubmitted || isReviewMode) return;
    
    setSelfAssessment(assessment);
    onAnswerChange(assessment);

    if (!isReviewMode) {
      const timeSpent = Math.round((Date.now() - startTime) / 1000);
      setHasSubmitted(true);
      onAnswerSubmit(assessment, timeSpent);
    }
  };

  const handleReset = () => {
    setIsFlipped(false);
    setSelfAssessment(null);
    setShowHint(false);
    onAnswerChange(null);
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Question Header */}
      <div className="text-center">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Flashcard</h2>
        <p className="text-gray-600 dark:text-gray-400">
          Study the term/question, then flip to see the answer and assess yourself
        </p>
      </div>

      {/* Flashcard */}
      <div className="flex justify-center" style={{ perspective: '1000px' }}>
        <div className="relative w-full max-w-md">
          {/* Card Container */}
          <div
            className={cn(
              "relative w-full h-64 cursor-pointer transition-transform duration-500 transform-style-preserve-3d",
              isFlipped && "rotate-y-180"
            )}
            onClick={handleFlip}
            style={{
              transformStyle: 'preserve-3d',
              transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)'
            }}
          >
            {/* Front Side */}
            <div
              className={cn(
                "absolute inset-0 w-full h-full backface-hidden",
                "bg-gradient-to-br from-blue-500 to-blue-600 dark:from-blue-600 dark:to-blue-700 rounded-xl shadow-lg",
                "flex items-center justify-center p-6 text-white"
              )}
              style={{
                backfaceVisibility: 'hidden',
                transform: 'rotateY(0deg)'
              }}
            >
              <div className="text-center">
                <div className="text-lg font-medium mb-4">
                  {question.front}
                </div>
                {!isFlipped && (
                  <div className="text-sm opacity-75">
                    Click to flip
                  </div>
                )}
              </div>
            </div>

            {/* Back Side */}
            <div
              className={cn(
                "absolute inset-0 w-full h-full backface-hidden rotate-y-180",
                "bg-gradient-to-br from-green-500 to-green-600 dark:from-green-600 dark:to-green-700 rounded-xl shadow-lg",
                "flex items-center justify-center p-6 text-white"
              )}
              style={{
                backfaceVisibility: 'hidden',
                transform: 'rotateY(180deg)'
              }}
            >
              <div className="text-center">
                <div className="text-lg font-medium">
                  {question.back}
                </div>
              </div>
            </div>
          </div>

          {/* Flip Indicator */}
          <div className="absolute top-4 right-4 bg-white dark:bg-gray-800 bg-opacity-20 dark:bg-opacity-20 rounded-full p-2">
            <RotateCcw className="h-4 w-4 text-white" />
          </div>
        </div>
      </div>

      {/* Hint Section */}
      {question.hint && (
        <div className="text-center">
          <button
            onClick={() => setShowHint(!showHint)}
            className="flex items-center space-x-2 mx-auto px-3 py-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
          >
            {showHint ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            <span>{showHint ? 'Hide Hint' : 'Show Hint'}</span>
          </button>

          {showHint && (
            <div className="mt-3 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
              <div className="flex items-start space-x-2">
                <Lightbulb className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-yellow-800 dark:text-yellow-200">
                  {question.hint}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Self Assessment */}
      {isFlipped && !isReviewMode && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4 text-center">
            How did you do?
          </h3>

          <div className="flex justify-center space-x-4">
            <button
              onClick={() => handleSelfAssessment('correct')}
              disabled={hasSubmitted || isReviewMode}
              className={cn(
                "flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors",
                hasSubmitted || isReviewMode ? "opacity-50 cursor-not-allowed" : "",
                selfAssessment === 'correct'
                  ? "bg-green-600 dark:bg-green-700 text-white"
                  : "bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/30"
              )}
            >
              <CheckCircle className="h-5 w-5" />
              <span>I got it right</span>
            </button>

            <button
              onClick={() => handleSelfAssessment('incorrect')}
              disabled={hasSubmitted || isReviewMode}
              className={cn(
                "flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors",
                hasSubmitted || isReviewMode ? "opacity-50 cursor-not-allowed" : "",
                selfAssessment === 'incorrect'
                  ? "bg-red-600 dark:bg-red-700 text-white"
                  : "bg-red-100 dark:bg-red-900/20 text-red-700 dark:text-red-300 hover:bg-red-200 dark:hover:bg-red-900/30"
              )}
            >
              <XCircle className="h-5 w-5" />
              <span>I need to study this</span>
            </button>
          </div>
        </div>
      )}

      {/* Review Mode Display */}
      {isReviewMode && userAnswer && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div className="flex items-center justify-center space-x-2">
            {userAnswer === 'correct' ? (
              <>
                <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
                <span className="text-green-700 dark:text-green-300 font-medium">You marked this as correct</span>
              </>
            ) : (
              <>
                <XCircle className="h-5 w-5 text-red-500 dark:text-red-400" />
                <span className="text-red-700 dark:text-red-300 font-medium">You marked this for review</span>
              </>
            )}
          </div>
        </div>
      )}

      {/* Reset Button */}
      {(isFlipped || selfAssessment) && !isReviewMode && (
        <div className="text-center">
          <button
            onClick={handleReset}
            className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 underline text-sm"
          >
            Reset and try again
          </button>
        </div>
      )}

      {/* Instructions */}
      {!isFlipped && !isReviewMode && (
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          <p>Click the card to reveal the answer, then assess your knowledge</p>
        </div>
      )}

       {/* Review mode indicator */}
      {isReviewMode && (
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <AlertCircle className="h-4 w-4" />
          <span>Review Mode - Answer cannot be changed</span>
        </div>
      )}
    </div>
  );
}