'use client';

import { useState, useEffect } from 'react';
import { cn } from '~/lib/utils';
import { CheckCircle, XCircle, AlertCircle, Send, FileText, Eye, EyeOff, Lightbulb } from 'lucide-react';
import type { QuestionComponentProps, FreeTextQuestion as FreeTextQuestionType } from '../types';

type FreeTextQuestionProps = QuestionComponentProps<FreeTextQuestionType>

export function FreeTextQuestion({
  question,
  userAnswer,
  isAnswered,
  isReviewMode,
  showCorrectAnswer,
  onAnswerChange,
  onAnswerSubmit,
  className
}: FreeTextQuestionProps) {
  const [answer, setAnswer] = useState<string>(
    typeof userAnswer === 'string' ? userAnswer : ''
  );
  const [startTime] = useState(Date.now());
  const [hasSubmitted, setHasSubmitted] = useState(isAnswered);
  const [showSampleAnswer, setShowSampleAnswer] = useState(false);
  const [showCriteria, setShowCriteria] = useState(false);
  const [showHint, setShowHint] = useState(false);

  // Update answer when userAnswer changes
  useEffect(() => {
    if (typeof userAnswer === 'string') {
      setAnswer(userAnswer);
    }
  }, [userAnswer]);

  const handleAnswerChange = (value: string) => {
    if (isReviewMode || hasSubmitted) return;
    
    setAnswer(value);
    onAnswerChange(value);
  };

  const handleSubmit = () => {
    if (hasSubmitted || isReviewMode || answer.trim().length === 0) return;
    
    const timeSpent = Math.round((Date.now() - startTime) / 1000);
    setHasSubmitted(true);
    onAnswerSubmit(answer, timeSpent);
  };

  const wordCount = answer.trim().split(/\s+/).filter(word => word.length > 0).length;
  const characterCount = answer.length;
  const isOverLimit = characterCount > question.maxLength;
  const canSubmit = answer.trim().length > 0 && !isOverLimit && !hasSubmitted && !isReviewMode;

  // Get minimum word count based on answer type
  const getMinWords = () => {
    return question.answerType === 'short' ? 10 : 50;
  };

  const minWords = getMinWords();
  const hasMinWords = wordCount >= minWords;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Question */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Free Text Answer
        </h2>
        
        {/* Question metadata */}
        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
          <span>Free Text</span>
          <span>•</span>
          <span>{question.points} point{question.points !== 1 ? 's' : ''}</span>
          <span>•</span>
          <span className="capitalize">{question.difficulty}</span>
          <span>•</span>
          <span className="capitalize">{question.answerType} form</span>
        </div>

        {/* Question text */}
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6 mb-6">
          <p className="text-lg text-gray-900 dark:text-gray-100 leading-relaxed">
            {question.question}
          </p>
        </div>
      </div>

      {/* Evaluation Criteria */}
      <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4">
        <button
          onClick={() => setShowCriteria(!showCriteria)}
          className="flex items-center space-x-2 text-blue-800 hover:text-blue-900 dark:text-blue-200 dark:hover:text-blue-100 transition-colors"
        >
          {showCriteria ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          <span className="font-medium">
            {showCriteria ? 'Hide' : 'Show'} Evaluation Criteria
          </span>
        </button>
        
        {showCriteria && (
          <div className="mt-3 space-y-2">
            <p className="text-sm text-blue-700 dark:text-blue-300 mb-2">Your answer will be evaluated based on:</p>
            <ul className="space-y-1">
              {question.evaluationCriteria.map((criterion, index) => (
                <li key={index} className="flex items-start space-x-2 text-sm text-blue-700 dark:text-blue-300">
                  <span className="text-blue-500 dark:text-blue-400 mt-1">•</span>
                  <span>{criterion}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Answer Input */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Your Answer
          </label>
          <div className="flex items-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
            <span className={cn(
              wordCount >= minWords ? "text-green-600 dark:text-green-400" : "text-yellow-600 dark:text-yellow-400"
            )}>
              {wordCount} words (min: {minWords})
            </span>
            <span className={cn(
              isOverLimit ? "text-red-600 dark:text-red-400" : "text-gray-500 dark:text-gray-400"
            )}>
              {characterCount}/{question.maxLength} characters
            </span>
          </div>
        </div>

        <textarea
          value={answer}
          onChange={(e) => handleAnswerChange(e.target.value)}
          disabled={isReviewMode || hasSubmitted}
          placeholder={`Write your ${question.answerType} answer here...`}
          className={cn(
            "w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none transition-colors",
            "dark:focus:ring-blue-400 dark:focus:border-blue-400",
            isOverLimit ? "border-red-300 focus:ring-red-500 focus:border-red-500 dark:border-red-600 dark:focus:ring-red-400 dark:focus:border-red-400" :
            hasMinWords ? "border-green-300 dark:border-green-600" :
            "border-gray-300 dark:border-gray-600",
            isReviewMode || hasSubmitted ? "bg-gray-50 cursor-not-allowed dark:bg-gray-700" : "bg-white dark:bg-gray-800",
            "text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
          )}
          rows={question.answerType === 'short' ? 4 : 8}
          maxLength={question.maxLength}
        />

        {/* Character limit warning */}
        {isOverLimit && (
          <div className="flex items-center space-x-2 text-sm text-red-600 dark:text-red-400">
            <AlertCircle className="h-4 w-4" />
            <span>Answer exceeds maximum length limit</span>
          </div>
        )}

        {/* Word count guidance */}
        {!hasMinWords && answer.trim().length > 0 && (
          <div className="flex items-center space-x-2 text-sm text-yellow-600 dark:text-yellow-400">
            <AlertCircle className="h-4 w-4" />
            <span>Consider providing more detail to meet the minimum word count</span>
          </div>
        )}
      </div>

      {/* Sample Answer */}
      {(hasSubmitted || showCorrectAnswer) && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <button
            onClick={() => setShowSampleAnswer(!showSampleAnswer)}
            className="flex items-center space-x-2 text-gray-700 hover:text-gray-900 dark:text-gray-300 dark:hover:text-gray-100 transition-colors mb-3"
          >
            {showSampleAnswer ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            <span className="font-medium">
              {showSampleAnswer ? 'Hide' : 'Show'} Sample Answer
            </span>
          </button>
          
          {showSampleAnswer && (
            <div className="bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
              <div className="flex items-center space-x-2 mb-3">
                <FileText className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Sample Answer</span>
              </div>
              <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                {question.sampleAnswer}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Hint Section */}
      {question.hint && (
        <div className="text-center">
          <button
            onClick={() => setShowHint(!showHint)}
            className="flex items-center space-x-2 mx-auto px-3 py-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
          >
            {showHint ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            <span>{showHint ? 'Hide Hint' : 'Show Hint'}</span>
          </button>
          
          {showHint && (
            <div className="mt-3 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
              <div className="flex items-start space-x-2">
                <Lightbulb className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-yellow-800 dark:text-yellow-200">
                  {question.hint}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Submit button */}
      {!isReviewMode && !hasSubmitted && (
        <div className="flex justify-center">
          <button
            onClick={handleSubmit}
            disabled={!canSubmit}
            className={cn(
              "flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors",
              canSubmit
                ? "bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-600 dark:hover:bg-blue-700"
                : "bg-gray-300 text-gray-500 cursor-not-allowed dark:bg-gray-600 dark:text-gray-400"
            )}
          >
            <Send className="h-4 w-4" />
            <span>Submit Answer</span>
          </button>
        </div>
      )}

      {/* Feedback */}
      {(hasSubmitted || showCorrectAnswer) && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          <div className="flex items-start space-x-3">
            <CheckCircle className="h-6 w-6 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
            <div>
              <div className="font-medium text-blue-800 dark:text-blue-200 mb-1">Answer Submitted</div>
              <div className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                Your answer has been recorded and will be evaluated based on the criteria above.
              </div>
              
              {/* Answer stats */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Word count:</span>
                  <span className="ml-2 font-medium text-gray-900 dark:text-gray-100">{wordCount}</span>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">Character count:</span>
                  <span className="ml-2 font-medium text-gray-900 dark:text-gray-100">{characterCount}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      {!hasSubmitted && !isReviewMode && (
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          <p>
            Provide a {question.answerType} answer that addresses the question thoroughly.
            {question.answerType === 'short' && ' Keep it concise but complete.'}
            {question.answerType === 'long' && ' Include detailed explanations and examples where appropriate.'}
          </p>
        </div>
      )}

      {/* Review mode indicator */}
      {isReviewMode && (
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <AlertCircle className="h-4 w-4" />
          <span>Review Mode - Answer cannot be changed</span>
        </div>
      )}
    </div>
  );
}