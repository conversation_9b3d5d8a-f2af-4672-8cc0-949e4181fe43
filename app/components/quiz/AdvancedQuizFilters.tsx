import { useState, useEffect } from "react";
import { But<PERSON> } from "~/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/components/ui/select";
import { useGetMyLearningContent } from "~/lib/hooks/use-learning-api";
import { SearchFilterShell } from "~/components/shared/SearchFilterShell";
import { ActiveFiltersDisplay } from "~/components/shared/ActiveFiltersDisplay";
import { FilterField } from "~/components/shared/FilterField";

export interface QuizFilters {
  search: string;
  difficulty: 'all' | 'easy' | 'medium' | 'hard';
  completionStatus: 'all' | 'completed' | 'in-progress' | 'not-started';
  learningContentId?: string;
}

interface AdvancedQuizFiltersProps {
  filters: QuizFilters;
  onFiltersChange: (filters: QuizFilters) => void;
  onClearFilters: () => void;
  className?: string;
}

export function AdvancedQuizFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  className = "",
}: AdvancedQuizFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchValue, setSearchValue] = useState(filters.search);

  // Fetch learning content for filter dropdown
  const { data: learningData } = useGetMyLearningContent({
    limit: 100,
  });

  const learningContent = learningData?.content || [];

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchValue !== filters.search) {
        onFiltersChange({ ...filters, search: searchValue });
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchValue, filters, onFiltersChange]);

  // Update local search state when filters change externally
  useEffect(() => {
    setSearchValue(filters.search);
  }, [filters.search]);

  const handleFilterChange = (key: keyof QuizFilters, value: any) => {
    onFiltersChange({ ...filters, [key]: value });
  };

  const hasActiveFilters =
    filters.search ||
    filters.difficulty !== 'all' ||
    filters.completionStatus !== 'all' ||
    filters.learningContentId;

  const activeFilterCount = [
    filters.search,
    filters.difficulty !== 'all' ? filters.difficulty : null,
    filters.completionStatus !== 'all' ? filters.completionStatus : null,
    filters.learningContentId,
  ].filter(Boolean).length;

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'hard': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'in-progress': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'not-started': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  return (
    <SearchFilterShell
      className={className}
      filters={filters}
      searchValue={searchValue}
      onSearchChange={setSearchValue}
      searchPlaceholder="Search quizzes by title, description, or learning content..."
      isExpanded={isExpanded}
      onExpandedChange={setIsExpanded}
      onSetFilter={(k, v) => handleFilterChange(k as keyof QuizFilters, v)}
      getActiveCount={() => activeFilterCount}
      renderAdvanced={({ filters }) => (
        <>

          {/* Difficulty Filter */}
          <FilterField title="Difficulty">
            <Select
              value={filters.difficulty}
              onValueChange={(value) => handleFilterChange('difficulty', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select difficulty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Difficulties</SelectItem>
                <SelectItem value="easy">Easy</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="hard">Hard</SelectItem>
              </SelectContent>
            </Select>
          </FilterField>

          {/* Completion Status Filter */}
          <FilterField title="Completion Status">
            <Select
              value={filters.completionStatus}
              onValueChange={(value) => handleFilterChange('completionStatus', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="not-started">Not Started</SelectItem>
              </SelectContent>
            </Select>
          </FilterField>

          {/* Learning Content Filter */}
          <FilterField title="Learning Content">
            <Select
              value={filters.learningContentId || "all"}
              onValueChange={(value) =>
                handleFilterChange('learningContentId', value === "all" ? undefined : value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select content" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Content</SelectItem>
                {learningContent.map((content) => (
                  <SelectItem key={content.id} value={content.id}>
                    {content.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </FilterField>
        </>
      )}
      renderActiveBadges={({ filters }) => {
        const badges = [] as Array<{ key: string; label: React.ReactNode; onRemove?: () => void; className?: string }>;
        if (filters.search) {
          badges.push({
            key: 'search',
            label: <>Search: "{filters.search}"</>,
            onRemove: () => handleFilterChange('search', ''),
          });
        }
        if (filters.difficulty !== 'all') {
          badges.push({
            key: 'difficulty',
            label: <>{filters.difficulty.charAt(0).toUpperCase() + filters.difficulty.slice(1)}</>,
            onRemove: () => handleFilterChange('difficulty', 'all'),
            className: getDifficultyColor(filters.difficulty),
          });
        }
        if (filters.completionStatus !== 'all') {
          badges.push({
            key: 'status',
            label: <>{filters.completionStatus.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</>,
            onRemove: () => handleFilterChange('completionStatus', 'all'),
            className: getStatusColor(filters.completionStatus),
          });
        }
        if (filters.learningContentId) {
          badges.push({
            key: 'content',
            label: <>Content: {learningContent.find(c => c.id === filters.learningContentId)?.title || 'Unknown'}</>,
            onRemove: () => handleFilterChange('learningContentId', undefined),
          });
        }
        return (
          <ActiveFiltersDisplay
            badges={badges}
            onClearAll={onClearFilters}
          />
        );
      }}
    />
  );
}
