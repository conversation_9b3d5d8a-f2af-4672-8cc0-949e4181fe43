'use client';

import { cn } from "~/lib/utils";
import { FlashcardQuestion } from "./questions/FlashcardQuestion";
import { MultipleChoiceQuestion } from "./questions/MultipleChoiceQuestion";
import { TrueFalseQuestion } from "./questions/TrueFalseQuestion";
import { FillInBlankQuestion } from "./questions/FillInBlankQuestion";
import { MatchingQuestion } from "./questions/MatchingQuestion";
import { FreeTextQuestion } from "./questions/FreeTextQuestion";
import { OrderingQuestion } from "./questions/OrderingQuestion";
import type { QuizQuestion, QuestionComponentProps } from "./types";

interface QuestionRendererProps {
  question: QuizQuestion;
  userAnswer?: any;
  isAnswered: boolean;
  isReviewMode: boolean;
  showCorrectAnswer: boolean;
  onAnswerChange: (answer: any) => void;
  onAnswerSubmit: (answer: any, timeSpent: number) => void;
  className?: string;
}

export function QuestionRenderer({
  question,
  userAnswer,
  isAnswered,
  isReviewMode,
  showCorrectAnswer,
  onAnswerChange,
  onAnswerSubmit,
  className
}: QuestionRendererProps) {
  // Common props for all question components
  const commonProps: QuestionComponentProps = {
    question,
    userAnswer,
    isAnswered,
    isReviewMode,
    showCorrectAnswer,
    onAnswerChange,
    onAnswerSubmit,
    className: cn("w-full", className)
  };

  // Render the appropriate question component based on type
  switch (question.type) {
    case 'flashcard':
      return <FlashcardQuestion {...commonProps} question={question} />;
    
    case 'multipleChoice':
      return <MultipleChoiceQuestion {...commonProps} question={question} />;
    
    case 'trueFalse':
      return <TrueFalseQuestion {...commonProps} question={question} />;
    
    case 'fillInBlank':
      return <FillInBlankQuestion {...commonProps} question={question} />;
    
    case 'matching':
      return <MatchingQuestion {...commonProps} question={question} />;
    
    case 'freeText':
      return <FreeTextQuestion {...commonProps} question={question} />;
    
    case 'ordering':
      return <OrderingQuestion {...commonProps} question={question} />;
    
    default:
      return (
        <div className={cn("p-6 text-center", className)}>
          <div className="text-red-600 dark:text-red-400 font-medium">
            Unknown question type: {(question as any).type}
          </div>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            This question type is not supported yet.
          </p>
        </div>
      );
  }
}