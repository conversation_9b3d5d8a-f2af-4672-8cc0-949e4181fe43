import { cn } from "~/lib/utils";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Question<PERSON>enderer } from "./QuestionRenderer";
import { ArrowLeft, CheckCircle, XCircle, Clock, Trophy } from "lucide-react";
import type { Quiz as SchemaQuiz, QuizAttempt as SchemaQuizAttempt } from "~/db/schema/quiz";
import type { Quiz as ComponentQuiz, QuizAttempt as ComponentQuizAttempt, QuestionResult } from "./types";

// Union types to support both database schema and component types
type QuizType = SchemaQuiz | ComponentQuiz;
type QuizAttemptType = SchemaQuizAttempt | ComponentQuizAttempt;

interface QuizReviewProps {
  quiz: QuizType;
  attempt: QuizAttemptType;
  questionResults: QuestionResult[];
  onBackToResults?: () => void;
  onExit?: () => void;
  className?: string;
}

export function QuizReview({ quiz, attempt, questionResults, onBackToResults, onExit, className }: QuizReviewProps) {
  // Create a map of question results for easy lookup
  const resultsMap = new Map(
    questionResults.map(result => [result.questionId, result])
  );

  // Create a map of user answers for easy lookup
  const answersMap = new Map(
    (attempt.answers ?? []).map(answer => [answer.questionId, answer])
  );

  // Calculate summary stats
  const totalQuestions = quiz.questions.length;
  const correctAnswers = questionResults.filter(r => r.isCorrect).length;
  const answeredQuestions = questionResults.length;
  const incorrectAnswers = answeredQuestions - correctAnswers;
  const unansweredQuestions = totalQuestions - answeredQuestions;
  const accuracy = totalQuestions > 0 ? Math.round((correctAnswers / totalQuestions) * 100) : 0;



  return (
    <div className={cn("max-w-4xl mx-auto p-6", className)}>
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Review: {quiz.title}
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Review your answers and see the correct solutions
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {onBackToResults && (
              <Button variant="outline" onClick={onBackToResults}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Results
              </Button>
            )}
            {onExit && (
              <Button variant="outline" onClick={onExit}>
                Exit Review
              </Button>
            )}
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
              {totalQuestions}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              Total Questions
            </div>
          </div>
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
              {correctAnswers}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              Correct
            </div>
          </div>
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-red-600 dark:text-red-400 mb-1">
              {incorrectAnswers}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              Incorrect
            </div>
          </div>
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-gray-500 dark:text-gray-400 mb-1">
              {unansweredQuestions}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              Unanswered
            </div>
          </div>
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">
              {accuracy}%
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              Accuracy
            </div>
          </div>
        </div>
      </div>

      {/* Questions Review */}
      <div className="space-y-6">
        {quiz.questions.map((question, index) => {
          const result = resultsMap.get(question.id);
          const answerData = answersMap.get(question.id);
          const userAnswer = answerData?.answer;
          const isAnswered = !!answerData; // Check if there's any answer data, not just userAnswer
          const isCorrect = result?.isCorrect || false;

          return (
            <div
              key={question.id}
              id={`question-${question.id}`}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6"
            >
              {/* Question Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400 rounded-full font-semibold text-sm">
                    {index + 1}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    Question {index + 1}
                  </h3>
                </div>
                <div className="flex items-center space-x-2">
                  {!isAnswered ? (
                    <div className="flex items-center space-x-1 text-gray-500 dark:text-gray-400">
                      <XCircle className="h-5 w-5" />
                      <span className="text-sm font-medium">Not Answered</span>
                    </div>
                  ) : isCorrect ? (
                    <div className="flex items-center space-x-1 text-green-600 dark:text-green-400">
                      <CheckCircle className="h-5 w-5" />
                      <span className="text-sm font-medium">Correct</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1 text-red-600 dark:text-red-400">
                      <XCircle className="h-5 w-5" />
                      <span className="text-sm font-medium">Incorrect</span>
                    </div>
                  )}
                  {answerData?.timeSpent && (
                    <div className="flex items-center space-x-1 text-gray-500 dark:text-gray-400">
                      <Clock className="h-4 w-4" />
                      <span className="text-sm">{Math.round(answerData.timeSpent)}s</span>
                    </div>
                  )}
                  {result?.pointsEarned !== undefined && (
                    <div className="flex items-center space-x-1 text-purple-600 dark:text-purple-400">
                      <Trophy className="h-4 w-4" />
                      <span className="text-sm">{result.pointsEarned}/{question.points} pts</span>
                    </div>
                  )}
                </div>
              </div>

              {/* Question Content */}
              <QuestionRenderer
                question={question as ComponentQuiz['questions'][0]}
                userAnswer={userAnswer}
                isAnswered={isAnswered}
                isReviewMode={true}
                showCorrectAnswer={isAnswered} // Only show correct answer if question was answered
                onAnswerChange={() => undefined} // No-op in review mode
                onAnswerSubmit={() => undefined} // No-op in review mode
              />



              {/* Additional feedback if available */}
              {result?.feedback && (
                <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg">
                  <h4 className="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">
                    Explanation
                  </h4>
                  <p className="text-sm text-blue-800 dark:text-blue-300">
                    {result.feedback}
                  </p>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Footer Actions */}
      <div className="mt-8 flex items-center justify-center space-x-4">
        {onBackToResults && (
          <Button onClick={onBackToResults}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Results
          </Button>
        )}
        {onExit && (
          <Button variant="outline" onClick={onExit}>
            Exit Review
          </Button>
        )}
      </div>
    </div>
  );
}

