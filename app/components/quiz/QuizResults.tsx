import { cn } from "~/lib/utils";
import { Trophy, Target, Clock, RotateCcw, Eye, X, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import type { Quiz as SchemaQuiz, QuizAttempt as SchemaQuizAttempt } from "~/db/schema/quiz";
import type { Quiz as ComponentQuiz, QuizAttempt as ComponentQuizAttempt, QuizScore, QuestionResult } from "./types";

// Union types to accept both schema and component types
type QuizType = SchemaQuiz | ComponentQuiz;
type QuizAttemptType = SchemaQuizAttempt | ComponentQuizAttempt;

interface QuizResultsProps {
  quiz: QuizType;
  attempt: QuizAttemptType;
  score: QuizScore;
  questionResults: QuestionResult[];
  onRetake?: () => void;
  onReview?: () => void;
  onExit?: () => void;
  isRetaking?: boolean;
  className?: string;
}

export function QuizResults({ quiz, attempt, score, questionResults, onRetake, onReview, onExit, isRetaking = false, className }: QuizResultsProps) {
  // Calculate performance metrics
  const accuracyPercentage = score.percentage;

  // Fix: Calculate average time per answered question, not total questions
  const answeredQuestionsCount = questionResults.length;
  const totalTimeSpent = attempt.totalTimeSpent ?? 0;
  const timePerQuestion = answeredQuestionsCount > 0
    ? totalTimeSpent / answeredQuestionsCount
    : 0;

  // Fix: Improved efficiency calculation with proper edge case handling
  const calculateEfficiency = () => {
    if (!quiz.estimatedDuration || quiz.estimatedDuration <= 0) {
      return 100; // Default efficiency if no estimate
    }

    if (!totalTimeSpent || totalTimeSpent <= 0) {
      return 0; // No time spent = no efficiency
    }

    const estimatedSeconds = quiz.estimatedDuration * 60;
    const actualSeconds = totalTimeSpent;

    // For incomplete quizzes, adjust estimated time proportionally
    const completionRatio = answeredQuestionsCount / quiz.questions.length;
    const adjustedEstimatedSeconds = estimatedSeconds * completionRatio;

    // Calculate efficiency: lower time = higher efficiency
    // Cap at 200% to show exceptional performance
    const rawEfficiency = (adjustedEstimatedSeconds / actualSeconds) * 100;
    return Math.min(200, Math.max(0, rawEfficiency));
  };

  const efficiency = calculateEfficiency();

  // Performance level
  const getPerformanceLevel = (percentage: number) => {
    if (percentage >= 90) return { level: 'Excellent', color: 'text-green-600 dark:text-green-400', bgColor: 'bg-green-100 dark:bg-green-900/30' };
    if (percentage >= 80) return { level: 'Good', color: 'text-blue-600 dark:text-blue-400', bgColor: 'bg-blue-100 dark:bg-blue-900/30' };
    if (percentage >= 70) return { level: 'Fair', color: 'text-yellow-600 dark:text-yellow-400', bgColor: 'bg-yellow-100 dark:bg-yellow-900/30' };
    return { level: 'Needs Improvement', color: 'text-red-600 dark:text-red-400', bgColor: 'bg-red-100 dark:bg-red-900/30' };
  };

  const performance = getPerformanceLevel(accuracyPercentage);

  // Format time
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Group questions by result
  const correctQuestions = questionResults.filter(q => q.isCorrect);
  const incorrectQuestions = questionResults.filter(q => !q.isCorrect);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-4">
          <Trophy className="h-8 w-8 text-blue-600 dark:text-blue-400" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Quiz Complete!</h1>
        <p className="text-gray-600 dark:text-gray-300">Here&apos;s how you performed on &quot;{quiz.title}&quot;</p>
      </div>

      {/* Score Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6">
        <div className="text-center mb-6">
          <div className="text-6xl font-bold text-gray-900 dark:text-white mb-2">
            Score: {score.percentage.toFixed(1)}%
          </div>
          <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300">
            {performance.level}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Accuracy */}
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full mx-auto mb-3">
              <Target className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {score.correctAnswers}/{score.totalQuestions}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Correct Answers</div>
          </div>

          {/* Time */}
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full mx-auto mb-3">
              <Clock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {formatTime(totalTimeSpent)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Total Time</div>
          </div>

          {/* Points */}
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full mx-auto mb-3">
              <Trophy className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {score.earnedPoints}/{score.totalPoints}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Points Earned</div>
          </div>
        </div>
      </div>

      {/* Performance Metrics */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-6">Performance Details</h2>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {/* Correct Answers */}
          <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-700">
            <div className="flex items-center justify-center w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-full mx-auto mb-2">
              <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
            </div>
            <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">
              {correctQuestions.length}
            </div>
            <div className="text-xs text-green-700 dark:text-green-300 font-medium">
              Correct
            </div>
          </div>

          {/* Average Time per Question */}
          <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
            <div className="flex items-center justify-center w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full mx-auto mb-2">
              <Clock className="h-5 w-5 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
              {formatTime(Math.round(timePerQuestion))}
            </div>
            <div className="text-xs text-blue-700 dark:text-blue-300 font-medium">
              Average per question
            </div>
          </div>

          {/* Incorrect Answers */}
          <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-700">
            <div className="flex items-center justify-center w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full mx-auto mb-2">
              <XCircle className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <div className="text-2xl font-bold text-red-600 dark:text-red-400 mb-1">
              {incorrectQuestions.length}
            </div>
            <div className="text-xs text-red-700 dark:text-red-300 font-medium">
              Incorrect
            </div>
          </div>

          {/* Efficiency */}
          <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-700">
            <div className="flex items-center justify-center w-10 h-10 bg-purple-100 dark:bg-purple-900/30 rounded-full mx-auto mb-2">
              <Target className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">
              {Math.round(efficiency)}%
            </div>
            <div className="text-xs text-purple-700 dark:text-purple-300 font-medium">
              Efficiency
            </div>
          </div>
        </div>
              {/* Efficiency Explanation */}
        <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg border border-gray-200 dark:border-gray-600">
          <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-2 flex items-center">
            <Target className="h-4 w-4 text-purple-600 dark:text-purple-400 mr-2" />
            Efficiency Calculation
          </h3>
          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <p>
              <strong>Formula:</strong> (Adjusted Estimated Time / Actual Time Spent) × 100
            </p>
            <p>
              <strong>Your calculation:</strong> {quiz.estimatedDuration ? (
                `(${Math.round((quiz.estimatedDuration * 60) * (answeredQuestionsCount / quiz.questions.length))}s / ${totalTimeSpent}s) × 100 = ${Math.round(efficiency)}%`
              ) : (
                'No estimated duration set - defaulted to 100%'
              )}
            </p>
            <p className="text-xs">
              • 200% = Half the estimated time or better • 100% = Exactly estimated time • 50% = Twice as long as estimated
            </p>
          </div>
        </div>
      </div>

      {/* Question Results Summary */}
      {questionResults.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Question Results</h2>

          <div className="space-y-3 max-h-64 overflow-y-auto">
            {questionResults.map((result, index) => {
              const question = quiz.questions.find(q => q.id === result.questionId);
              if (!question) return null;

              return (
                <div
                  key={result.questionId}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg border",
                    result.isCorrect
                      ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800"
                      : "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
                  )}
                >
                  <div className="flex items-center space-x-3">
                    {result.isCorrect ? (
                      <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500 dark:text-red-400" />
                    )}
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        Question {index + 1}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 capitalize">
                        {question.type.replace(/([A-Z])/g, ' $1').toLowerCase()}
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {result.pointsEarned}/{question.points} pts
                    </div>
                    {result.feedback && (
                      <div className="text-xs text-gray-600 dark:text-gray-400 max-w-xs truncate">
                        {result.feedback}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <button
          onClick={onReview}
          className="flex items-center justify-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
        >
          <Eye className="h-4 w-4" />
          <span>Review Answers</span>
        </button>

        {onRetake && (
          <button
            onClick={onRetake}
            disabled={isRetaking}
            className="flex items-center justify-center space-x-2 px-6 py-3 bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700 disabled:bg-gray-400 dark:disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors"
          >
            {isRetaking ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Preparing Retake...</span>
              </>
            ) : (
              <>
                <RotateCcw className="h-4 w-4" />
                <span>Retake Quiz</span>
              </>
            )}
          </button>
        )}

        <button
          onClick={onExit}
          className="flex items-center justify-center space-x-2 px-6 py-3 bg-gray-600 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
        >
          <X className="h-4 w-4" />
          <span>Exit</span>
        </button>
      </div>

      {/* Motivational message */}
      <div className="text-center">
        {accuracyPercentage >= 90 ? (
          <p className="text-green-600 dark:text-green-400 font-medium">🎉 Outstanding performance! You&apos;ve mastered this topic.</p>
        ) : accuracyPercentage >= 80 ? (
          <p className="text-blue-600 dark:text-blue-400 font-medium">👏 Great job! You have a solid understanding.</p>
        ) : accuracyPercentage >= 70 ? (
          <p className="text-yellow-600 dark:text-yellow-400 font-medium">👍 Good effort! Consider reviewing the material.</p>
        ) : (
          <p className="text-red-600 dark:text-red-400 font-medium">💪 Keep practicing! Review the content and try again.</p>
        )}
      </div>
    </div>
  );
}

