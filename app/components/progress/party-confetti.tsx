import React, { useCallback, useEffect, useRef } from 'react';
import confetti from 'canvas-confetti';

interface PartyConfettiProps {
  isActive: boolean;
  className?: string;
}

export function PartyConfetti({ isActive, className }: PartyConfettiProps) {
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const confettiRef = useRef<any>(null);

  const createConfetti = useCallback(() => {
    if (!canvasRef.current) return;

    // Create confetti instance with custom canvas
    confettiRef.current = confetti.create(canvasRef.current, {
      resize: true,
      useWorker: true,
    });
  }, []);

  const fireConfetti = useCallback(() => {
    if (!confettiRef.current) return;

    // Create emoji shapes for celebration
    const partyEmoji = confetti.shapeFromText({ text: '🎉', scalar: 2 });
    const confettiEmoji = confetti.shapeFromText({ text: '🎊', scalar: 2 });
    const trophyEmoji = confetti.shapeFromText({ text: '🏆', scalar: 2 });
    const crownEmoji = confetti.shapeFromText({ text: '👑', scalar: 2 });
    const starEmoji = confetti.shapeFromText({ text: '⭐', scalar: 2 });

    // Slow fireworks effect - gradual bursts with slower particles
    const duration = 2000; // Longer duration for slow effect
    const animationEnd = Date.now() + duration;
    const defaults = { 
      startVelocity: 15, // Slower initial velocity
      spread: 120, // More focused spread
      ticks: 300, // Longer particle life
      gravity: 0.3, // Reduced gravity for floating effect
      decay: 0.95, // Slower decay
      scalar: 1.2, // Slightly larger particles
      zIndex: 0,
      shapes: [partyEmoji, confettiEmoji, trophyEmoji, crownEmoji, starEmoji]
    };

    function randomInRange(min: number, max: number) {
      return Math.random() * (max - min) + min;
    }

    // Create multiple firework bursts at different times
    const createFireworkBurst = (delay: number, origin: { x: number, y: number }) => {
      setTimeout(() => {
        confettiRef.current({
          ...defaults,
          particleCount: 30,
          origin,
          angle: randomInRange(60, 120),
          colors: ['#FFD700', '#FFA500', '#FF6347', '#FF1493', '#00CED1', '#32CD32', '#9370DB']
        });
      }, delay);
    };

    // Create sequential firework bursts
    createFireworkBurst(0, { x: 0.2, y: 0.6 });
    createFireworkBurst(300, { x: 0.8, y: 0.5 });
    createFireworkBurst(600, { x: 0.5, y: 0.4 });
    createFireworkBurst(900, { x: 0.3, y: 0.7 });
    createFireworkBurst(1200, { x: 0.7, y: 0.6 });

    // Additional continuous smaller bursts
    const interval: NodeJS.Timeout = setInterval(function() {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        return clearInterval(interval);
      }

      const particleCount = 15 * (timeLeft / duration);

      // Create smaller continuous bursts
      confettiRef.current({
        ...defaults,
        particleCount,
        startVelocity: 10,
        origin: { x: randomInRange(0.2, 0.8), y: randomInRange(0.4, 0.8) },
        colors: ['#FFD700', '#FFA500', '#FF6347', '#FF1493', '#00CED1', '#32CD32', '#9370DB']
      });
      confettiRef.current({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 },
        colors: ['#FFD700', '#FFA500', '#FF6347', '#FF1493', '#00CED1']
      });
    }, 250);
  }, []);

  const startContinuousConfetti = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    // Fire confetti immediately
    fireConfetti();

    // Then fire every 3 seconds for continuous effect
    intervalRef.current = setInterval(() => {
      fireConfetti();
    }, 3000);
  }, [fireConfetti]);

  const stopConfetti = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  useEffect(() => {
    createConfetti();
    return () => {
      stopConfetti();
    };
  }, [createConfetti, stopConfetti]);

  useEffect(() => {
    if (isActive) {
      startContinuousConfetti();
    } else {
      stopConfetti();
    }

    return () => {
      stopConfetti();
    };
  }, [isActive, startContinuousConfetti, stopConfetti]);

  if (!isActive) {
    return null;
  }

  return (
    <canvas
      ref={canvasRef}
      className={`pointer-events-none fixed inset-0 z-50 ${className || ''}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        pointerEvents: 'none',
      }}
    />
  );
}