import React, { useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Calendar, Flame, TrendingUp } from "lucide-react";
import { cn } from "~/lib/utils";
import type { ContributionDay } from "~/lib/hooks/use-leaderboard-api";

interface ContributionHeatmapProps {
  contributions: ContributionDay[];
  stats: {
    year: number;
    totalSessions: number;
    activeDays: number;
    longestStreak: number;
    currentStreak: number;
    maxSessions: number;
  };
  className?: string;
}

const MONTHS = [
  'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
];

const DAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export function ContributionHeatmap({ contributions, stats, className }: ContributionHeatmapProps) {
  // Group contributions by weeks for grid layout
  const contributionGrid = useMemo(() => {
    const grid: ContributionDay[][] = [];
    let currentWeek: ContributionDay[] = [];
    
    contributions.forEach((contribution, index) => {
      const date = new Date(contribution.date);
      const dayOfWeek = date.getDay();
      
      // If it's Sunday and we have data, start a new week
      if (dayOfWeek === 0 && currentWeek.length > 0) {
        grid.push(currentWeek);
        currentWeek = [];
      }
      
      currentWeek.push(contribution);
      
      // If it's the last day, push the current week
      if (index === contributions.length - 1) {
        grid.push(currentWeek);
      }
    });
    
    return grid;
  }, [contributions]);

  // Generate month labels for the grid
  const monthLabels = useMemo(() => {
    const labels: Array<{ month: string; weekIndex: number }> = [];
    let currentMonth = -1;
    
    contributionGrid.forEach((week, weekIndex) => {
      if (week.length > 0) {
        const firstDayMonth = new Date(week[0].date).getMonth();
        if (firstDayMonth !== currentMonth) {
          labels.push({
            month: MONTHS[firstDayMonth],
            weekIndex
          });
          currentMonth = firstDayMonth;
        }
      }
    });
    
    return labels;
  }, [contributionGrid]);

  const getContributionColor = (level: number) => {
    switch (level) {
      case 0:
        return 'bg-gray-100 dark:bg-gray-800';
      case 1:
        return 'bg-green-200 dark:bg-green-900';
      case 2:
        return 'bg-green-300 dark:bg-green-700';
      case 3:
        return 'bg-green-400 dark:bg-green-600';
      case 4:
        return 'bg-green-500 dark:bg-green-500';
      default:
        return 'bg-gray-100 dark:bg-gray-800';
    }
  };

  const formatTooltipDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleDateString('en-US', { 
      weekday: 'short', 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Calendar className="h-5 w-5" />
          <span>Learning Activity</span>
          <Badge variant="outline">{stats.year}</Badge>
        </CardTitle>
        <CardDescription>
          Your learning sessions throughout the year, GitHub-style
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Stats Row */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4 mb-8">
          <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
            <div className="text-xl md:text-2xl font-bold text-green-600">{stats.totalSessions}</div>
            <div className="text-xs text-muted-foreground mt-1">Total Sessions</div>
          </div>
          <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="text-xl md:text-2xl font-bold text-blue-600">{stats.activeDays}</div>
            <div className="text-xs text-muted-foreground mt-1">Active Days</div>
          </div>
          <div className="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
            <div className="text-xl md:text-2xl font-bold text-orange-600 flex items-center justify-center gap-1">
              <Flame className="h-4 w-4" />
              {stats.longestStreak}
            </div>
            <div className="text-xs text-muted-foreground mt-1">Longest Streak</div>
          </div>
          <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
            <div className="text-xl md:text-2xl font-bold text-purple-600 flex items-center justify-center gap-1">
              <TrendingUp className="h-4 w-4" />
              {stats.currentStreak}
            </div>
            <div className="text-xs text-muted-foreground mt-1">Current Streak</div>
          </div>
        </div>

        {/* Heatmap Grid */}
        <div className="overflow-x-auto">
          <div className="min-w-full">
            {/* Month Labels */}
            <div className="flex mb-3 md:mb-4">
              <div className="w-8 md:w-10"></div> {/* Spacer for day labels */}
              <div className="flex-1 relative h-4">
                {monthLabels.map((label, index) => (
                  <div
                    key={index}
                    className="absolute text-[10px] md:text-xs text-muted-foreground"
                    style={{
                      left: `${(label.weekIndex * 14)}px`,
                    }}
                  >
                    {label.month}
                  </div>
                ))}
              </div>
            </div>

            {/* Grid */}
            <div className="flex">
              {/* Day Labels */}
              <div className="w-8 md:w-10 pr-2">
                {DAYS.map((day, index) => (
                  index % 2 === 1 && (
                    <div key={day} className="text-[10px] md:text-xs text-muted-foreground h-3.5 leading-3.5 mb-1.5">
                      {day}
                    </div>
                  )
                ))}
              </div>

              {/* Contribution Squares */}
              <div className="flex flex-col">
                {[0, 1, 2, 3, 4, 5, 6].map(dayOfWeek => (
                  <div key={dayOfWeek} className="flex mb-1.5">
                    {contributionGrid.map((week, weekIndex) => {
                      const contribution = week.find(c => new Date(c.date).getDay() === dayOfWeek);
                      if (!contribution) {
                        return (
                          <div
                            key={`${weekIndex}-${dayOfWeek}-empty`}
                            className="w-3.5 h-3.5 mr-1.5 bg-gray-100 dark:bg-gray-800 rounded-[3px]"
                          />
                        );
                      }

                      return (
                        <div
                          key={`${weekIndex}-${dayOfWeek}`}
                          className={cn(
                            "w-3.5 h-3.5 mr-1.5 rounded-[3px] cursor-pointer hover:ring-2 hover:ring-blue-400 transition-all",
                            getContributionColor(contribution.level)
                          )}
                          title={`${formatTooltipDate(contribution.date)}: ${contribution.sessionCount} session${contribution.sessionCount !== 1 ? 's' : ''}`}
                        />
                      );
                    })}
                  </div>
                ))}
              </div>
            </div>

            {/* Legend */}
            <div className="flex items-center justify-between mt-5 text-[10px] md:text-xs text-muted-foreground">
              <span className="mr-2">Less</span>
              <div className="flex space-x-1.5">
                {[0, 1, 2, 3, 4].map(level => (
                  <div
                    key={level}
                    className={cn(
                      "w-3.5 h-3.5 rounded-[3px]",
                      getContributionColor(level)
                    )}
                  />
                ))}
              </div>
              <span className="ml-2">More</span>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-4 p-4 bg-muted/50 rounded-lg">
          <p className="text-sm text-muted-foreground">
            Each square represents a day. Darker squares indicate more learning sessions.
            {stats.currentStreak > 0 && (
              <span className="text-green-600 font-medium">
                {' '}Keep up your {stats.currentStreak}-day streak!
              </span>
            )}
          </p>
        </div>
      </CardContent>
    </Card>
  );
}