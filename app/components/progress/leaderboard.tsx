import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "~/components/ui/avatar";
import { Badge } from "~/components/ui/badge";
import { Trophy, Medal, Award, Crown, Users, Loader2, TrendingUp, Target } from "lucide-react";
import { cn } from "~/lib/utils";
import type { LeaderboardUser } from "~/lib/hooks/use-leaderboard-api";
import { PointBreakdown } from "~/components/dashboard/PointBreakdown";
import { PartyConfetti } from "~/components/progress/party-confetti";

interface LeaderboardProps {
  users: LeaderboardUser[];
  currentUserRank: number;
  currentUserData?: LeaderboardUser | null;
  totalUsers: number;
  isLoading?: boolean;
  hasNextPage?: boolean;
  isFetchingNextPage?: boolean;
  onLoadMore?: () => void;
  className?: string;
}

export function Leaderboard({ 
  users, 
  currentUserRank, 
  currentUserData,
  totalUsers,
  isLoading, 
  hasNextPage, 
  isFetchingNextPage, 
  onLoadMore,
  className 
}: LeaderboardProps) {
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="h-4 w-4 text-yellow-500" />;
      case 2:
        return <Trophy className="h-4 w-4 text-gray-400" />;
      case 3:
        return <Medal className="h-4 w-4 text-amber-600" />;
      default:
        return null;
    }
  };

  const getRankStyles = (rank: number) => {
    switch (rank) {
      case 1:
        return {
          container: "bg-gradient-to-br from-yellow-400 to-yellow-600 text-white shadow-lg",
          text: "text-white font-bold"
        };
      case 2:
        return {
          container: "bg-gradient-to-br from-gray-300 to-gray-500 text-white shadow-md",
          text: "text-white font-bold"
        };
      case 3:
        return {
          container: "bg-gradient-to-br from-amber-400 to-amber-600 text-white shadow-md",
          text: "text-white font-bold"
        };
      default:
        return {
          container: "bg-muted text-muted-foreground",
          text: "text-muted-foreground font-medium"
        };
    }
  };

  const getRankBadgeVariant = (rank: number) => {
    switch (rank) {
      case 1:
        return "default";
      case 2:
        return "secondary";
      case 3:
        return "outline";
      default:
        return "outline";
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Check if current user is in the displayed results
  const currentUserInResults = users.some(user => user.isCurrentUser);
  
  // Show current user separately if they're not in top results
  const shouldShowCurrentUserSeparately = currentUserData && !currentUserInResults && currentUserRank > users.length;

  if (isLoading && users.length === 0) {
    return (
      <Card className={cn("", className)}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Leaderboard</span>
          </CardTitle>
          <CardDescription>
            See how you rank among all learners
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Trophy className="h-5 w-5" />
          <span>Leaderboard</span>
          {currentUserRank > 0 && totalUsers > 0 && (
            <Badge variant="outline">#{currentUserRank} of {totalUsers}</Badge>
          )}
        </CardTitle>
        <CardDescription>
          See how you rank among all learners
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Top Stats - Simplified */}
        {currentUserData && (
          <div className="space-y-4 mb-6">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="text-center p-4 bg-muted/50 rounded-lg relative overflow-hidden">
                <div className="text-lg font-bold mb-1 flex items-center justify-center gap-1">
                  #{currentUserRank}
                  {currentUserRank === 1 && (
                    <Crown className="h-4 w-4 text-yellow-500 animate-pulse" />
                  )}
                </div>
                <div className="text-xs text-muted-foreground">
                  {totalUsers > 0 ? `Your rank among ${totalUsers} users` : 'Your Rank'}
                </div>
                {/* Party Confetti Animation for Rank #1 */}
                <PartyConfetti isActive={currentUserRank === 1} />
              </div>
              
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-lg font-bold mb-1">{currentUserData.totalPoints.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">Your Points</div>
              </div>
              
              <div className="text-center p-4 bg-muted/50 rounded-lg">
                <div className="text-lg font-bold mb-1">{currentUserData.completedContent}</div>
                <div className="text-xs text-muted-foreground">Content Done</div>
              </div>
            </div>
            
            {/* Point Breakdown Button */}
            <div className="flex justify-center">
              <PointBreakdown
                totalPoints={currentUserData.totalPoints}
                quizPoints={currentUserData.quizPoints || 0}
                progressPoints={currentUserData.progressPoints || 0}
                analyticsPoints={currentUserData.analyticsPoints || 0}
                contentCount={currentUserData.completedContent || 0}
                quizCount={currentUserData.completedQuizzes || 0}
                sessionCount={currentUserData.totalSessions || 0}
              />
            </div>
          </div>
        )}
        
        <div className="space-y-3">
          {/* Scrollable leaderboard list */}
          <div className="max-h-96 overflow-y-auto pr-2 space-y-3">
          {users.map((user, index) => (
            <div
              key={user.userId}
              className={cn(
                "flex items-center justify-between p-4 rounded-lg transition-colors",
                user.isCurrentUser 
                  ? "bg-primary/5 border border-primary/20" 
                  : "bg-muted/30 hover:bg-muted/50"
              )}
            >
              <div className="flex items-center space-x-4">
                {/* Rank */}
                <div className="flex items-center space-x-3 min-w-[80px]">
                  <div className={cn(
                    "w-12 h-12 rounded-full flex items-center justify-center text-sm relative",
                    getRankStyles(user.rank).container
                  )}>
                    {user.rank <= 3 ? (
                      <div className="flex flex-col items-center">
                        {getRankIcon(user.rank)}
                        <span className="text-xs font-bold mt-0.5">#{user.rank}</span>
                      </div>
                    ) : (
                      <span className={cn("text-sm", getRankStyles(user.rank).text)}>#{user.rank}</span>
                    )}
                  </div>
                </div>

                {/* User Info */}
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={user.userAvatar || undefined} alt={user.userName} />
                    <AvatarFallback>{getInitials(user.userName)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">
                      {user.userName}
                      {user.isCurrentUser && (
                        <Badge variant="outline" className="ml-2 text-xs">
                          You
                        </Badge>
                      )}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {user.completedContent} content • {user.completedQuizzes} quizzes • {user.totalSessions} sessions
                    </div>
                  </div>
                </div>
              </div>

              {/* Points */}
              <div className="text-right">
                <div className="text-xl font-bold">
                  {user.totalPoints.toLocaleString()}
                </div>
                <div className="text-xs text-muted-foreground">
                  points
                </div>
              </div>
            </div>
          ))}

          {/* Empty State */}
          {!isLoading && users.length === 0 && (
            <div className="text-center py-8">
              <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground">No leaderboard data available yet.</p>
              <p className="text-sm text-muted-foreground mt-2">
                Complete some learning content to see your ranking!
              </p>
            </div>
          )}
          </div>

        {/* Fixed bottom section - Current User Position */}
        {shouldShowCurrentUserSeparately && (
          <>
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-dashed" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">Your Position</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between p-4 rounded-lg bg-primary/5 border border-primary/20">
              <div className="flex items-center space-x-4">
                {/* Rank */}
                <div className="flex items-center space-x-3 min-w-[80px]">
                  <div className={cn(
                    "w-12 h-12 rounded-full flex items-center justify-center text-sm relative",
                    getRankStyles(currentUserData.rank).container
                  )}>
                    {currentUserData.rank <= 3 ? (
                      <div className="flex flex-col items-center">
                        {getRankIcon(currentUserData.rank)}
                        <span className="text-xs font-bold mt-0.5">#{currentUserData.rank}</span>
                      </div>
                    ) : (
                      <span className={cn("text-sm", getRankStyles(currentUserData.rank).text)}>#{currentUserData.rank}</span>
                    )}
                  </div>
                </div>

                {/* User Info */}
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={currentUserData.userAvatar || undefined} alt={currentUserData.userName} />
                    <AvatarFallback>{getInitials(currentUserData.userName)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">
                      {currentUserData.userName}
                      <Badge variant="outline" className="ml-2 text-xs">
                        You
                      </Badge>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {currentUserData.completedContent} content • {currentUserData.completedQuizzes} quizzes • {currentUserData.totalSessions} sessions
                    </div>
                  </div>
                </div>
              </div>

              {/* Points */}
              <div className="text-right">
                <div className="text-xl font-bold">
                  {currentUserData.totalPoints.toLocaleString()}
                </div>
                <div className="text-xs text-muted-foreground">
                  points
                </div>
              </div>
            </div>
          </>
        )}

          {/* Fixed bottom section - Load More Button */}
          {hasNextPage && (
            <div className="flex justify-center pt-4">
              <Button
                variant="outline"
                onClick={onLoadMore}
                disabled={isFetchingNextPage}
                className="w-full"
              >
                {isFetchingNextPage ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Loading...
                  </>
                ) : (
                  'Load More'
                )}
              </Button>
            </div>
          )}
        </div>

        {/* Points System Info */}
        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <h4 className="text-sm font-medium mb-3 flex items-center">
            <Award className="h-4 w-4 mr-2" />
            How Points Work
          </h4>
          <div className="text-xs text-muted-foreground space-y-2">
            <div className="flex items-center">
              <Target className="h-3 w-3 mr-2" />
              Complete learning content: dynamic averages based on learning level and content reading time
            </div>
            <div className="flex items-center">
              <Target className="h-3 w-3 mr-2" />
              Quiz scores: 20-100 points based on quiz percentage result
            </div>
            <div className="flex items-center">
              <Target className="h-3 w-3 mr-2" />
              Learning sessions: 10 points per session
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}