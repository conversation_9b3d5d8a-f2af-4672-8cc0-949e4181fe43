import React, { useMemo, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Calendar, Flame, TrendingUp, BookOpen, Target, ChevronLeft, ChevronRight } from "lucide-react";
import { cn } from "~/lib/utils";
import type { ContributionDay } from "~/lib/hooks/use-leaderboard-api";

interface LearningActivityCalendarProps {
  contributions: ContributionDay[];
  stats: {
    year: number;
    totalSessions: number;
    activeDays: number;
    longestStreak: number;
    currentStreak: number;
    maxSessions: number;
  };
  className?: string;
}

const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

const MONTH_ABBR = [
  'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
];

const DAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

export function LearningActivityCalendar({ contributions, stats, className }: LearningActivityCalendarProps) {
  // State for current month navigation
  const [currentMonth, setCurrentMonth] = useState(new Date().getMonth());
  const [currentYear, setCurrentYear] = useState(stats.year);

  // Create a map for quick lookup of contributions by date
  const contributionMap = useMemo(() => {
    const map = new Map<string, ContributionDay>();
    contributions.forEach(contribution => {
      map.set(contribution.date, contribution);
    });
    return map;
  }, [contributions]);

  // Generate calendar data for the current month only
  const monthData = useMemo(() => {
    const firstDay = new Date(currentYear, currentMonth, 1);
    const lastDay = new Date(currentYear, currentMonth + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay()); // Start from Sunday
    
    const weeks = [];
    let currentWeek = [];
    
    for (let d = new Date(startDate); d <= lastDay || currentWeek.length > 0; d.setDate(d.getDate() + 1)) {
      // Create date string in local timezone format (YYYY-MM-DD) to match server response
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const dateStr = `${year}-${month}-${day}`;
      
      const contribution = contributionMap.get(dateStr);
      const isCurrentMonth = d.getMonth() === currentMonth;
      
      currentWeek.push({
        date: new Date(d),
        dateStr,
        contribution,
        isCurrentMonth,
        sessionCount: contribution?.sessionCount || 0,
        level: contribution?.level || 0
      });
      
      if (currentWeek.length === 7) {
        weeks.push(currentWeek);
        currentWeek = [];
      }
      
      // Break if we've filled the month and completed the week
      if (d > lastDay && currentWeek.length === 0) break;
    }
    
    return {
      name: MONTHS[currentMonth],
      abbr: MONTH_ABBR[currentMonth],
      weeks
    };
  }, [contributionMap, currentMonth, currentYear]);

  // Navigation functions
  const goToPreviousMonth = () => {
    if (currentMonth === 0) {
      setCurrentMonth(11);
      setCurrentYear(currentYear - 1);
    } else {
      setCurrentMonth(currentMonth - 1);
    }
  };

  const goToNextMonth = () => {
    if (currentMonth === 11) {
      setCurrentMonth(0);
      setCurrentYear(currentYear + 1);
    } else {
      setCurrentMonth(currentMonth + 1);
    }
  };

  const goToCurrentMonth = () => {
    const now = new Date();
    setCurrentMonth(now.getMonth());
    setCurrentYear(now.getFullYear());
  };

  const getLearningIntensityColor = (level: number, isCurrentMonth: boolean) => {
    if (!isCurrentMonth) {
      return 'bg-gray-50 dark:bg-gray-900 text-gray-300';
    }
    
    switch (level) {
      case 0:
        return 'bg-gray-100 dark:bg-gray-800 text-gray-500 hover:bg-gray-200 dark:hover:bg-gray-700';
      case 1:
        return 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 hover:bg-blue-200 dark:hover:bg-blue-800/50';
      case 2:
        return 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-800/50';
      case 3:
        return 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 hover:bg-orange-200 dark:hover:bg-orange-800/50';
      case 4:
        return 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 hover:bg-purple-200 dark:hover:bg-purple-800/50';
      default:
        return 'bg-gray-100 dark:bg-gray-800 text-gray-500';
    }
  };

  const formatTooltipDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'long', 
      month: 'long', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  const getIntensityLabel = (level: number) => {
    switch (level) {
      case 0: return 'No activity';
      case 1: return 'Light learning';
      case 2: return 'Moderate learning';
      case 3: return 'Active learning';
      case 4: return 'Intensive learning';
      default: return 'No activity';
    }
  };

  return (
    <Card className={cn("", className)}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <BookOpen className="h-5 w-5" />
          <span>Learning Activity</span>
          <Badge variant="outline">{stats.year}</Badge>
        </CardTitle>
        <CardDescription>
          Your learning journey throughout the year in calendar view
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Progress Rings - Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-xl border border-blue-200 dark:border-blue-800">
            <div className="relative w-16 h-16 mx-auto mb-2">
              <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
                <circle cx="32" cy="32" r="28" fill="none" stroke="currentColor" strokeWidth="4" className="text-blue-200 dark:text-blue-800" />
                <circle 
                  cx="32" 
                  cy="32" 
                  r="28" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="4" 
                  strokeLinecap="round"
                  className="text-blue-600 dark:text-blue-400"
                  strokeDasharray={`${(stats.totalSessions / Math.max(stats.totalSessions, 100)) * 175.93} 175.93`}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-lg font-bold text-blue-600">{stats.totalSessions}</span>
              </div>
            </div>
            <div className="text-xs text-muted-foreground">Total Sessions</div>
          </div>
          
          <div className="text-center p-4 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-xl border border-green-200 dark:border-green-800">
            <div className="relative w-16 h-16 mx-auto mb-2">
              <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
                <circle cx="32" cy="32" r="28" fill="none" stroke="currentColor" strokeWidth="4" className="text-green-200 dark:text-green-800" />
                <circle 
                  cx="32" 
                  cy="32" 
                  r="28" 
                  fill="none" 
                  stroke="currentColor" 
                  strokeWidth="4" 
                  strokeLinecap="round"
                  className="text-green-600 dark:text-green-400"
                  strokeDasharray={`${(stats.activeDays / 365) * 175.93} 175.93`}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-lg font-bold text-green-600">{stats.activeDays}</span>
              </div>
            </div>
            <div className="text-xs text-muted-foreground">Active Days</div>
          </div>
          
          <div className="text-center p-4 bg-gradient-to-br from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20 rounded-xl border border-orange-200 dark:border-orange-800">
            <div className="flex items-center justify-center mb-2">
              <Flame className="h-8 w-8 text-orange-600" />
            </div>
            <div className="text-2xl font-bold text-orange-600 mb-1">{stats.longestStreak}</div>
            <div className="text-xs text-muted-foreground">Longest Streak</div>
          </div>
          
          <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-xl border border-purple-200 dark:border-purple-800">
            <div className="flex items-center justify-center mb-2">
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
            <div className="text-2xl font-bold text-purple-600 mb-1">{stats.currentStreak}</div>
            <div className="text-xs text-muted-foreground">Current Streak</div>
          </div>
        </div>

        {/* Month Navigation */}
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={goToPreviousMonth}
            className="flex items-center space-x-1"
          >
            <ChevronLeft className="h-4 w-4" />
            <span>Previous</span>
          </Button>
          
          <div className="flex items-center space-x-4">
            <h3 className="text-xl font-semibold">{monthData.name} {currentYear}</h3>
            {(currentMonth !== new Date().getMonth() || currentYear !== new Date().getFullYear()) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={goToCurrentMonth}
                className="text-blue-600 hover:text-blue-700"
              >
                Today
              </Button>
            )}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={goToNextMonth}
            className="flex items-center space-x-1"
          >
            <span>Next</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Calendar Grid */}
        <div className="">
          {/* Day headers */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {DAYS.map(day => (
              <div key={day} className="text-center text-xs font-medium text-muted-foreground p-2">
                {day}
              </div>
            ))}
          </div>
          
          {/* Calendar days */}
          <div className="space-y-1">
            {monthData.weeks.map((week, weekIndex) => (
              <div key={weekIndex} className="grid grid-cols-7 gap-1">
                {week.map((day, dayIndex) => {
                   const isToday = day.date.toDateString() === new Date().toDateString();
                   return (
                     <div
                       key={dayIndex}
                       className={cn(
                         "aspect-square flex items-center justify-center text-sm rounded-lg transition-all cursor-pointer border relative",
                         getLearningIntensityColor(day.level, day.isCurrentMonth),
                         day.isCurrentMonth ? "border-transparent" : "border-gray-200 dark:border-gray-700",
                         isToday && "ring-2 ring-blue-500 ring-offset-2 font-bold"
                       )}
                       title={`${formatTooltipDate(day.date)}: ${day.sessionCount} session${day.sessionCount !== 1 ? 's' : ''} - ${getIntensityLabel(day.level)}${isToday ? ' (Today)' : ''}`}
                     >
                       {day.date.getDate()}
                       {isToday && (
                         <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full"></div>
                       )}
                     </div>
                   );
                 })}
              </div>
            ))}
          </div>
        </div>

        {/* Learning Intensity Legend */}
        <div className="mt-8 p-4 bg-muted/50 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <span className="text-sm font-medium">Learning Intensity</span>
          </div>
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>Less</span>
            <div className="flex space-x-1">
              {[0, 1, 2, 3, 4].map(level => (
                <div
                  key={level}
                  className={cn(
                    "w-4 h-4 rounded border",
                    getLearningIntensityColor(level, true)
                  )}
                  title={getIntensityLabel(level)}
                />
              ))}
            </div>
            <span>More</span>
          </div>
          
          {/* Motivational message */}
          <div className="mt-3 text-center">
            <p className="text-sm text-muted-foreground">
              {stats.currentStreak > 0 ? (
                <span className="text-green-600 font-medium">
                  🔥 Amazing! You're on a {stats.currentStreak}-day learning streak!
                </span>
              ) : (
                <span>
                  Start your learning journey today and build a consistent habit!
                </span>
              )}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}