import React from "react";

export interface StickyFooterItem {
  key: string;
  content: React.ReactNode;
}

export interface StickyFooterProps {
  className?: string;
  offsetClassName?: string; // e.g., "md:left-64" for sidebar offset
  items?: StickyFooterItem[];
  children?: React.ReactNode; // alternative to items
  contentPaddingClassName?: string; // default padding container
  innerClassName?: string; // default flex container for items
}

export function StickyFooter({
  className = "",
  offsetClassName = "md:left-64",
  items,
  children,
  contentPaddingClassName = "px-4 py-2",
  innerClassName = "flex items-center justify-center gap-6 text-xs text-gray-500 dark:text-gray-400 flex-wrap",
}: StickyFooterProps) {
  return (
    <div
      className={[
        "fixed bottom-0 left-0 right-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm border-t border-gray-200 dark:border-gray-700 z-10",
        offsetClassName,
        className,
      ].join(" ")}
    >
      <div className={contentPaddingClassName}>
        <div className={innerClassName}>
          {items
            ? items.map((item) => (
                <div key={item.key}>{item.content}</div>
              ))
            : children}
        </div>
      </div>
    </div>
  );
}

export default StickyFooter;

