import React from "react";
import { Label } from "~/components/ui/label";

export interface FilterFieldProps {
  title: string;
  icon?: React.ComponentType<{ className?: string }> | React.ReactNode;
  children: React.ReactNode;
  className?: string;
  // Constrain control width; SelectTrigger uses w-full, so container constrains it.
  maxWidthClassName?: string; // e.g., "max-w-xs". Default below.
  contentClassName?: string; // extra classes for content wrapper (e.g., px-2 for sliders)
}

export function FilterField({
  title,
  icon,
  children,
  className = "",
  maxWidthClassName = "max-w-xs", // keep selects from becoming too wide
  contentClassName = "",
}: FilterFieldProps) {
  const IconComp = icon as React.ComponentType<{ className?: string }> | undefined;

  return (
    <div className={["space-y-2", className].filter(Boolean).join(" ")}
    >
      <Label className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
        {typeof icon === "function" && IconComp ? (
          <IconComp className="h-4 w-4" />
        ) : (
          React.isValidElement(icon) ? icon : null
        )}
        {title}
      </Label>
      <div className={["w-full", maxWidthClassName, contentClassName].filter(Boolean).join(" ")}
      >
        {children}
      </div>
    </div>
  );
}

export default FilterField;

