import React from "react";
import { Button } from "~/components/ui/button";
import { Grid, List } from "lucide-react";

export type ViewMode = "grid" | "list";

export interface ViewControlsProps {
  className?: string;
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  showLabels?: boolean; // show "Grid"/"List" labels (hidden on xs like quizzes)
}

export function ViewControls({
  className = "",
  viewMode,
  onViewModeChange,
  showLabels = true,
}: ViewControlsProps) {
  return (
    <div className={["flex items-center gap-2", className].filter(Boolean).join(" ")}
    >
      <Button
        variant={viewMode === 'grid' ? 'default' : 'outline'}
        size="sm"
        onClick={() => onViewModeChange('grid')}
      >
        <Grid className="h-4 w-4" />
        {showLabels && <span className="hidden sm:inline ml-2">Grid</span>}
      </Button>
      <Button
        variant={viewMode === 'list' ? 'default' : 'outline'}
        size="sm"
        onClick={() => onViewModeChange('list')}
      >
        <List className="h-4 w-4" />
        {showLabels && <span className="hidden sm:inline ml-2">List</span>}
      </Button>
    </div>
  );
}

export default ViewControls;

