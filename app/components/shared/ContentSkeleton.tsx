import React from "react";
import { Skeleton } from "~/components/ui/skeleton";
import { Card, CardContent, CardHeader } from "~/components/ui/card";

export type ContentSkeletonVariant = "card" | "list" | "compact";

export interface ContentSkeletonProps {
  className?: string;
  itemClassName?: string;
  count?: number;
  variant?: ContentSkeletonVariant;
  viewMode?: "grid" | "list";
  gridClasses?: string; // container classes when in grid mode
}

function CardItem({ itemClassName }: { itemClassName?: string }) {
  return (
    <Card className={["animate-pulse", itemClassName].filter(Boolean).join(" ")}>
      <CardHeader>
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-3 w-1/2" />
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-5/6" />
        </div>
      </CardContent>
    </Card>
  );
}

function ListItem({ itemClassName }: { itemClassName?: string }) {
  return (
    <div className={[
      "w-full border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-900 animate-pulse",
      itemClassName,
    ].filter(Boolean).join(" ")}
    >
      <div className="flex items-start gap-4">
        <Skeleton className="h-12 w-12 rounded-md" />
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-3 w-5/6" />
        </div>
      </div>
    </div>
  );
}

function CompactItem({ itemClassName }: { itemClassName?: string }) {
  return (
    <div className={[
      "w-full border border-gray-200 dark:border-gray-700 rounded-lg p-3 bg-white dark:bg-gray-900 animate-pulse",
      itemClassName,
    ].filter(Boolean).join(" ")}
    >
      <div className="flex items-center gap-3">
        <Skeleton className="h-6 w-6 rounded" />
        <div className="flex-1">
          <Skeleton className="h-3 w-3/5" />
        </div>
      </div>
    </div>
  );
}

export function ContentSkeleton({
  className = "",
  itemClassName,
  count = 6,
  variant = "card",
  viewMode = "grid",
  gridClasses = "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4",
}: ContentSkeletonProps) {
  const Container = ({ children }: { children: React.ReactNode }) => (
    <div className={[
      viewMode === "grid" ? gridClasses : "space-y-4",
      className,
    ].filter(Boolean).join(" ")}
    >
      {children}
    </div>
  );

  const Item = () => {
    switch (variant) {
      case "list":
        return <ListItem itemClassName={itemClassName} />;
      case "compact":
        return <CompactItem itemClassName={itemClassName} />;
      case "card":
      default:
        return <CardItem itemClassName={itemClassName} />;
    }
  };

  return (
    <Container>
      {Array.from({ length: count }).map((_, i) => (
        <Item key={i} />
      ))}
    </Container>
  );
}

export default ContentSkeleton;

