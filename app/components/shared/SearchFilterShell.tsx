import React, { useState, useMemo } from "react";
import { Search, Filter, ChevronDown } from "lucide-react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Badge } from "~/components/ui/badge";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "~/components/ui/collapsible";

export interface SearchFilterShellProps<TFilters> {
  className?: string;
  filters: TFilters;
  // Controlled search value so parents can debounce as needed
  searchValue: string;
  onSearchChange: (value: string) => void;
  searchPlaceholder?: string;
  // Advanced section
  isExpanded?: boolean;
  onExpandedChange?: (open: boolean) => void;
  // Updater for individual filter keys
  onSetFilter?: <K extends keyof TFilters>(key: K, value: TFilters[K]) => void;
  // Render route-specific advanced controls
  renderAdvanced: (ctx: {
    filters: TFilters;
    setFilter: <K extends keyof TFilters>(key: K, value: TFilters[K]) => void;
  }) => React.ReactNode;
  // Optional active badges area (below the collapsible)
  renderActiveBadges?: (ctx: {
    filters: TFilters;
    setFilter: <K extends keyof TFilters>(key: K, value: TFilters[K]) => void;
  }) => React.ReactNode;
  // Shows a count badge on the Advanced Filters button
  getActiveCount?: (filters: TFilters) => number;
}

export function SearchFilterShell<TFilters extends Record<string, any>>({
  className = "",
  filters,
  searchValue,
  onSearchChange,
  searchPlaceholder = "Search...",
  isExpanded,
  onExpandedChange,
  renderAdvanced,
  renderActiveBadges,
  getActiveCount,
  onSetFilter,
}: SearchFilterShellProps<TFilters>) {
  const [internalOpen, setInternalOpen] = useState(false);
  const open = isExpanded ?? internalOpen;

  const handleOpenChange = (next: boolean) => {
    if (onExpandedChange) onExpandedChange(next);
    else setInternalOpen(next);
  };

  const activeCount = useMemo(() => (getActiveCount ? getActiveCount(filters) : 0), [filters, getActiveCount]);

  const setFilter = <K extends keyof TFilters>(key: K, value: TFilters[K]) => {
    if (onSetFilter) onSetFilter(key, value);
  };

  return (
    <div className={`bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg ${className}`}>
      {/* Search Bar */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder={searchPlaceholder}
            value={searchValue}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 pr-4"
          />
        </div>
      </div>

      {/* Filter Toggle + Advanced */}
      <Collapsible open={open} onOpenChange={handleOpenChange}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-4 h-auto font-normal"
          >
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              <span>Advanced Filters</span>
              {activeCount > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {activeCount}
                </Badge>
              )}
            </div>
            <ChevronDown className={`h-4 w-4 transition-transform ${open ? 'rotate-180' : ''}`} />
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent className="border-t border-gray-200 dark:border-gray-700">
          <div className="p-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {renderAdvanced({ filters, setFilter })}
          </div>
        </CollapsibleContent>
      </Collapsible>

      {/* Active Badges (optional) */}
      {renderActiveBadges && activeCount > 0 && (
        <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
          <div className="p-4">
            {renderActiveBadges({ filters, setFilter })}
          </div>
        </div>
      )}
    </div>
  );
}

export default SearchFilterShell;

