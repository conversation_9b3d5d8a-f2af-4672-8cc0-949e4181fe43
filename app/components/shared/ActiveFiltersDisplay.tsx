import React from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { X } from "lucide-react";

export interface ActiveBadge {
  key: string;
  label: React.ReactNode;
  onRemove?: () => void;
  className?: string; // allow color variants like quizzes
}

export interface ActiveFiltersDisplayProps {
  className?: string;
  prefixLabel?: React.ReactNode; // e.g., "Active filters:"
  badges: ActiveBadge[];
  onClearAll?: () => void;
}

export function ActiveFiltersDisplay({
  className = "",
  prefixLabel = <span className="text-sm text-gray-600 dark:text-gray-400">Active filters:</span>,
  badges,
  onClearAll,
}: ActiveFiltersDisplayProps) {
  if (!badges || badges.length === 0) return null;

  return (
    <div className={[
      "flex flex-wrap items-center gap-2",
      className,
    ].filter(Boolean).join(" ")}
    >
      {prefixLabel}

      {badges.map(b => (
        <Badge key={b.key} variant="secondary" className={["flex items-center gap-1", b.className].filter(Boolean).join(" ")}
        >
          {b.label}
          {b.onRemove && (
            <button
              onClick={b.onRemove}
              className="ml-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-full p-0.5"
            >
              <X className="h-3 w-3" />
            </button>
          )}
        </Badge>
      ))}

      {onClearAll && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearAll}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          Clear all
        </Button>
      )}
    </div>
  );
}

export default ActiveFiltersDisplay;

