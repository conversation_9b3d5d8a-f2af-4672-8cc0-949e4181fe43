'use client';

import React, { useState, useEffect, useRef } from 'react';
import { ChevronRight, ChevronLeft, Check } from 'lucide-react';
import { Button } from '~/components/ui/button';
import { cn } from '~/lib/utils';
import confetti from 'canvas-confetti';
import {
  MultiStepExplainProps,
  StepConfig,
  InfoBoxData,
  GridData,
  ComparisonData,
  TableData,
  ScatterPlotConfig,
  KeyValueData
} from './types';

export function MultiStepExplain({
  steps,
  className = '',
  initialStep = 0,
  onStepChange,
  completedSteps: externalCompletedSteps = [],
  isLoading = false,
}: MultiStepExplainProps) {
  // Trophy animations are now handled in LearningContentDisplay component
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [completedSteps, setCompletedSteps] = useState<Set<number>>(new Set());
  const previousCompletedStepsRef = useRef<Set<number>>(new Set());

  // Sync internal state with initialStep prop changes
  useEffect(() => {
    setCurrentStep(initialStep);
  }, [initialStep]);

  // Sync with external completed steps (trophy animations handled in parent)
  useEffect(() => {
    const previousCompletedSteps = previousCompletedStepsRef.current;

    // Check if there are any changes before updating state
    const hasChanges = externalCompletedSteps.length !== previousCompletedSteps.size ||
      externalCompletedSteps.some(stepIndex => !previousCompletedSteps.has(stepIndex));

    if (!hasChanges) {
      return; // No changes, avoid unnecessary state updates
    }

    // Update the ref and state only when there are actual changes
    // Trophy animations are now handled in LearningContentDisplay after successful progress save
    const newCompletedSteps = new Set(externalCompletedSteps);
    previousCompletedStepsRef.current = newCompletedSteps;
    setCompletedSteps(newCompletedSteps);
  }, [externalCompletedSteps]);

  // Trigger confetti animation
  const triggerConfetti = () => {
    const count = 200;
    const defaults = {
      origin: { y: 0.7 }
    };

    function fire(particleRatio: number, opts: any) {
      confetti({
        ...defaults,
        ...opts,
        particleCount: Math.floor(count * particleRatio)
      });
    }

    fire(0.25, {
      spread: 26,
      startVelocity: 55,
    });

    fire(0.2, {
      spread: 60,
    });

    fire(0.35, {
      spread: 100,
      decay: 0.91,
      scalar: 0.8
    });

    fire(0.1, {
      spread: 120,
      startVelocity: 25,
      decay: 0.92,
      scalar: 1.2
    });

    fire(0.1, {
      spread: 120,
      startVelocity: 45,
    });
  };

  const nextStep = () => {
    // Mark current step as completed (trophy animation handled in parent after successful save)
    if (!completedSteps.has(currentStep)) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
    }

    if (currentStep < steps.length - 1) {
      // Move to next step
      const newStep = currentStep + 1;
      setCurrentStep(newStep);
      if (onStepChange) {
        onStepChange(newStep);
      }
    } else {
      // On final step - trigger confetti and ensure progress is saved
      triggerConfetti();
      
      // Mark all steps as completed when completing the lesson
      const allSteps = Array.from({ length: steps.length }, (_, i) => i);
      const updatedCompletedSteps = new Set([...completedSteps, ...allSteps]);
      setCompletedSteps(updatedCompletedSteps);
      
      if (onStepChange) {
        // Call onStepChange with lesson completion flag to ensure all steps are marked as completed
        onStepChange(currentStep, true);
      }
    }
  };

  const restartLesson = () => {
    // Navigate back to first step
    setCurrentStep(0);
    if (onStepChange) {
      onStepChange(0);
    }
  };

  const prevStep = () => {
    const newStep = (currentStep - 1 + steps.length) % steps.length;
    setCurrentStep(newStep);
    if (onStepChange) {
      onStepChange(newStep);
    }
  };

  const goToStep = (step: number) => {
    setCurrentStep(step);
    if (onStepChange) {
      onStepChange(step);
    }
  };

  // Check if lesson is complete (all steps are completed)
  const isLessonComplete = completedSteps.size === steps.length;

  // Check if we should show restart button (lesson complete AND on final step)
  const shouldShowRestart = isLessonComplete && currentStep === steps.length - 1;

  // Content renderers
  const renderParagraph = (data: string | string[]) => {
    const paragraphs = Array.isArray(data) ? data : [data];
    return (
      <div className="space-y-4">
        {paragraphs.map((paragraph, index) => (
          <p key={index} className="text-lg leading-relaxed text-gray-900 dark:text-gray-100">
            {paragraph}
          </p>
        ))}
      </div>
    );
  };

  const renderInfoBox = (data: InfoBoxData) => {
    return (
      <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
        {data.heading && (
          <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-3">{data.heading}</h4>
        )}
        <div className="space-y-2">
          {data.lines.map((line, index) => (
            <p key={index} className="text-blue-700 dark:text-blue-300">
              {line}
            </p>
          ))}
        </div>
      </div>
    );
  };

  const renderBulletList = (data: string[]) => {
    return (
      <ul className="space-y-2">
        {data.map((item, index) => (
          <li key={index} className="flex items-start">
            <span className="text-blue-500 dark:text-blue-400 mr-2">•</span>
            <span className="text-gray-900 dark:text-gray-100">{item}</span>
          </li>
        ))}
      </ul>
    );
  };

  const renderNumberedList = (data: string[]) => {
    return (
      <ol className="space-y-2">
        {data.map((item, index) => (
          <li key={index} className="flex items-start">
            <span className="bg-blue-200 dark:bg-blue-700 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0 text-blue-800 dark:text-blue-200">
              {index + 1}
            </span>
            <span className="text-gray-900 dark:text-gray-100">{item}</span>
          </li>
        ))}
      </ol>
    );
  };

  const renderGrid = (data: GridData[]) => {
    const gridCols = data.length <= 2 ? 'grid-cols-1 md:grid-cols-2' : 'grid-cols-1 md:grid-cols-3';
    return (
      <div className={`grid ${gridCols} gap-4`}>
        {data.map((item, index) => (
          <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">{item.title}</h4>
            <p className="text-gray-700 dark:text-gray-300">{item.content}</p>
          </div>
        ))}
      </div>
    );
  };

  const renderComparison = (data: ComparisonData[]) => {
    return (
      <div className="space-y-4">
        {data.map((item, index) => (
          <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-3">{item.label}</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded border dark:border-red-800">
                <h5 className="font-semibold text-red-800 dark:text-red-300 text-sm mb-1">Before</h5>
                <p className="text-red-700 dark:text-red-400 text-sm">{item.before}</p>
              </div>
              <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded border dark:border-green-800">
                <h5 className="font-semibold text-green-800 dark:text-green-300 text-sm mb-1">After</h5>
                <p className="text-green-700 dark:text-green-400 text-sm">{item.after}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderTable = (data: TableData) => {
    return (
      <div className="overflow-x-auto">
        <table className="w-full text-sm bg-white dark:bg-gray-800 rounded border dark:border-gray-700">
          <thead className="bg-gray-100 dark:bg-gray-700">
            <tr>
              {data.headers.map((header, index) => (
                <th key={index} className="p-3 text-left font-semibold text-gray-900 dark:text-gray-100">
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.rows.map((row, rowIndex) => (
              <tr key={rowIndex} className="border-t dark:border-gray-700">
                {row.map((cell, cellIndex) => (
                  <td key={cellIndex} className="p-3 text-gray-900 dark:text-gray-100">
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const renderKeyValueGrid = (data: KeyValueData[]) => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {data.map((item, index) => (
          <div key={index} className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">{item.key}</h4>
            <p className="text-blue-700 dark:text-blue-300">{item.value}</p>
          </div>
        ))}
      </div>
    );
  };

  const renderScatterPlot = (config: ScatterPlotConfig | any) => {
    // Handle both the expected format and the backend format
    const points = config.points || config.data || [];
    const { title, xLabel, yLabel } = config;
    const width = config.width || 500;
    const height = config.height || 400;
    
    if (!points || points.length === 0) {
      return (
        <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg text-center text-gray-500">
          No data points available
        </div>
      );
    }
    
    const maxX = Math.max(...points.map((d: any) => d.x));
    const minX = Math.min(...points.map((d: any) => d.x));
    const maxY = Math.max(...points.map((d: any) => d.y));
    const minY = Math.min(...points.map((d: any) => d.y));
    
    const plotWidth = width - 100; // Leave space for axes labels
    const plotHeight = height - 100;
    const plotStartX = 60;
    const plotStartY = 20;
    
    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

    return (
      <div className="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
        <h4 className="font-semibold text-center mb-6 text-gray-900 dark:text-gray-100">
          {title || 'Scatter Plot Visualization'}
        </h4>
        
        <div className="flex flex-col lg:flex-row gap-6 items-start">
          {/* Chart Area */}
          <div className="flex-1">
            <div
              className="relative bg-white dark:bg-gray-800 border dark:border-gray-600 rounded mx-auto"
              style={{ width: `${width}px`, height: `${height}px` }}
            >
              {/* Grid lines */}
              <svg className="absolute inset-0 pointer-events-none" width={width} height={height}>
                {/* Vertical grid lines */}
                {[0, 0.25, 0.5, 0.75, 1].map((ratio) => {
                  const x = plotStartX + ratio * plotWidth;
                  return (
                    <line
                      key={`v-${ratio}`}
                      x1={x}
                      y1={plotStartY}
                      x2={x}
                      y2={plotStartY + plotHeight}
                      stroke="#E5E7EB"
                      strokeWidth="1"
                      strokeDasharray="2,2"
                    />
                  );
                })}
                
                {/* Horizontal grid lines */}
                {[0, 0.25, 0.5, 0.75, 1].map((ratio) => {
                  const y = plotStartY + ratio * plotHeight;
                  return (
                    <line
                      key={`h-${ratio}`}
                      x1={plotStartX}
                      y1={y}
                      x2={plotStartX + plotWidth}
                      y2={y}
                      stroke="#E5E7EB"
                      strokeWidth="1"
                      strokeDasharray="2,2"
                    />
                  );
                })}
              </svg>

              {/* Data Points */}
              {points.map((point: any, index: number) => {
                const x = plotStartX + ((point.x - minX) / (maxX - minX)) * plotWidth;
                const y = plotStartY + plotHeight - ((point.y - minY) / (maxY - minY)) * plotHeight;
                const color = colors[index % colors.length];
                
                return (
                  <div
                    key={index}
                    className="absolute w-4 h-4 rounded-full shadow-lg cursor-pointer hover:scale-125 transition-transform"
                    style={{ 
                      left: `${x - 8}px`, 
                      top: `${y - 8}px`,
                      backgroundColor: color
                    }}
                    title={`${point.label}: (${point.x}, ${point.y})`}
                  />
                );
              })}

              {/* Y-axis label */}
              <div 
                className="absolute text-sm font-medium text-gray-700 dark:text-gray-300"
                style={{ 
                  left: '10px', 
                  top: '50%', 
                  transform: 'rotate(-90deg) translateY(-50%)',
                  transformOrigin: 'center'
                }}
              >
                {yLabel || 'Accuracy (%)'}
              </div>

              {/* X-axis label */}
              <div 
                className="absolute text-sm font-medium text-gray-700 dark:text-gray-300 text-center"
                style={{ 
                  left: '50%', 
                  bottom: '10px',
                  transform: 'translateX(-50%)'
                }}
              >
                {xLabel || 'Year'}
              </div>

              {/* Axis values */}
              {/* X-axis values */}
              {[minX, Math.round((minX + maxX) / 2), maxX].map((value, index) => {
                const x = plotStartX + (index / 2) * plotWidth;
                return (
                  <div
                    key={`x-${value}`}
                    className="absolute text-xs text-gray-600 dark:text-gray-400"
                    style={{ 
                      left: `${x}px`, 
                      top: `${plotStartY + plotHeight + 10}px`,
                      transform: 'translateX(-50%)'
                    }}
                  >
                    {value}
                  </div>
                );
              })}

              {/* Y-axis values */}
              {[minY, Math.round((minY + maxY) / 2), maxY].map((value, index) => {
                const y = plotStartY + plotHeight - (index / 2) * plotHeight;
                return (
                  <div
                    key={`y-${value}`}
                    className="absolute text-xs text-gray-600 dark:text-gray-400"
                    style={{ 
                      left: `${plotStartX - 35}px`, 
                      top: `${y}px`,
                      transform: 'translateY(-50%)'
                    }}
                  >
                    {value}%
                  </div>
                );
              })}
            </div>
          </div>

          {/* Legend */}
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg border dark:border-gray-600 min-w-[200px]">
            <h5 className="font-semibold text-sm text-gray-900 dark:text-gray-100 mb-3">Legend</h5>
            <div className="space-y-2">
              {points.map((point: any, index: number) => {
                const color = colors[index % colors.length];
                return (
                  <div key={index} className="flex items-center gap-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: color }}
                    />
                    <div className="text-xs text-gray-700 dark:text-gray-300 flex-1">
                      <div className="font-medium">{point.label}</div>
                      <div className="text-gray-500 dark:text-gray-400">
                        ({point.x}, {point.y}%)
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderContent = (step: StepConfig) => {
    switch (step.type) {
      case 'paragraph':
        return renderParagraph(step.data);
      case 'infoBox':
        return renderInfoBox(step.data);
      case 'bulletList':
        return renderBulletList(step.data);
      case 'numberedList':
        return renderNumberedList(step.data);
      case 'grid':
        return renderGrid(step.data);
      case 'comparison':
        return renderComparison(step.data);
      case 'table':
        return renderTable(step.data);
      case 'scatterPlot':
        return renderScatterPlot(step.data);
      case 'keyValueGrid':
        return renderKeyValueGrid(step.data);
      default:
        return <div className="text-gray-500">Unknown content type: {step.type}</div>;
    }
  };

  if (!steps || steps.length === 0) {
    return (
      <div className="text-center text-gray-500 dark:text-gray-400 p-8">
        No steps available
      </div>
    );
  }

  return (
    <div className={cn("bg-white dark:bg-gray-800 h-full flex flex-col rounded-lg", className)}>

      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto scrollbar-thin px-6">
        {/* Current step */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-8 h-full flex flex-col">
          <div className="flex items-center justify-center mb-6">
            {steps[currentStep].icon}
            <h2 className="text-2xl font-bold ml-3 text-gray-800 dark:text-gray-200">
              {steps[currentStep].title}
            </h2>
          </div>

          <div className="flex-1">
            {renderContent(steps[currentStep])}
          </div>
        </div>
      </div>

      {/* Navigation - Fixed at bottom */}
      <div className="flex justify-between items-center p-6 pt-4 border-t dark:border-gray-700 bg-white dark:bg-gray-800 flex-shrink-0 rounded-none">
        <Button
          variant="outline"
          onClick={prevStep}
          disabled={currentStep === 0 || isLoading}
          className="flex items-center"
        >
          <ChevronLeft className="w-4 h-4 mr-1" />
          Previous
        </Button>

        <span className="text-gray-500 dark:text-gray-400">
          {currentStep + 1} of {steps.length}
        </span>

        <Button
          onClick={shouldShowRestart ? restartLesson : nextStep}
          disabled={isLoading}
          className="flex items-center"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 mr-1 animate-spin rounded-full border-2 border-gray-300 border-t-white" />
              Saving...
            </>
          ) : (
            <>
              {shouldShowRestart ? 'Restart' : currentStep === steps.length - 1 ? 'Complete Lesson' : 'Next'}
              <ChevronRight className="w-4 h-4 ml-1" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
