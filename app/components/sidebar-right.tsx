'use client'

import * as React from "react"
import { <PERSON><PERSON>, <PERSON>n<PERSON><PERSON>, <PERSON>, <PERSON><PERSON>Icon } from "lucide-react"
import { cn } from "~/lib/utils"
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
} from "~/components/ui/sidebar"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "~/components/ui/tabs"
import { AnimatePresence, motion } from "framer-motion"



export interface SidebarTab {
  id: string
  label: string
  icon: LucideIcon
  content: React.ReactNode
}

export interface SidebarRightProps extends React.ComponentProps<typeof Sidebar> {
  isOpen: boolean
  isPinned: boolean
  title: string
  titleIcon?: LucideIcon
  tabs: SidebarTab[]
  defaultTab?: string
  onToggle: () => void
  onTogglePin: () => void
  onClose?: () => void
  width?: string
}

export function SidebarRight({
  isOpen,
  isPinned,
  title,
  titleIcon: TitleIcon,
  tabs,
  defaultTab,
  onToggle,
  onTogglePin,
  onClose,
  width = "24rem",
  className,
  ...props
}: SidebarRightProps) {
  // Don't render if no tabs provided
  if (!tabs || tabs.length === 0) {
    return null
  }

  const handleClose = onClose || onToggle
  const isVisible = isOpen || isPinned

  return (
    <AnimatePresence initial={false}>
      {isVisible && (
        <motion.aside
          key="right-sidebar"
          className={cn(
            "sticky top-0 h-svh",
            className
          )}
          initial={{ width: 0 }}
          animate={{ width }}
          exit={{ width: 0 }}
          transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
          style={{ overflow: "hidden" }}
        >
          <Sidebar
            collapsible="none"
            side="right"
            className={cn("h-full w-full rounded-lg")}
            {...props}
          >
            <SidebarHeader className="border-sidebar-border h-16 border-b">
              <div className="flex items-center justify-between px-4 h-full">
                <div className="flex items-center space-x-2">
                  {TitleIcon && <TitleIcon className="h-5 w-5 text-sidebar-foreground" />}
                  <span className="font-medium text-sidebar-foreground">
                    {title}
                  </span>
                </div>
                <div className="flex items-center space-x-1">
                  {/* Pin/Unpin Button */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onTogglePin}
                    className="h-8 w-8 p-0"
                    title={isPinned ? "Unpin sidebar" : "Pin sidebar"}
                  >
                    {isPinned ? (
                      <PinOff className="h-4 w-4" />
                    ) : (
                      <Pin className="h-4 w-4" />
                    )}
                  </Button>
                  
                  {/* Close Button - only show if not pinned */}
                  {!isPinned && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleClose}
                      className="h-8 w-8 p-0"
                      title="Close sidebar"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </SidebarHeader>
            
            <SidebarContent className="flex-1 overflow-hidden border-2 border-t-0 rounded-lg rounded-t-none">
              <Tabs defaultValue={defaultTab || tabs[0]?.id} className="h-full flex flex-col">
                <TabsList className="flex justify-between items-center px-2 w-full mt-2 flex-shrink-0">
                  {tabs.map((tab) => {
                    const IconComponent = tab.icon
                    return (
                      <TabsTrigger key={tab.id} value={tab.id} className="text-xs">
                        <IconComponent className="h-4 w-4 mr-1" />
                        {tab.label}
                      </TabsTrigger>
                    )
                  })}
                </TabsList>
                
                {tabs.map((tab) => (
                  <TabsContent key={tab.id} value={tab.id} className="flex-1 overflow-hidden overflow-x-hidden mt-2">
                    {tab.content}
                  </TabsContent>
                ))}
              </Tabs>
            </SidebarContent>
          </Sidebar>
        </motion.aside>
      )}
    </AnimatePresence>
  )
}
