"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/components/ui/card";
import { But<PERSON> } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import {
  Trophy,
  BookOpen,
  Target,
  Activity,
  Info,
  TrendingUp,
  Clock,
  Award,
  BarChart3,
} from "lucide-react";
import {
  usePointsBreakdown,
  type PointsBreakdownData,
} from "~/lib/hooks/use-points-breakdown";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "~/components/ui/collapsible";
import { ChevronDown, ChevronRight } from "lucide-react";

interface PointBreakdownProps {
  totalPoints: number;
  quizPoints?: number;
  progressPoints?: number;
  analyticsPoints?: number;
  contentCount?: number;
  quizCount?: number;
  sessionCount?: number;
}

export function PointBreakdown({
  totalPoints,
  quizPoints = 0,
  progressPoints = 0,
  analyticsPoints = 0,
  contentCount = 0,
  quizCount = 0,
  sessionCount = 0,
}: PointBreakdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedSections, setExpandedSections] = useState<
    Record<string, boolean>
  >({});
  const { data: breakdownResponse, isLoading: isLoadingBreakdown } =
    usePointsBreakdown();

  const breakdownData = breakdownResponse?.success
    ? breakdownResponse.data
    : null;

  // Calculate actual average points per completion
  const avgQuizPoints = quizCount > 0 ? Math.round(quizPoints / quizCount) : 0;
  const avgContentPoints =
    contentCount > 0 ? Math.round(progressPoints / contentCount) : 0;
  const avgSessionPoints =
    sessionCount > 0 ? Math.round(analyticsPoints / sessionCount) : 0;

  const toggleSection = (section: string) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const pointSources = [
    {
      icon: Target,
      label: "Quiz Completions",
      points: quizPoints,
      count: quizCount,
      description:
        quizCount > 0
          ? `${avgQuizPoints} points per completed quiz`
          : "No quizzes completed",
      color: "bg-blue-500",
    },
    {
      icon: BookOpen,
      label: "Learning Progress",
      points: progressPoints,
      count: contentCount,
      description:
        contentCount > 0
          ? `${avgContentPoints} points per completed learning content`
          : "No content completed",
      color: "bg-green-500",
    },
    {
      icon: Activity,
      label: "Learning Sessions",
      points: analyticsPoints,
      count: sessionCount,
      description:
        sessionCount > 0
          ? `${avgSessionPoints} points per unique learning session`
          : "No sessions recorded",
      color: "bg-purple-500",
    },
  ];

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Info className="h-4 w-4" />
          Point Breakdown
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            Point Breakdown
          </DialogTitle>
          <DialogDescription>
            See how your {totalPoints} points are calculated
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Total Points Summary */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-green-500" />
                  <span className="font-medium">Total Points</span>
                </div>
                <Badge variant="secondary" className="text-lg font-bold">
                  {totalPoints}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Point Sources Overview */}
          <div className="grid grid-cols-1 gap-4">
            {pointSources.map((source, index) => {
              const Icon = source.icon;
              return (
                <Card
                  key={index}
                  className="border-l-4"
                  style={{ borderLeftColor: source.color.replace("bg-", "#") }}
                >
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <div
                          className={`p-1.5 rounded-full ${source.color} text-white`}
                        >
                          <Icon className="h-3 w-3" />
                        </div>
                        <span className="font-medium text-sm">
                          {source.label}
                        </span>
                      </div>
                      <Badge variant="outline" className="whitespace-nowrap">
                        {source.points}&nbsp;pts
                      </Badge>
                    </div>
                    <div className="text-xs text-muted-foreground mb-1">
                      {source.description}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {source.count} {source.label.toLowerCase()} completed
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Detailed Breakdown */}
          {isLoadingBreakdown ? (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                </div>
              </CardContent>
            </Card>
          ) : breakdownData ? (
            <div className="space-y-4">
              {/* Quiz Points Summary */}
              {breakdownData.quiz.total.count > 0 && (
                <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-2">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <BarChart3 className="h-5 w-5 text-green-500" />
                      Total Quiz Points Summary
                    </CardTitle>
                    <CardDescription>
                      Combined points from all quiz completions
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {Object.entries(breakdownData.quiz.scoreRanges).map(
                      ([range, data]) => {
                        if (data.count === 0) return null;
                        const pointsPerQuiz =
                          range === "90+"
                            ? 100
                            : range === "80-89"
                              ? 80
                              : range === "70-79"
                                ? 60
                                : range === "60-69"
                                  ? 40
                                  : 20;
                        return (
                          <div
                            key={range}
                            className="flex items-center justify-between p-3 bg-white rounded-lg border"
                          >
                            <div className="flex items-center gap-3">
                              <Badge variant="outline" className="font-medium">
                                {range}%
                              </Badge>
                              <div className="text-sm text-muted-foreground">
                                {data.count} quiz{data.count !== 1 ? "es" : ""}
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold text-lg">
                                {data.points} pts
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {data.count} × {pointsPerQuiz} pts each
                              </div>
                            </div>
                          </div>
                        );
                      }
                    )}

                    {/* Grand Total */}
                    <div className="border-t pt-4 mt-4">
                      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-green-100 to-blue-100 rounded-lg border-2 border-dashed">
                        <div className="flex items-center gap-3">
                          <Trophy className="h-6 w-6 text-green-600" />
                          <div>
                            <div className="font-bold text-lg">Grand Total</div>
                            <div className="text-sm text-muted-foreground">
                              All quiz completions combined
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-2xl text-green-600">
                            {quizPoints} pts
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {breakdownData.quiz.total.count} quizzes •{" "}
                            {breakdownData.quiz.total.average} pts avg
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Learning Progress Total Points Summary */}
              {Object.keys(breakdownData.learning.levelBreakdown).length >
                0 && (
                <Card className="bg-gradient-to-r from-blue-50 to-orange-50 border-2">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2 text-lg">
                      <BarChart3 className="h-5 w-5 text-purple-500" />
                      Total Learning Points Summary
                    </CardTitle>
                    <CardDescription>
                      Combined base points and duration bonus points
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {Object.entries(breakdownData.learning.levelBreakdown).map(
                      ([level, data]) => {
                        const basePointsPerLevel =
                          level === "beginner"
                            ? 30
                            : level === "intermediate"
                              ? 50
                              : 70;
                        const totalForLevel =
                          data.basePoints + data.bonusPoints;
                        return (
                          <div
                            key={level}
                            className="flex items-center justify-between p-3 bg-white rounded-lg border"
                          >
                            <div className="flex items-center gap-3">
                              <Badge
                                variant="outline"
                                className="capitalize font-medium"
                              >
                                {level}
                              </Badge>
                              <div className="text-sm text-muted-foreground">
                                {data.count} items
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-semibold text-lg">
                                {totalForLevel} pts
                              </div>
                              <div className="text-xs text-muted-foreground">
                                {data.basePoints} base + {data.bonusPoints}{" "}
                                bonus
                              </div>
                            </div>
                          </div>
                        );
                      }
                    )}

                    {/* Grand Total */}
                    <div className="border-t pt-4 mt-4">
                      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-100 to-orange-100 rounded-lg border-2 border-dashed">
                        <div className="flex items-center gap-3">
                          <Trophy className="h-6 w-6 text-purple-600" />
                          <div>
                            <div className="font-bold text-lg">Grand Total</div>
                            <div className="text-sm text-muted-foreground">
                              All learning content combined
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-2xl text-purple-600">
                            {progressPoints} pts
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {Object.values(
                              breakdownData.learning.levelBreakdown
                            ).reduce(
                              (sum, data) => sum + data.basePoints,
                              0
                            )}{" "}
                            base +{" "}
                            {Object.values(
                              breakdownData.learning.levelBreakdown
                            ).reduce(
                              (sum, data) => sum + data.bonusPoints,
                              0
                            )}{" "}
                            bonus
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : breakdownResponse && !breakdownResponse.success ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center py-8">
                  <div className="text-sm text-red-500">
                    Failed to load breakdown data
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : null}

          {/* Point System Info */}
          <Card className="bg-muted/50">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Point System</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-1 text-xs text-muted-foreground">
                <div>
                  • Quiz completion: 100pts (90%+), 80pts (80-89%), 60pts
                  (70-79%), 40pts (60-69%), 20pts (&lt;60%)
                </div>
                <div>
                  • Learning content: Base points (30/50/70 for
                  beginner/intermediate/advanced) + duration bonus (5pts per
                  5min, capped at 15pts)
                </div>
                <div>• Unique learning session: 10 points each</div>
                <div className="mt-2 text-xs text-amber-600">
                  Note: Actual averages calculated from your performance are
                  shown above.
                </div>
                <div className="mt-2 text-xs text-amber-600">
                  Note: Learning content averages update only when content is
                  fully completed, not during step progress saves.
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
}
