import * as React from "react"
import { MessageSquare, FileText, Bookmark, Plus, Trash2, Clock, Edit3, Save, X } from "lucide-react"
import { ChatInterface } from "~/components/learn/ChatInterface"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "~/components/ui/card"
import { But<PERSON> } from "~/components/ui/button"
import { ScrollArea } from "~/components/ui/scroll-area"
import { Textarea } from "~/components/ui/textarea"
import { Badge } from "~/components/ui/badge"
import { useUIStore } from "~/lib/stores/ui-store"
import { SidebarRight, SidebarTab } from "~/components/sidebar-right"
import { 
  useGetProgress, 
  useAddBookmark, 
  useRemoveBookmark, 
  useAddNote,
  useDeleteNote
} from "~/lib/hooks/use-progress-api"

// Notes Component
function NotesComponent({ contentId }: { contentId: string }) {
  const { learning } = useUIStore()
  const { data: progressData } = useGetProgress(contentId)
  const addNoteMutation = useAddNote()
  const deleteNoteMutation = useDeleteNote()
  
  const [isAdding, setIsAdding] = React.useState(false)
  const [newNoteContent, setNewNoteContent] = React.useState("")
  const [currentStep, setCurrentStep] = React.useState(learning.currentStep)

  // Update current step when UI store changes
  React.useEffect(() => {
    setCurrentStep(learning.currentStep)
  }, [learning.currentStep])

  // Keep original indexes from server array for stable identification
  const notesRaw = React.useMemo(() => {
    return (progressData?.progress?.notes || []).map((note: any, originalIndex: number) => ({
      ...note,
      originalIndex,
      id: `${note.stepIndex}-${originalIndex}`,
      timestamp: note.timestamp || new Date().toISOString(),
    }))
  }, [progressData?.progress?.notes])

  const notes = React.useMemo(() => {
    return [...notesRaw].sort((a: any, b: any) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
  }, [notesRaw])

  const currentStepNotes = React.useMemo(() => {
    return notes.filter((note: any) => note.stepIndex === currentStep)
  }, [notes, currentStep])

  const handleAddNote = async () => {
    if (!newNoteContent.trim()) return

    try {
      await addNoteMutation.mutateAsync({
        action: 'add_note',
        contentId,
        stepId: currentStep.toString(),
        content: newNoteContent.trim(),
        noteType: 'step_specific',
      })
      
      setNewNoteContent("")
      setIsAdding(false)
    } catch (error) {
      console.error("Failed to add note:", error)
    }
  }

  const handleDeleteNote = async (originalIndex: number) => {
    try {
      // Encode contentId and originalIndex into noteId as `${contentId}:${index}`
      const noteId = `${contentId}:${originalIndex}`
      await deleteNoteMutation.mutateAsync({
        action: 'delete_note',
        noteId,
      })
    } catch (error) {
      console.error("Failed to delete note:", error)
    }
  }

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (diffDays === 1) {
      return 'Yesterday'
    } else if (diffDays < 7) {
      return `${diffDays} days ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Notes</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAdding(true)}
            disabled={isAdding}
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Note
          </Button>
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          Step {Number.isFinite(currentStep) ? currentStep + 1 : 1} • {notes.length} total notes
        </p>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {/* Add new note form */}
          {isAdding && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Add Note for Step {currentStep + 1}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Textarea
                  placeholder="Write your note here..."
                  value={newNoteContent}
                  onChange={(e) => setNewNoteContent(e.target.value)}
                  className="min-h-[100px] resize-none"
                  autoFocus
                />
                <div className="flex justify-end space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={() => {
                      setIsAdding(false)
                      setNewNoteContent("")
                    }}
                  >
                    <X className="h-4 w-4 mr-1" />
                    Cancel
                  </Button>
                  <Button 
                    size="sm" 
                    onClick={handleAddNote}
                    disabled={!newNoteContent.trim() || addNoteMutation.isPending}
                  >
                    <Save className="h-4 w-4 mr-1" />
                    Save Note
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Current step notes only */}
          {currentStepNotes.length > 0 && (
            <div className="space-y-3">
              {currentStepNotes.map((note: any) => (
                <Card key={note.id} className="border-l-4 border-l-primary">
                  <CardContent className="p-3">
                    <p className="text-sm whitespace-pre-wrap">{note.content}</p>
                    <div className="flex items-center justify-between mt-2">
                      <Badge variant="secondary" className="text-xs">
                        Step {note.stepIndex + 1}
                      </Badge>
                      <div className="flex items-center gap-3">
                        <span className="text-xs text-muted-foreground">
                          <Clock className="h-3 w-3 inline mr-1" />
                          {formatDate(note.timestamp)}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                          title="Delete note"
                          onClick={() => handleDeleteNote(note.originalIndex)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Empty state */}
          {currentStepNotes.length === 0 && !isAdding && (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground text-sm">No notes for this step</p>
              <p className="text-muted-foreground text-xs mt-1">Add notes to remember key insights for Step {currentStep + 1}</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}

// Bookmarks Component  
function BookmarksComponent({ contentId }: { contentId: string }) {
  const { learning } = useUIStore()
  const { data: progressData } = useGetProgress(contentId)
  const addBookmarkMutation = useAddBookmark()
  const removeBookmarkMutation = useRemoveBookmark()
  
  const [currentStep, setCurrentStep] = React.useState(learning.currentStep)

  // Update current step when UI store changes
  React.useEffect(() => {
    setCurrentStep(learning.currentStep)
  }, [learning.currentStep])

  const allBookmarks = React.useMemo(() => {
    const bookmarksRaw = progressData?.progress?.bookmarks || []
    return bookmarksRaw.map((bookmark: any, index: number) => ({
      ...bookmark,
      id: `${bookmark.stepIndex}-${index}`, // Generate ID from step and index
      timestamp: bookmark.timestamp || new Date().toISOString()
    })).sort((a: any, b: any) => a.stepIndex - b.stepIndex)
  }, [progressData?.progress?.bookmarks])

  const currentStepBookmarks = React.useMemo(() => {
    return allBookmarks.filter((bookmark: any) => bookmark.stepIndex === currentStep)
  }, [allBookmarks, currentStep])

  const isCurrentStepBookmarked = React.useMemo(() => {
    return allBookmarks.some((bookmark: any) => bookmark.stepIndex === currentStep)
  }, [allBookmarks, currentStep])

  const handleToggleCurrentStepBookmark = async () => {
    try {
      if (isCurrentStepBookmarked) {
        await removeBookmarkMutation.mutateAsync({
          action: 'remove_bookmark',
          contentId,
          stepId: currentStep.toString(),
        })
      } else {
        await addBookmarkMutation.mutateAsync({
          action: 'add_bookmark',
          contentId,
          stepId: currentStep.toString(),
        })
      }
    } catch (error) {
      console.error("Failed to toggle bookmark:", error)
    }
  }

  const handleRemoveBookmark = async (stepIndex: number) => {
    try {
      await removeBookmarkMutation.mutateAsync({
        action: 'remove_bookmark',
        contentId,
        stepId: stepIndex.toString(),
      })
    } catch (error) {
      console.error("Failed to remove bookmark:", error)
    }
  }

  const formatDate = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    } else if (diffDays === 1) {
      return 'Yesterday'
    } else if (diffDays < 7) {
      return `${diffDays} days ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Bookmarks</h3>
          <Button
            variant={isCurrentStepBookmarked ? "default" : "outline"}
            size="sm"
            onClick={handleToggleCurrentStepBookmark}
            disabled={addBookmarkMutation.isPending || removeBookmarkMutation.isPending}
          >
            <Bookmark className={`h-4 w-4 mr-1 ${isCurrentStepBookmarked ? 'fill-current' : ''}`} />
            {isCurrentStepBookmarked ? 'Bookmarked' : 'Bookmark Step'}
          </Button>
        </div>
        <p className="text-xs text-muted-foreground mt-1">
          Step {currentStep + 1} • {currentStepBookmarks.length} bookmark{currentStepBookmarks.length !== 1 ? 's' : ''}
        </p>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {/* Current step bookmarks list */}
          {currentStepBookmarks.map((bookmark: any) => (
            <Card key={bookmark.id} className="border-l-4 border-l-primary bg-primary/5">
              <CardContent className="p-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <Bookmark className="h-4 w-4 text-primary fill-current" />
                      <Badge variant="default" className="text-xs">
                        Step {bookmark.stepIndex + 1}
                      </Badge>
                    </div>
                    {bookmark.note && (
                      <p className="text-sm text-muted-foreground mt-2 whitespace-pre-wrap">
                        {bookmark.note}
                      </p>
                    )}
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-muted-foreground">
                        <Clock className="h-3 w-3 inline mr-1" />
                        {formatDate(bookmark.timestamp)}
                      </span>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveBookmark(bookmark.stepIndex)}
                    className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                    title="Remove bookmark"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* Empty state */}
          {currentStepBookmarks.length === 0 && (
            <div className="text-center py-8">
              <Bookmark className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground text-sm">No bookmarks for this step</p>
              <p className="text-muted-foreground text-xs mt-1">Bookmark Step {currentStep + 1} for quick reference</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}

/**
 * Learning-specific wrapper for the reusable SidebarRight component.
 * This maintains backward compatibility with the original learning sidebar functionality.
 */
export function SidebarRightLearning({
  ...props
}: Omit<React.ComponentProps<typeof SidebarRight>, 'isOpen' | 'isPinned' | 'title' | 'titleIcon' | 'tabs' | 'defaultTab' | 'onToggle' | 'onTogglePin'>) {
  const { 
    rightSidebarOpen, 
    rightSidebarPinned, 
    learningContent,
    toggleRightSidebar,
    toggleRightSidebarPin
  } = useUIStore()

  const hasLearningContent = learningContent.contentId && learningContent.contentTitle

  // Don't render if no learning content available
  if (!hasLearningContent) {
    return null
  }

  const tabs: SidebarTab[] = [
    {
      id: "assistant",
      label: "Assistant",
      icon: MessageSquare,
      content: (
        <ChatInterface
          learningContentId={learningContent.contentId!}
          className="h-full border-0"
        />
      )
    },
    {
      id: "notes",
      label: "Notes",
      icon: FileText,
      content: (
        <NotesComponent contentId={learningContent.contentId!} />
      )
    },
    {
      id: "bookmarks",
      label: "Bookmarks",
      icon: Bookmark,
      content: (
        <BookmarksComponent contentId={learningContent.contentId!} />
      )
    }
  ]

  return (
    <SidebarRight
      className="pb-4"
      isOpen={rightSidebarOpen}
      isPinned={rightSidebarPinned}
      title="Learning Tools"
      titleIcon={MessageSquare}
      tabs={tabs}
      defaultTab="assistant"
      onToggle={toggleRightSidebar}
      onTogglePin={toggleRightSidebarPin}
      {...props}
    />
  )
}