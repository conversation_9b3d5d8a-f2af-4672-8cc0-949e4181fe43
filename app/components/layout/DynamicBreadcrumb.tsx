import React from 'react';
import { Link } from 'react-router';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  BreadcrumbEllipsis,
} from "~/components/ui/breadcrumb";
import { useBreadcrumbs } from '~/lib/hooks/use-breadcrumbs';

export function DynamicBreadcrumb() {
  const { currentBreadcrumbs } = useBreadcrumbs();

  if (!currentBreadcrumbs || currentBreadcrumbs.length === 0) {
    return null;
  }

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {currentBreadcrumbs.map((crumb, index) => {
          const isLast = index === currentBreadcrumbs.length - 1;
          const isEllipsis = crumb.isEllipsis;

          return (
            <React.Fragment key={`${crumb.href}-${index}`}>
              <BreadcrumbItem className={index === 0 ? "hidden md:block" : undefined}>
                {isEllipsis ? (
                  <BreadcrumbEllipsis />
                ) : isLast ? (
                  <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
                ) : (
                  <BreadcrumbLink asChild>
                    <Link to={crumb.href}>{crumb.label}</Link>
                  </BreadcrumbLink>
                )}
              </BreadcrumbItem>
              {!isLast && (
                <BreadcrumbSeparator className={index === 0 ? "hidden md:block" : undefined} />
              )}
            </React.Fragment>
          );
        })}
      </BreadcrumbList>
    </Breadcrumb>
  );
}