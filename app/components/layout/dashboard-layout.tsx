import { SidebarLeft } from "~/components/sidebar-left"
import { SidebarRightLearning } from "~/components/sidebar-right-learning"
import { DynamicBreadcrumb } from "~/components/layout/DynamicBreadcrumb"
import { Separator } from "~/components/ui/separator"
import {
  <PERSON>barInset,
  SidebarProvider,
  SidebarTrigger,
} from "~/components/ui/sidebar"
import { ThemeToggle } from "~/lib/theme/theme-toggle"

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <SidebarProvider>
      <SidebarLeft />
      <SidebarInset className="flex flex-col h-screen">
        <header className="sticky top-0 z-10 flex h-16 shrink-0 items-center gap-2 bg-background border-b transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <DynamicBreadcrumb />
          </div>
          <div className="ml-auto px-4">
            <ThemeToggle size="sm" />
          </div>
        </header>
        <main className="flex-1 overflow-auto">
          <div className="flex flex-col gap-4 p-4 h-full">
            {children}
          </div>
        </main>
      </SidebarInset>
      <SidebarRightLearning width="28rem" />
    </SidebarProvider>
  )
}