import React from "react";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardHeader } from "~/components/ui/card";
import {
  FileQuestion,
  BookmarkPlus,
  Star,
  Share2,
  Sparkles,
  Plus,
  Clock,
  User,
  Brain,
  PanelRightClose,
  PanelRightOpen,
  Loader2,
  RefreshCw,
} from "lucide-react";
import { cn } from "~/lib/utils";
import { useUIStore } from "~/lib/stores/ui-store";
import { ReindexButton } from "./ReindexButton";

interface LearningContentHeaderProps {
  contentId: string;
  title: string;
  description: string;
  progress: number; // 0-100
  currentStep: number;
  totalSteps: number;
  estimatedReadingTime: number;
  learningLevel: "beginner" | "intermediate" | "advanced";
  isPublic: boolean;
  contentType?: "kwaci-primer" | "standard";
  onGenerateQuiz?: () => void;
  onNotesBookmarks?: () => void;
  onRateFeedback?: () => void;
  onShare?: () => void;
  onGenerateSimilar?: () => void;
  onCreateNew?: () => void;
  isGeneratingSimilar?: boolean;
  className?: string;
}

// Sidebar Toggle Button Component
function SidebarToggleButton() {
  const { rightSidebarOpen, rightSidebarPinned, toggleRightSidebar } = useUIStore();
  
  // When sidebar is pinned, always show it as "open" state regardless of actual open state
  const displayAsOpen = rightSidebarPinned || rightSidebarOpen;
  
  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={toggleRightSidebar}
      className="flex items-center space-x-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
      title={rightSidebarPinned 
        ? "Learning tools are pinned" 
        : displayAsOpen 
          ? "Close learning tools" 
          : "Open learning tools"
      }
      disabled={rightSidebarPinned}
    >
      {displayAsOpen ? (
        <>
          <PanelRightClose className="h-4 w-4" />
          <span className="text-sm">Close Tools</span>
        </>
      ) : (
        <>
          <PanelRightOpen className="h-4 w-4" />
          <span className="text-sm">Learning Tools</span>
        </>
      )}
    </Button>
  );
}

export function LearningContentHeader({
  contentId,
  title,
  description,
  progress,
  currentStep,
  totalSteps,
  estimatedReadingTime,
  learningLevel,
  isPublic,
  contentType = "standard",
  onGenerateQuiz,
  onNotesBookmarks,
  onRateFeedback,
  onShare,
  onGenerateSimilar,
  onCreateNew,
  isGeneratingSimilar = false,
  className,
}: LearningContentHeaderProps) {
  const getLevelColor = (level: string) => {
    switch (level) {
      case "beginner":
        return "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-700";
      case "intermediate":
        return "bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300 border border-orange-200 dark:border-orange-700";
      case "advanced":
        return "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-700";
      default:
        return "bg-gray-50 dark:bg-gray-900/20 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700";
    }
  };

  return (
    <Card
      className={cn(
        "border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 shadow-lg dark:shadow-black/20 rounded-lg",
        className
      )}
    >
      <CardHeader className="pb-6">
        {/* Title and Description */}
        <div className="space-y-3">
          <div className="flex items-start justify-between">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 flex-1">
              {title}
            </h1>
            <SidebarToggleButton />
          </div>
          <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
            {description}
          </p>
        </div>

        {/* Progress and Metadata */}
        <div className="flex items-center justify-between mt-6">
          {/* Metadata Row */}
          <div className="flex items-center space-x-6 text-sm text-gray-600 dark:text-gray-300">
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              <span>{estimatedReadingTime} min read</span>
            </div>
            <div className="flex items-center">
              <FileQuestion className="h-4 w-4 mr-2" />
              <span>{totalSteps} steps</span>
            </div>
            <div className="flex items-center">
              <Share2 className="h-4 w-4 mr-2" />
              <span>{isPublic ? "Public" : "Private"}</span>
            </div>
            {/* Content Type Indicator */}
            <div className="flex items-center">
              <Brain className="h-4 w-4 mr-2" />
              <span>{contentType === "kwaci-primer" ? "Kwaci Primer" : "Standard"}</span>
            </div>
            {/* Level Indicator */}
            <div className="flex items-center justify-between">
              <Badge
                className={`${getLevelColor(learningLevel)} px-3 py-1.5 font-medium`}
              >
                {learningLevel}
              </Badge>
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-300">
              Step{" "}
              <span className="font-semibold text-gray-900 dark:text-gray-100">
                {currentStep}
              </span>
              /<span className="font-semibold">{totalSteps}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                {Math.round(progress)}%
              </div>
              <div className="w-12 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div
                  className="bg-blue-600 dark:bg-blue-400 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Action Buttons */}
        <div className="flex items-center space-x-2 flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onRateFeedback}
            className="flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <Star className="h-4 w-4" />
            <span>Rate & Feedback</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onShare}
            className="flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <Share2 className="h-4 w-4" />
            <span>Share</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onGenerateSimilar}
            disabled={isGeneratingSimilar}
            className="flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            {isGeneratingSimilar ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Sparkles className="h-4 w-4" />
            )}
            <span>{isGeneratingSimilar ? "Generating..." : "Generate Similar"}</span>
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={onCreateNew}
            className="flex items-center space-x-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Create New</span>
          </Button>

          {/* Reindex Button */}
          <ReindexButton
            contentId={contentId}
            variant="outline"
            size="sm"
            className="hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
            onSuccess={(result) => {
              console.log('Content reindexed successfully:', result);
            }}
            onError={(error) => {
              console.error('Content reindexing failed:', error);
            }}
          />
        </div>
      </CardContent>
    </Card>
  );
}
