import { useState } from 'react';
import { Button } from '~/components/ui/button';
import { RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import { cn } from '~/lib/utils';

interface ReindexButtonProps {
  contentId: string;
  className?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  onSuccess?: (result: any) => void;
  onError?: (error: string) => void;
}

export function ReindexButton({
  contentId,
  className,
  variant = 'outline',
  size = 'sm',
  onSuccess,
  onError,
}: ReindexButtonProps) {
  const [isReindexing, setIsReindexing] = useState(false);
  const [reindexStatus, setReindexStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState<string>('');

  const handleReindex = async () => {
    if (isReindexing) return;

    setIsReindexing(true);
    setReindexStatus('idle');
    setStatusMessage('');

    try {
      const response = await fetch('/api/reindex', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contentId,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setReindexStatus('success');
        setStatusMessage(`Reindexed ${result.result.chunksIndexed} chunks in ${result.result.processingTime}ms`);
        onSuccess?.(result);
        
        // Reset status after 3 seconds
        setTimeout(() => {
          setReindexStatus('idle');
          setStatusMessage('');
        }, 3000);
      } else {
        setReindexStatus('error');
        setStatusMessage(result.error || 'Reindexing failed');
        onError?.(result.error || 'Reindexing failed');
        
        // Reset status after 5 seconds
        setTimeout(() => {
          setReindexStatus('idle');
          setStatusMessage('');
        }, 5000);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Network error';
      setReindexStatus('error');
      setStatusMessage(errorMessage);
      onError?.(errorMessage);
      
      // Reset status after 5 seconds
      setTimeout(() => {
        setReindexStatus('idle');
        setStatusMessage('');
      }, 5000);
    } finally {
      setIsReindexing(false);
    }
  };

  const getButtonContent = () => {
    if (isReindexing) {
      return (
        <>
          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          Reindexing...
        </>
      );
    }

    if (reindexStatus === 'success') {
      return (
        <>
          <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
          Reindexed
        </>
      );
    }

    if (reindexStatus === 'error') {
      return (
        <>
          <AlertCircle className="h-4 w-4 mr-2 text-red-600" />
          Failed
        </>
      );
    }

    return (
      <>
        <RefreshCw className="h-4 w-4 mr-2" />
        Reindex Content
      </>
    );
  };

  const getButtonVariant = () => {
    if (reindexStatus === 'success') return 'default';
    if (reindexStatus === 'error') return 'destructive';
    return variant;
  };

  return (
    <div className="flex flex-col items-center space-y-1">
      <Button
        variant={getButtonVariant()}
        size={size}
        onClick={handleReindex}
        disabled={isReindexing}
        className={cn(
          'transition-all duration-200',
          reindexStatus === 'success' && 'bg-green-600 hover:bg-green-700 text-white',
          reindexStatus === 'error' && 'bg-red-600 hover:bg-red-700 text-white',
          className
        )}
        title="Reindex this content in the vector database for improved search results"
      >
        {getButtonContent()}
      </Button>
      
      {statusMessage && (
        <div className={cn(
          'text-xs px-2 py-1 rounded text-center max-w-48 truncate',
          reindexStatus === 'success' && 'text-green-700 bg-green-50 border border-green-200',
          reindexStatus === 'error' && 'text-red-700 bg-red-50 border border-red-200'
        )}>
          {statusMessage}
        </div>
      )}
    </div>
  );
}

// Batch reindex button for reindexing all content
interface BatchReindexButtonProps {
  className?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  onSuccess?: (result: any) => void;
  onError?: (error: string) => void;
}

export function BatchReindexButton({
  className,
  variant = 'outline',
  size = 'sm',
  onSuccess,
  onError,
}: BatchReindexButtonProps) {
  const [isReindexing, setIsReindexing] = useState(false);
  const [reindexStatus, setReindexStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [statusMessage, setStatusMessage] = useState<string>('');

  const handleBatchReindex = async () => {
    if (isReindexing) return;

    const confirmed = confirm(
      'This will reindex ALL content in the system. This may take several minutes. Continue?'
    );

    if (!confirmed) return;

    setIsReindexing(true);
    setReindexStatus('idle');
    setStatusMessage('');

    try {
      const response = await fetch('/api/reindex', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reindexAll: true,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setReindexStatus('success');
        setStatusMessage(`Reindexed ${result.results.successful}/${result.results.total} content items`);
        onSuccess?.(result);
        
        // Reset status after 5 seconds
        setTimeout(() => {
          setReindexStatus('idle');
          setStatusMessage('');
        }, 5000);
      } else {
        setReindexStatus('error');
        setStatusMessage(result.error || 'Batch reindexing failed');
        onError?.(result.error || 'Batch reindexing failed');
        
        // Reset status after 5 seconds
        setTimeout(() => {
          setReindexStatus('idle');
          setStatusMessage('');
        }, 5000);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Network error';
      setReindexStatus('error');
      setStatusMessage(errorMessage);
      onError?.(errorMessage);
      
      // Reset status after 5 seconds
      setTimeout(() => {
        setReindexStatus('idle');
        setStatusMessage('');
      }, 5000);
    } finally {
      setIsReindexing(false);
    }
  };

  const getButtonContent = () => {
    if (isReindexing) {
      return (
        <>
          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
          Reindexing All...
        </>
      );
    }

    if (reindexStatus === 'success') {
      return (
        <>
          <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
          Completed
        </>
      );
    }

    if (reindexStatus === 'error') {
      return (
        <>
          <AlertCircle className="h-4 w-4 mr-2 text-red-600" />
          Failed
        </>
      );
    }

    return (
      <>
        <RefreshCw className="h-4 w-4 mr-2" />
        Reindex All Content
      </>
    );
  };

  const getButtonVariant = () => {
    if (reindexStatus === 'success') return 'default';
    if (reindexStatus === 'error') return 'destructive';
    return variant;
  };

  return (
    <div className="flex flex-col items-center space-y-1">
      <Button
        variant={getButtonVariant()}
        size={size}
        onClick={handleBatchReindex}
        disabled={isReindexing}
        className={cn(
          'transition-all duration-200',
          reindexStatus === 'success' && 'bg-green-600 hover:bg-green-700 text-white',
          reindexStatus === 'error' && 'bg-red-600 hover:bg-red-700 text-white',
          className
        )}
        title="Reindex all content in the vector database - use this after system updates"
      >
        {getButtonContent()}
      </Button>
      
      {statusMessage && (
        <div className={cn(
          'text-xs px-2 py-1 rounded text-center max-w-48 truncate',
          reindexStatus === 'success' && 'text-green-700 bg-green-50 border border-green-200',
          reindexStatus === 'error' && 'text-red-700 bg-red-50 border border-red-200'
        )}>
          {statusMessage}
        </div>
      )}
    </div>
  );
}
