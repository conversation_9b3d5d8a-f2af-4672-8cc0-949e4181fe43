import { useState, useEffect, use<PERSON><PERSON>back, useMemo, useRef } from "react";
import { redirect, useNavigate } from "react-router";
import { useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Progress } from "~/components/ui/progress";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "~/components/ui/popover";
import {
  Clock,
  Trophy,
  FileQuestion,
  Eye,
  RotateCcw,
  MoreVertical,
  Loader2,
  Bookmark,
  StickyNote,
  Check,
  ChevronLeft,
  ChevronRight,
  Brain,
  AlertCircle,
} from "lucide-react";
import { cn } from "~/lib/utils";
import { LearningContentHeader } from "./LearningContentHeader";
import { MultiStepExplain } from "../templates/MultiStepExplain";
import type { StepConfig } from "../templates/types";
import {
  useGetProgress,
  useUpdateProgress,
  useTrackEvent,
  useAddBookmark,
  useRemoveBookmark,
  useAddNote,
  useUpdateNote,
  useDeleteNote,
} from "~/lib/hooks/use-progress-api";
import { useUIStore } from "~/lib/stores/ui-store";
import { QuizSelectionModal } from "../quiz/QuizSelectionModal";
import { useGenerateQuiz } from "~/lib/hooks/use-quiz-api";
import type { QuizGenerationConfig } from "../quiz/QuizSelectionModal";
import { useGenerateSimilarContent } from "~/lib/hooks/use-learning-api";
import { showErrorToast, showSuccessToast } from "~/lib/error-handling";
import { getOrCreateSessionId } from "~/lib/utils/session";
import { FeedbackModal } from "./FeedbackModal";

// Types for quiz history and progress data
interface QuizHistoryItem {
  id: string;
  quizId: string;
  quizTitle?: string;
  isCompleted: boolean;
  score?: {
    percentage?: number;
  };
  startedAt: string;
}

interface ProgressData {
  progress?: {
    completedSteps: number[];
    isCompleted: boolean;
    sessionCountComplete?: number;
    bookmarks: Array<{ stepIndex: number }>;
    notes: Array<{ stepIndex: number; content: string }>;
  };
}

interface EnhancedLearningContentDisplayProps {
  contentId: string;
  title: string;
  description: string;
  steps: StepConfig[];
  learningLevel: "beginner" | "intermediate" | "advanced";
  estimatedReadingTime: number;
  isPublic: boolean;
  contentType?: "kwaci-primer" | "standard";
  initialStep?: number;
  progress?: number;
  onStepChange?: (step: number, isLessonComplete?: boolean) => void;
  onProgressUpdate?: (progress: number) => void;
  onQuizSelect?: (quizId: string, attemptId?: string) => void;
  onRetakeQuiz?: (quizId: string) => void;
  onGenerateNew?: () => void;
  className?: string;
  totalTimeSpent?: number;
  sessionCount?: number;
}

export function EnhancedLearningContentDisplay({
  contentId,
  title,
  description,
  steps,
  learningLevel,
  estimatedReadingTime,
  isPublic,
  contentType = "standard",
  initialStep = 0,
  progress = 0,
  onStepChange,
  onProgressUpdate,
  onQuizSelect,
  onRetakeQuiz,
  onGenerateNew,
  className,
  totalTimeSpent = 0,
  sessionCount = 0,
}: EnhancedLearningContentDisplayProps) {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Quiz Selection Modal state
  const [isQuizModalOpen, setIsQuizModalOpen] = useState(false);
  
  // Feedback Modal state
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);

  // Quiz generation hook with navigation callback
  const generateQuizMutation = useGenerateQuiz();

  // Quiz generation handler
  const handleQuizGeneration = (config: QuizGenerationConfig) => {
    generateQuizMutation.mutate(
      {
        ...config,
        learningContentId: contentId,
      },
      {
        onSuccess: (result) => {
          // API client unwraps the response, so result contains {quizId, message} directly
          if (result.quizId) {
            // Close modal and navigate to the generated quiz
            setIsQuizModalOpen(false);
            navigate(`/dashboard/quiz/${result.quizId}`, { replace: true });
          } else {
            console.error("Quiz generation failed - no quizId found:", result);
            // TODO: Show user-friendly error message
          }
        },
        onError: (error) => {
          console.error("Quiz generation error:", error);
          // TODO: Show user-friendly error message
        },
      }
    );
  };

  const [currentStep, setCurrentStep] = useState(initialStep);
  const [timeSpent, setTimeSpent] = useState(0); // Time spent on current step
  const [totalSessionTime, setTotalSessionTime] = useState(0); // Total time for this session
  const [stepStartTime, setStepStartTime] = useState<number>(Date.now());
  const [sessionStartTime, setSessionStartTime] = useState<number>(Date.now());
  const [isLessonCompleted, setIsLessonCompleted] = useState(false);

  // Session ID for consistent tracking across API calls
  const [sessionId] = useState(() => getOrCreateSessionId());

  // State for note dialog
  const [showNoteDialog, setShowNoteDialog] = useState(false);
  const [noteContent, setNoteContent] = useState("");
  const [editingNoteIndex, setEditingNoteIndex] = useState<number | null>(null);

  // Trophy animation state
  const [trophyAnimations, setTrophyAnimations] = useState<Set<number>>(
    new Set()
  );
  const previousCompletedStepsRef = useRef<number[]>([]);

  // UI store actions for right sidebar content and visibility
  const setLearningContent = useUIStore((state) => state.setLearningContent);
  const setRightSidebarOpen = useUIStore((state) => state.setRightSidebarOpen);
  const setCurrentStepInStore = useUIStore((state) => state.setCurrentStep);

  // Fetch learning progress from API
  const { data: progressData, isLoading: progressLoading } =
    useGetProgress(contentId);

  // Progress update mutation with onSuccess callback for trophy animations
  const updateProgressMutation = useUpdateProgress();
  const trackEventMutation = useTrackEvent();

  // Add onSuccess handler for trophy animations (similar to reference implementation)
  useEffect(() => {
    if (updateProgressMutation.isSuccess && updateProgressMutation.data) {
      // Get the variables from the last successful mutation
      const variables = updateProgressMutation.variables;
      if (variables?.completedSteps) {
        const previousCompleted = previousCompletedStepsRef.current;
        const newlyCompleted = variables.completedSteps.filter(
          (step) => !previousCompleted.includes(step)
        );

        // Animate only the newly completed steps (not all completed steps)
        newlyCompleted.forEach((stepIndex) => {
          triggerTrophyAnimation(stepIndex);
        });

        // Update the ref to track the new completed steps
        previousCompletedStepsRef.current = [...variables.completedSteps];
      }
    }
  }, [
    updateProgressMutation.isSuccess,
    updateProgressMutation.data,
    updateProgressMutation.variables,
  ]);

  // Bookmark mutations
  const addBookmarkMutation = useAddBookmark();
  const removeBookmarkMutation = useRemoveBookmark();

  // Note mutations
  const addNoteMutation = useAddNote();
  const updateNoteMutation = useUpdateNote();
  const deleteNoteMutation = useDeleteNote();

  // Extract progress data (avoid using props, use API data instead)
  const progressDataReal = progressData?.progress || {
    completedSteps: [],
    isCompleted: false,
    sessionCountComplete: sessionCount,
    bookmarks: [],
    notes: [],
  };

  // State for pagination
  const [quizHistoryOffset, setQuizHistoryOffset] = useState(0);
  const [allQuizAttempts, setAllQuizAttempts] = useState<QuizHistoryItem[]>([]);
  const [hasMoreQuizzes, setHasMoreQuizzes] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  // Generation loading state
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStatus, setGenerationStatus] = useState("");
  const [generationError, setGenerationError] = useState<string | null>(null);
  const [isRedirecting, setIsRedirecting] = useState(false);

  // Mock quiz history data - TODO: Replace with actual API calls
  const quizHistoryData = {
    attempts: allQuizAttempts,
    hasMore: hasMoreQuizzes,
    stats: {
      totalAttempts: 0,
      completedAttempts: 0,
      averageScore: 0,
      bestScore: 0,
    },
  };

  useEffect(() => {
    // Set current step from database progress on first mount
    if (
      progressDataReal?.completedSteps &&
      progressDataReal.completedSteps.length > 0
    ) {
      // Get the last completed step + 1 as the current step (or stay at last step if all completed)
      const lastCompletedStep = Math.max(...progressDataReal.completedSteps);
      const nextStep =
        lastCompletedStep + 1 < steps.length
          ? lastCompletedStep + 1
          : lastCompletedStep;
      setCurrentStep(nextStep);
    } else {
      // If no completed steps, start from the beginning or use initialStep
      setCurrentStep(initialStep);
    }
  }, [initialStep, progressDataReal?.completedSteps, steps.length]);

  // Sync current step with API progress data
  useEffect(() => {
    if (progressDataReal?.currentStepIndex !== undefined) {
      setCurrentStep(progressDataReal.currentStepIndex);
    }
  }, [progressDataReal?.currentStepIndex]);

  // Sync local currentStep with UI store for sidebar integration
  useEffect(() => {
    setCurrentStepInStore(currentStep);
  }, [currentStep, setCurrentStepInStore]);

  // Initialize session timer when component first loads
  useEffect(() => {
    const now = Date.now();
    setSessionStartTime(now);
    setStepStartTime(now);
  }, [contentId]); // Reset when content changes

  // Initialize previousCompletedStepsRef with existing progress data
  useEffect(() => {
    if (progressDataReal?.completedSteps) {
      previousCompletedStepsRef.current = [...progressDataReal.completedSteps];
    }
  }, [progressDataReal?.completedSteps]);

  // Check and update isCompleted status on mount if all steps are completed but isCompleted is false
  useEffect(() => {
    // Only run this check when progress data is loaded and not already processing
    if (
      !progressLoading &&
      progressDataReal &&
      !progressDataReal.isCompleted &&
      progressDataReal.completedSteps &&
      progressDataReal.completedSteps.length === steps.length &&
      steps.length > 0 &&
      !updateProgressMutation.isPending
    ) {
      // All steps are completed but isCompleted is false, update it
       updateProgressMutation.mutateAsync({
         action: "update_progress",
         contentId,
         stepId: steps[steps.length - 1]?.title || `step-${steps.length - 1}`,
         progress: 100,
         timeSpent: 0, // No additional time spent for this correction
         completed: true, // Force completion
         completedSteps: progressDataReal.completedSteps,
         currentStep: progressDataReal.completedSteps[progressDataReal.completedSteps.length - 1] || 0,
         sessionId,
       }).then(() => {
         // Invalidate the learning material list to refresh completion status
         queryClient.invalidateQueries({ queryKey: ["myLearning.infinite"] });
       }).catch((error) => {
         console.error("Failed to update completion status:", error);
       });
    }
  }, [
    progressLoading,
    progressDataReal?.isCompleted,
    progressDataReal?.completedSteps,
    steps.length,
    contentId,
    sessionId,
    updateProgressMutation.isPending,
  ]);

  // Track time spent with optimized interval
  useEffect(() => {
    // Don't track time in review mode or if lesson is already completed
    if (progressDataReal?.isCompleted) {
      return;
    }

    // Use a more efficient interval that only updates when component is visible and lesson is not completed
    const updateTime = () => {
      if (document.visibilityState === "visible" && !isLessonCompleted) {
        const now = Date.now();
        const stepTime = Math.floor((now - stepStartTime) / 1000);
        const totalTime = Math.floor((now - sessionStartTime) / 1000);
        setTimeSpent(stepTime);
        setTotalSessionTime(totalTime);
      }
    };

    const intervalId = setInterval(updateTime, 1000);

    // Also update when page becomes visible
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && !isLessonCompleted) {
        updateTime();
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      clearInterval(intervalId);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [
    isLessonCompleted,
    stepStartTime,
    sessionStartTime,
    progressDataReal?.isCompleted,
  ]);

  // Trophy animation logic with cleanup
  const triggerTrophyAnimation = useCallback((stepIndex: number) => {
    setTrophyAnimations((prev) => new Set([...prev, stepIndex]));

    const timeoutId = setTimeout(() => {
      setTrophyAnimations((prev) => {
        const newSet = new Set(prev);
        newSet.delete(stepIndex);
        return newSet;
      });
    }, 2000); // Animation duration

    // Store timeout ID for potential cleanup
    return () => clearTimeout(timeoutId);
  }, []);

  // Use refs to store current values and avoid dependency issues
  const currentValuesRef = useRef({
    currentStep,
    progressDataReal,
    sessionStartTime,
    contentId,
    stepsLength: steps.length,
  });

  // Update ref values when they change
  useEffect(() => {
    currentValuesRef.current = {
      currentStep,
      progressDataReal,
      sessionStartTime,
      contentId,
      stepsLength: steps.length,
    };
  }, [
    currentStep,
    progressDataReal,
    sessionStartTime,
    contentId,
    steps.length,
  ]);

  // Function to save current progress including total time (using refs to avoid dependency loops)
  const saveCurrentProgress = useCallback(
    async (
      options: {
        isLessonComplete?: boolean;
        newCurrentStep?: number;
        silent?: boolean;
      } = {}
    ) => {
      const { isLessonComplete = false, silent = false } = options;
      const {
        currentStep: refCurrentStep,
        progressDataReal: refProgressData,
        sessionStartTime: refSessionStart,
        contentId: refContentId,
        stepsLength,
      } = currentValuesRef.current;
      const newCurrentStep = options.newCurrentStep ?? refCurrentStep;

      // Skip saving in review mode
      if (refProgressData?.isCompleted && !isLessonComplete) {
        return;
      }

      try {
        // Calculate current session time and total time
        const now = Date.now();
        const currentSessionTime = Math.floor((now - refSessionStart) / 1000);
        const currentTotalTime =
          (refProgressData?.totalTimeSpent || 0) + currentSessionTime;

        // Get current completed steps
        const currentCompletedSteps = refProgressData?.completedSteps || [];

        await updateProgressMutation.mutateAsync({
          action: "update_progress",
          contentId: refContentId,
          stepId: newCurrentStep.toString(),
          progress: Math.round(
            (currentCompletedSteps.length / stepsLength) * 100
          ),
          timeSpent: currentTotalTime,
          completed: isLessonComplete,
          completedSteps: currentCompletedSteps,
          currentStep: newCurrentStep,
          sessionId: sessionId,
        });

        // Track analytics event for time tracking
        if (!silent) {
          await trackEventMutation.mutateAsync({
            action: "track_event",
            eventType: "time_spent",
            contentId: refContentId,
            stepId: newCurrentStep.toString(),
            duration: currentSessionTime,
            sessionId: sessionId,
            metadata: {
              totalTime: currentTotalTime,
              sessionTime: currentSessionTime,
            },
          });
        }
      } catch (error) {
        console.error("Failed to save progress:", error);
      }
    },
    [updateProgressMutation, trackEventMutation]
  );

  // Use actual progress data from API instead of props
  const completedSteps: number[] = progressDataReal?.completedSteps || [];

  // Save progress when component unmounts or user leaves page
  useEffect(() => {
    let isMounted = true;

    const handleBeforeUnload = () => {
      if (isMounted) {
        // Use a synchronous approach for beforeunload
        const now = Date.now();
        const currentSessionTime = Math.floor((now - sessionStartTime) / 1000);
        const currentTotalTime =
          (progressDataReal?.totalTimeSpent || 0) + currentSessionTime;

        // Send beacon for reliable data transmission on page unload
        const data = {
          action: "update_progress",
          contentId,
          stepId: currentStep.toString(),
          progress: Math.round(
            ((progressDataReal?.completedSteps?.length || 0) / steps.length) *
              100
          ),
          timeSpent: currentTotalTime,
          completed: false,
          completedSteps: progressDataReal?.completedSteps || [],
          currentStep: currentStep,
        };

        // Send beacon with proper content type
        const blob = new Blob([JSON.stringify(data)], {
          type: "application/json",
        });
        navigator.sendBeacon("/api/progress", blob);
      }
    };

    const handleVisibilityChange = (() => {
      let lastCall = 0;
      return () => {
        if (document.visibilityState === "hidden" && isMounted) {
          // Save progress when tab becomes hidden (with throttling)
          const now = Date.now();
          if (now - lastCall > 5000) {
            lastCall = now;
            saveCurrentProgress({ silent: true }).catch(console.error);
          }
        }
      };
    })();

    // Add event listeners
    window.addEventListener("beforeunload", handleBeforeUnload);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Return cleanup function
    return () => {
      isMounted = false;
      window.removeEventListener("beforeunload", handleBeforeUnload);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [
    contentId,
    currentStep,
    steps.length,
    sessionStartTime,
    progressDataReal?.totalTimeSpent,
    progressDataReal?.completedSteps,
    saveCurrentProgress,
  ]);

  // Periodic save every 30 seconds during active learning
  useEffect(() => {
    if (progressDataReal?.isCompleted) return;

    const interval = setInterval(() => {
      if (document.visibilityState === "visible" && !isLessonCompleted) {
        // Avoid using saveCurrentProgress to prevent dependency issues
        const now = Date.now();
        const currentSessionTime = Math.floor((now - sessionStartTime) / 1000);
        const currentTotalTime =
          (progressDataReal?.totalTimeSpent || 0) + currentSessionTime;

        updateProgressMutation.mutate({
          action: "update_progress",
          contentId,
          stepId: currentStep.toString(),
          progress: Math.round(
            ((progressDataReal?.completedSteps?.length || 0) / steps.length) *
              100
          ),
          timeSpent: currentTotalTime,
          completed: false,
          completedSteps: progressDataReal?.completedSteps || [],
          currentStep: currentStep,
          sessionId: sessionId,
        });
      }
    }, 30000); // Save every 30 seconds

    return () => clearInterval(interval);
  }, [
    progressDataReal?.isCompleted,
    progressDataReal?.totalTimeSpent,
    progressDataReal?.completedSteps,
    isLessonCompleted,
    sessionStartTime,
    contentId,
    currentStep,
    steps.length,
    updateProgressMutation,
  ]);

  // Handle step changes with improved error handling
  const handleStepChange = useCallback(
    async (step: number, isLessonComplete = false) => {
      // Validate step bounds
      if (step < 0 || step >= steps.length) {
        console.warn(
          `Invalid step index: ${step}. Must be between 0 and ${steps.length - 1}`
        );
        return;
      }

      // Calculate time spent on current step
      const currentTime = Date.now();
      const stepTimeSpent = Math.floor((currentTime - stepStartTime) / 1000);

      setCurrentStep(step);
      if (onStepChange) {
        onStepChange(step, isLessonComplete);
      }

      // Skip progress updates in review mode or if lesson is already completed
      if (progressDataReal?.isCompleted) {
        setStepStartTime(currentTime);
        return;
      }

      // Calculate and update progress
      const newProgress = ((step + 1) / steps.length) * 100;
      onProgressUpdate?.(newProgress);

      // Save progress to backend
      try {
        const currentCompletedSteps = progressDataReal?.completedSteps || [];
        const newCompletedSteps = [...currentCompletedSteps];

        // Mark previous steps as completed if moving forward
        if (step > currentStep) {
          for (let i = currentStep; i < step; i++) {
            if (!newCompletedSteps.includes(i)) {
              newCompletedSteps.push(i);
            }
          }
        }

        // Handle lesson completion - mark current step as completed
        if (isLessonComplete && !newCompletedSteps.includes(step)) {
          newCompletedSteps.push(step);
          setIsLessonCompleted(true); // Stop the timer
        }

        // Calculate total time spent (existing total + current session time)
        const currentTotalTime =
          (progressDataReal?.totalTimeSpent || 0) + totalSessionTime;

        await updateProgressMutation.mutateAsync({
          action: "update_progress",
          contentId,
          stepId: step.toString(),
          progress: Math.round((newCompletedSteps.length / steps.length) * 100),
          timeSpent: currentTotalTime, // Send total accumulated time
          completed: isLessonComplete,
          completedSteps: newCompletedSteps,
          currentStep: step,
          sessionId: sessionId,
        });

        // Track analytics event for step completion
        await trackEventMutation.mutateAsync({
          action: "track_event",
          eventType: isLessonComplete ? "content_completed" : "step_completed",
          contentId,
          stepId: step.toString(),
          duration: stepTimeSpent,
          sessionId: sessionId,
          metadata: {
            totalSteps: steps.length,
            completedSteps: newCompletedSteps.length,
            totalTime: currentTotalTime,
            sessionTime: totalSessionTime,
          },
        });

        // If lesson is completed, revalidate my learning list to update progress
        if (isLessonComplete) {
          queryClient.invalidateQueries({ queryKey: ["myLearning.infinite"] });
        }

        // Trophy animation will be triggered in the useEffect hook after successful save
      } catch (error) {
        console.error("Failed to save progress:", error);
        // Optionally show user-friendly error message
      }

      // Update step start time for next calculation
      setStepStartTime(currentTime);
    },
    [
      currentStep,
      steps.length,
      progressDataReal?.completedSteps,
      progressDataReal?.totalTimeSpent,
      stepStartTime,
      totalSessionTime,
      contentId,
      onStepChange,
      onProgressUpdate,
      updateProgressMutation,
      trackEventMutation,
    ]
  );

  // Sync with external step changes
  useEffect(() => {
    setCurrentStep(initialStep ?? 0);
  }, [initialStep]);

  // Reset step start time when step changes
  useEffect(() => {
    setStepStartTime(Date.now());
  }, [currentStep]);

  const progress_data = progressDataReal;

  // Check if lesson is completed when all steps are done
  useEffect(() => {
    if (progress_data?.isCompleted) {
      return;
    }

    const allStepsCompleted =
      completedSteps.length === steps.length && steps.length > 0;
    if (allStepsCompleted && !isLessonCompleted) {
      setIsLessonCompleted(true);
    }
    // Reset completion status if not all steps are completed
    if (!allStepsCompleted && isLessonCompleted) {
      setIsLessonCompleted(false);
    }
  }, [
    completedSteps.length,
    steps.length,
    isLessonCompleted,
    progress_data?.isCompleted,
  ]);

  const completionPercentage = useMemo(() => {
    if (isLessonCompleted || progressDataReal?.isCompleted) {
      return 100;
    }
    return Math.round((completedSteps.length / steps.length) * 100);
  }, [
    completedSteps.length,
    steps.length,
    isLessonCompleted,
    progressDataReal?.isCompleted,
  ]);

  const bookmarks = progress_data?.bookmarks || [];
  const notes = progress_data?.notes || [];

  // Current step bookmark and note status with memoization
  const isStepBookmarked = useMemo(() => {
    return bookmarks.some(
      (b: { stepIndex: number }) => b.stepIndex === currentStep
    );
  }, [bookmarks, currentStep]);

  const stepNotes = useMemo(() => {
    return notes.filter(
      (n: { stepIndex: number }) => n.stepIndex === currentStep
    );
  }, [notes, currentStep]);

  // Format time with hours support
  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;

    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      // Format as HH:MM:SS when hours are present
      return `${hours.toString().padStart(2, "0")}:${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
    } else {
      // Format as Xm Ys when under 1 hour
      return `${mins}m ${secs}s`;
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      handleStepChange(currentStep - 1);
    }
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      handleStepChange(currentStep + 1);
    }
  };

  // Restart lesson functionality
  const handleRestartLesson = useCallback(async () => {
    try {
      // Reset all states
      setCurrentStep(0);
      setTimeSpent(0);
      setTotalSessionTime(0);
      const now = Date.now();
      setStepStartTime(now);
      setSessionStartTime(now);
      setIsLessonCompleted(false);
      setTrophyAnimations(new Set());

      // Reset progress in database
      await updateProgressMutation.mutateAsync({
        action: "update_progress",
        contentId,
        stepId: "0",
        progress: 0,
        timeSpent: 0,
        completed: false,
        completedSteps: [],
        currentStep: 0,
      });

      // Revalidate my learning list to update progress
      queryClient.invalidateQueries({ queryKey: ["myLearning.infinite"] });

      // Call external onStepChange if provided
      if (onStepChange) {
        onStepChange(0);
      }
    } catch (error) {
      console.error("Failed to restart lesson:", error);
    }
  }, [contentId, onStepChange, updateProgressMutation, queryClient]);

  // Bookmark toggle handler
  const handleToggleBookmark = useCallback(async () => {
    // Prevent bookmark changes in review mode
    if (progress_data?.isCompleted) {
      return;
    }

    try {
      if (isStepBookmarked) {
        await removeBookmarkMutation.mutateAsync({
          action: "remove_bookmark",
          contentId,
          stepId: currentStep.toString(),
        });
      } else {
        await addBookmarkMutation.mutateAsync({
          action: "add_bookmark",
          contentId,
          stepId: currentStep.toString(),
        });
      }
    } catch (error) {
      console.error("Failed to toggle bookmark:", error);
    }
  }, [
    isStepBookmarked,
    contentId,
    currentStep,
    progress_data?.isCompleted,
    addBookmarkMutation,
    removeBookmarkMutation,
  ]);

  // Note handlers
  const handleAddNote = () => {
    // Prevent note changes in review mode
    if (progress_data?.isCompleted) {
      return;
    }

    setShowNoteDialog(true);
    setNoteContent("");
    setEditingNoteIndex(null);
  };

  const handleSaveNote = async () => {
    if (!noteContent.trim()) return;

    // Prevent note changes in review mode
    if (progress_data?.isCompleted) {
      return;
    }

    try {
      if (editingNoteIndex !== null) {
        // Update existing note - for now we'll just add a new note
        // In a full implementation, you'd need note IDs to update specific notes
        await addNoteMutation.mutateAsync({
          action: "add_note",
          contentId,
          stepId: currentStep.toString(),
          content: noteContent,
          noteType: "step_specific",
        });
      } else {
        // Add new note
        await addNoteMutation.mutateAsync({
          action: "add_note",
          contentId,
          stepId: currentStep.toString(),
          content: noteContent,
          noteType: "step_specific",
        });
      }

      setShowNoteDialog(false);
      setNoteContent("");
      setEditingNoteIndex(null);
    } catch (error) {
      console.error("Failed to save note:", error);
    }
  };

  // Load more quiz attempts
  const handleLoadMore = async () => {
    if (isLoadingMore || !hasMoreQuizzes) return;

    setIsLoadingMore(true);
    const newOffset = quizHistoryOffset + 3;

    try {
      // TODO: Implement quiz history API call
      console.log("Load more quiz attempts, offset:", newOffset);
      setQuizHistoryOffset(newOffset);
    } catch (error) {
      console.error("Failed to load more quiz attempts:", error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  // Format date
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  const handleGenerateQuiz = () => {
    onGenerateNew?.();
  };

  const handleNotesBookmarks = () => {
    // TODO: Implement notes and bookmarks
    console.log("Open notes and bookmarks for content:", contentId);
  };

  const handleRateFeedback = () => {
    setIsFeedbackModalOpen(true);
  };

  const handleShare = () => {
    // TODO: Implement sharing
    console.log("Share content:", contentId);
  };

  const generateSimilarMutation = useGenerateSimilarContent();

  const handleGenerateSimilar = () => {
    if (generateSimilarMutation.isPending) return;

    // Reset generation state
    setGenerationProgress(0);
    setGenerationStatus("Preparing to generate similar content...");
    setGenerationError(null);
    setIsRedirecting(false);

    try {
      generateSimilarMutation.mutate(
        { learningContentId: contentId },
        {
          onSuccess: (result) => {
            // Animate to 100% before showing success
            setGenerationStatus("Content generated successfully!");
            setGenerationProgress(100);
            console.log(result);

            const newId =
              (result as any)?.id ||
              (result as any)?.content?.id ||
              (result as any)?.data.id;

            if (newId) {
              // Set redirecting state immediately to prevent UI flicker
              setIsRedirecting(true);

              // Handle navigation after a short delay
              setTimeout(async () => {
                try {
                  // Invalidate queries first
                  await queryClient.invalidateQueries({
                    queryKey: ["myLearning.infinite"],
                  });

                  showSuccessToast("Generated similar content");
                  setGenerationStatus("Redirecting to your new content...");

                  // Redirect after delay (matching regular content generation)
                  setTimeout(() => {
                    window.location.href = `/dashboard/learn/${newId}`;
                  }, 2500);
                } catch (error) {
                  console.error("Navigation error:", error);
                  setGenerationError("Failed to navigate to new content");
                  setIsRedirecting(false);
                }
              }, 800); // Delay to show success message
            } else {
              console.warn(
                "Similar content generated but no ID returned",
                result
              );
              setGenerationError(
                "Generated content but could not navigate to it"
              );
            }
          },
          onError: (error) => {
            console.error("Generate similar content error:", error);

            // Extract error message
            let errorMessage =
              "An unexpected error occurred while generating similar content.";
            if (error instanceof Error) {
              errorMessage = error.message;
            } else if (typeof error === "object" && error !== null) {
              const errorObj = error as any;
              if (errorObj.message) {
                errorMessage = errorObj.message;
              } else if (errorObj.error) {
                errorMessage = errorObj.error;
              }
            }

            setGenerationError(errorMessage);
            setGenerationProgress(0);
            setGenerationStatus("");
            showErrorToast(
              "Failed to generate similar content. Please try again."
            );
          },
        }
      );
    } catch (err) {
      console.error("Unexpected error generating similar content:", err);
      const errorMessage =
        err instanceof Error
          ? err.message
          : "Failed to generate similar content.";
      setGenerationError(errorMessage);
      setGenerationProgress(0);
      setGenerationStatus("");
      showErrorToast("Failed to generate similar content.");
    }
  };

  const handleCreateNew = () => {
    // TODO: Navigate to create new content
    console.log("Create new content");
  };

  // Simulate progress during generation
  useEffect(() => {
    if (generateSimilarMutation.isPending) {
      const progressInterval = setInterval(() => {
        setGenerationProgress((prev) => {
          // Don't go past 96% during generation - save final 4% for completion
          if (prev >= 96) return prev;

          // Progressive slowdown as we approach 96%
          let increment: number;
          if (prev < 70) {
            // Normal speed: 5-12%
            increment = Math.random() * 7 + 5;
          } else if (prev < 85) {
            // Slower: 2-6%
            increment = Math.random() * 4 + 2;
          } else if (prev < 93) {
            // Much slower: 0.5-2%
            increment = Math.random() * 1.5 + 0.5;
          } else {
            // Very slow crawl: 0.1-0.5%
            increment = Math.random() * 0.4 + 0.1;
          }

          return Math.min(prev + increment, 96); // Cap at 96% during generation
        });
      }, 800);

      const statusMessages = [
        "Analyzing your content...",
        "Finding similar topics...",
        "Generating new perspective...",
        "Creating learning steps...",
        "Finalizing content...",
      ];

      let statusIndex = 0;
      const statusInterval = setInterval(() => {
        if (statusIndex < statusMessages.length) {
          setGenerationStatus(statusMessages[statusIndex]);
          statusIndex++;
        }
      }, 2000);

      return () => {
        clearInterval(progressInterval);
        clearInterval(statusInterval);
      };
    }
  }, [generateSimilarMutation.isPending]);

  // Chat functionality is now handled directly by ChatInterface component

  // Chat function for AI assistant
  const handleSendMessage = async (message: string): Promise<string> => {
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          learningContentId: contentId,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      const data = await response.json();
      
      if (data.success && data.message) {
        return data.message.content;
      } else {
        throw new Error(data.error || 'Unknown error occurred');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  };

  // Ensure the right sidebar reflects current learning content
  useEffect(() => {
    setLearningContent({
      contentId,
      contentTitle: title,
      onSendMessage: handleSendMessage,
    });
    setRightSidebarOpen(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contentId, title]);

  const currentStepData = steps[currentStep];
  const quizHistory = allQuizAttempts;
  const stats = quizHistoryData?.stats || {
    totalAttempts: 0,
    completedAttempts: 0,
    averageScore: 0,
    bestScore: 0,
  };

  if (!steps || steps.length === 0) {
    return (
      <div className="max-w-4xl mx-auto p-6 bg-white dark:bg-gray-800">
        <div className="text-center text-gray-500 dark:text-gray-400">
          No steps provided
        </div>
      </div>
    );
  }

  // Show loading overlay during generation or redirecting
  if (generateSimilarMutation.isPending || isRedirecting) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="p-8">
            <div className="text-center">
              <div className="mb-6">
                <Brain className="h-16 w-16 text-blue-500 mx-auto animate-pulse" />
              </div>
              <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                Generating Similar Content
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Our AI is creating content similar to what you're currently
                learning. This may take a few moments.
              </p>

              {/* Progress Bar */}
              <Progress value={generationProgress} className="mb-4" />

              {/* Status Message */}
              <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-300">
                <Clock className="h-4 w-4 animate-spin" />
                <span>
                  {generationStatus || "Preparing to generate content..."}
                </span>
              </div>

              {/* Progress Percentage */}
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
                {Math.round(generationProgress)}% complete
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show error overlay if generation failed
  if (generationError) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-lg mx-4">
          <CardContent className="p-8">
            <div className="text-center">
              <div className="mb-6">
                <AlertCircle className="h-16 w-16 text-red-500 mx-auto" />
              </div>
              <h2 className="text-2xl font-bold mb-4 text-gray-900 dark:text-gray-100">
                Generation Failed
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                We encountered an issue while generating your similar content:
              </p>

              {/* Error Message */}
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-700 rounded-lg p-4 mb-6">
                <p className="text-red-800 dark:text-red-300 text-sm">
                  {generationError}
                </p>
              </div>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  onClick={() => {
                    setGenerationError(null);
                    handleGenerateSimilar();
                  }}
                  className="w-full"
                >
                  Try Again
                </Button>
                <Button
                  onClick={() => setGenerationError(null)}
                  variant="outline"
                  className="w-full"
                >
                  Cancel
                </Button>
              </div>

              {/* Help Text */}
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-4">
                If this error persists, try refreshing the page or contact
                support.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "bg-background flex flex-col overflow-auto h-full",
        className
      )}
    >
      <div className="w-full h-full flex flex-col overflow-auto">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-full">
          {/* Main Content Area */}
          <div className="lg:col-span-4 flex flex-col h-full">
            {/* Header - Fixed */}
            <div className="flex-shrink-0">
              <LearningContentHeader
                contentId={contentId}
                title={title}
                description={description}
                progress={completionPercentage}
                currentStep={currentStep + 1}
                totalSteps={steps.length}
                estimatedReadingTime={estimatedReadingTime}
                learningLevel={learningLevel}
                isPublic={isPublic}
                contentType={contentType}
                onGenerateQuiz={handleGenerateQuiz}
                onNotesBookmarks={handleNotesBookmarks}
                onRateFeedback={handleRateFeedback}
                onShare={handleShare}
                onGenerateSimilar={handleGenerateSimilar}
                onCreateNew={handleCreateNew}
                isGeneratingSimilar={generateSimilarMutation.isPending}
              />
            </div>

            {/* Main Content Card with Three-Section Layout */}
            <div className="flex-1 min-h-0 mt-6 rounded-lg">
              <Card className="h-full flex flex-col border-2 border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 transition-all duration-200">
                <CardContent className="p-0 h-full flex flex-col rounded-lg">
                  {/* Header Section - Enhanced Progress Indicator with 3-column grid */}
                  <div className="grid grid-cols-3 items-center p-4 pb-3 flex-shrink-0 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-800/30 rounded-lg">
                    {/* Left Column: Learning Progress */}
                    <div className="flex items-center space-x-3 text-xs text-gray-600 dark:text-gray-300">
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <div className="w-32 text-left">
                          <span className="font-mono">
                            {progress_data?.isCompleted
                              ? formatTime(
                                  progressDataReal?.totalTimeSpent || 0
                                )
                              : `${formatTime(timeSpent)} / ${formatTime((progressDataReal?.totalTimeSpent || 0) + totalSessionTime)}`}
                          </span>
                        </div>
                        {progress_data?.isCompleted ? (
                          <span className="text-gray-500 dark:text-gray-400 whitespace-nowrap">
                            (Total)
                          </span>
                        ) : (
                          <span className="text-gray-500 dark:text-gray-400 whitespace-nowrap">
                            (Step / Total)
                          </span>
                        )}
                      </div>
                      {progress_data?.isCompleted && (
                        <div className="flex items-center space-x-1">
                          <span>
                            Sessions: {progress_data?.sessionCountComplete || 0}
                          </span>
                        </div>
                      )}
                      <div
                        className={cn(
                          "font-medium",
                          isLessonCompleted
                            ? "text-green-600 dark:text-green-400"
                            : "text-blue-600 dark:text-blue-400"
                        )}
                      >
                        {completionPercentage}%
                        {isLessonCompleted && (
                          <span className="ml-1 text-green-600 dark:text-green-400">
                            ✓ Complete
                          </span>
                        )}
                      </div>
                      <div>
                        Step {currentStep + 1}/{steps.length}
                      </div>
                    </div>

                    {/* Center Column: Step Navigation Dots - Always Centered */}
                    <div className="flex justify-center">
                      <div className="flex space-x-2">
                        {steps.map((_, index) => {
                          // Use API progress data for accurate completion state
                          const isCompleted = completedSteps.includes(index);
                          const isCurrent = index === currentStep;
                          const showTrophyAnimation =
                            trophyAnimations.has(index);
                          return (
                            <div
                              key={index}
                              className={cn(
                                "w-4 h-4 rounded-full transition-all duration-300 flex items-center justify-center relative border-2",
                                updateProgressMutation.isPending || trackEventMutation.isPending
                                  ? "cursor-not-allowed opacity-50"
                                  : "cursor-pointer hover:scale-110 hover:shadow-lg",
                                isCurrent
                                  ? "bg-blue-500 dark:bg-blue-400 border-blue-600 dark:border-blue-300 shadow-blue-200 dark:shadow-blue-900/50 ring-2 ring-blue-300 dark:ring-blue-500"
                                  : isCompleted
                                    ? "bg-green-500 dark:bg-green-400 border-green-600 dark:border-green-300 shadow-green-200 dark:shadow-green-900/50"
                                    : "bg-gray-300 dark:bg-gray-600 border-gray-400 dark:border-gray-500 hover:bg-gray-400 dark:hover:bg-gray-500"
                              )}
                              onClick={() => {
                                if (!updateProgressMutation.isPending && !trackEventMutation.isPending) {
                                  handleStepChange(index);
                                }
                              }}
                            >
                              {isCompleted && !showTrophyAnimation && (
                                <Check className="h-2.5 w-2.5 text-white font-bold" />
                              )}
                              {showTrophyAnimation && (
                                <div className="absolute -top-0 left-1/2 transform -translate-x-1/2 z-10">
                                  <Trophy className="h-4 w-4 text-yellow-400 animate-trophy-jump drop-shadow-lg" />
                                  <div className="absolute inset-0 rounded-full bg-yellow-400 opacity-20 animate-ping scale-200" />
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </div>

                    {/* Right Column: Action Buttons and Quiz History */}
                    <div className="flex justify-end items-center space-x-3 text-xs text-gray-600 dark:text-gray-300">
                      {/* Restart Button - only show when lesson is completed */}
                      {progress_data?.isCompleted && (
                        <button
                          onClick={handleRestartLesson}
                          className="flex items-center space-x-1 hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 dark:hover:from-green-900/20 dark:hover:to-emerald-900/20 px-3 py-2 rounded-lg transition-all duration-200 text-green-600 dark:text-green-400 border border-green-200 dark:border-green-700 hover:border-green-300 dark:hover:border-green-600 hover:shadow-md"
                          title="Restart lesson"
                        >
                          <RotateCcw className="h-4 w-4" />
                          <span className="font-medium">Restart</span>
                        </button>
                      )}

                      {/* Bookmark Button */}
                      <button
                        onClick={handleToggleBookmark}
                        disabled={progress_data?.isCompleted}
                        className={cn(
                          "flex items-center space-x-1 px-3 py-2 rounded-lg transition-all duration-200 border",
                          progress_data?.isCompleted
                            ? "opacity-50 cursor-not-allowed border-gray-200 dark:border-gray-600"
                            : "hover:shadow-md border-gray-200 dark:border-gray-600 hover:border-yellow-300 dark:hover:border-yellow-600",
                          isStepBookmarked
                            ? "text-yellow-600 dark:text-yellow-400 bg-gradient-to-r from-yellow-50 to-amber-50 dark:from-yellow-900/20 dark:to-amber-900/20 border-yellow-300 dark:border-yellow-600"
                            : "text-gray-600 dark:text-gray-400 hover:bg-gradient-to-r hover:from-yellow-50 hover:to-amber-50 dark:hover:from-yellow-900/10 dark:hover:to-amber-900/10"
                        )}
                        title={
                          progress_data?.isCompleted
                            ? "Bookmarks disabled in review mode"
                            : isStepBookmarked
                              ? "Remove bookmark"
                              : "Bookmark this step"
                        }
                      >
                        <Bookmark
                          className={cn(
                            "h-4 w-4",
                            isStepBookmarked && "fill-current"
                          )}
                        />
                      </button>

                      {/* Note Button */}
                      <button
                        onClick={handleAddNote}
                        disabled={progress_data?.isCompleted}
                        className={cn(
                          "flex items-center space-x-1 px-3 py-2 rounded-lg transition-all duration-200 relative border",
                          progress_data?.isCompleted
                            ? "opacity-50 cursor-not-allowed border-gray-200 dark:border-gray-600"
                            : "hover:shadow-md border-gray-200 dark:border-gray-600 hover:border-purple-300 dark:hover:border-purple-600",
                          stepNotes.length > 0
                            ? "text-purple-600 dark:text-purple-400 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/20 dark:to-violet-900/20 border-purple-300 dark:border-purple-600"
                            : "text-gray-600 dark:text-gray-400 hover:bg-gradient-to-r hover:from-purple-50 hover:to-violet-50 dark:hover:from-purple-900/10 dark:hover:to-violet-900/10"
                        )}
                        title={
                          progress_data?.isCompleted
                            ? "Notes disabled in review mode"
                            : "Add note to this step"
                        }
                      >
                        <StickyNote className="h-4 w-4" />
                        {stepNotes.length > 0 && (
                          <span className="absolute -top-1 -right-1 bg-gradient-to-r from-purple-500 to-violet-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-bold border-2 border-white dark:border-gray-800 shadow-lg">
                            {stepNotes.length}
                          </span>
                        )}
                      </button>

                      {stats.completedAttempts > 0 ? (
                        <Popover key={`quiz-history-${contentId}`}>
                          <PopoverTrigger asChild>
                            <button className="flex items-center space-x-2 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-900/20 dark:hover:to-indigo-900/20 px-3 py-2 rounded-lg transition-all duration-200 border border-gray-200 dark:border-gray-600 hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-md">
                              <div className="font-medium text-gray-700 dark:text-gray-300">
                                {(() => {
                                  // Calculate unique quiz count
                                  const uniqueQuizIds = new Set(
                                    quizHistory.map((quiz) => quiz.quizId)
                                  );
                                  const quizCount = uniqueQuizIds.size;
                                  return `${quizCount} quiz${quizCount !== 1 ? "zes" : ""}`;
                                })()}
                              </div>
                              <MoreVertical className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                            </button>
                          </PopoverTrigger>
                          <PopoverContent
                            className="w-96 p-4 border-2 border-gray-200 dark:border-gray-600 shadow-2xl dark:shadow-black/40 bg-white dark:bg-gray-800 rounded-lg"
                            align="end"
                          >
                            <div className="space-y-3">
                              <div className="flex items-center justify-between">
                                <h3 className="font-medium text-gray-900 dark:text-gray-100">
                                  Quiz History
                                </h3>
                                <div className="text-xs text-gray-500 dark:text-gray-400">
                                  {stats.completedAttempts} attempt
                                  {stats.completedAttempts !== 1 ? "s" : ""}
                                </div>
                              </div>

                              <div className="pt-2 border-t border-gray-200 dark:border-gray-700">
                                <button
                                  onClick={onGenerateNew}
                                  className="w-full flex items-center justify-center space-x-2 px-4 py-3 text-sm bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-lg transition-all duration-200 font-medium shadow-lg hover:shadow-xl border border-blue-500 dark:border-blue-400"
                                >
                                  <FileQuestion className="h-4 w-4" />
                                  <span>Generate New Quiz</span>
                                </button>
                              </div>
                            </div>
                          </PopoverContent>
                        </Popover>
                      ) : (
                        <Button
                          onClick={() => setIsQuizModalOpen(true)}
                          variant="outline"
                          size="sm"
                          className="flex items-center space-x-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 border-blue-200 dark:border-blue-700 hover:border-blue-300 dark:hover:border-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                        >
                          <FileQuestion className="h-4 w-4" />
                          <span>Generate Quiz</span>
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Content Section - Using MultiStepExplain for proper rendering */}
                  <div className="flex-1 overflow-y-auto scrollbar-thin">
                    <MultiStepExplain
                      steps={steps}
                      initialStep={currentStep}
                      completedSteps={completedSteps}
                      onStepChange={handleStepChange}
                      isLoading={updateProgressMutation.isPending || trackEventMutation.isPending}
                      className="h-full"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Sidebar */}
          {/* <div className="lg:col-span-1">
            <div className="sticky h-full">
              AskQuestionsSidebar moved to global SidebarRight
            </div>
          </div> */}
        </div>
      </div>

      {/* Note Dialog */}
      {showNoteDialog && (
        <div className="fixed inset-0 bg-black/60 dark:bg-black/80 backdrop-blur-sm flex items-center justify-center z-50 transition-all duration-300">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 w-full max-w-lg mx-4 border-2 border-gray-200 dark:border-gray-600 shadow-2xl dark:shadow-black/40 transform transition-all duration-300 scale-100">
            <h3 className="text-xl font-bold mb-6 text-gray-900 dark:text-white bg-gradient-to-r from-purple-600 to-violet-600 bg-clip-text">
              {editingNoteIndex !== null ? "✏️ Edit Note" : "📝 Add Note"} for
              Step {currentStep + 1}
            </h3>
            <textarea
              value={noteContent}
              onChange={(e) => setNoteContent(e.target.value)}
              placeholder="Enter your note here... Share your thoughts, key insights, or questions about this step."
              className="w-full h-36 p-4 border-2 border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 dark:focus:border-purple-400 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 transition-all duration-200 font-medium"
            />
            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowNoteDialog(false);
                  setNoteContent("");
                  setEditingNoteIndex(null);
                }}
                className="px-6 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 font-medium border-2 border-gray-300 dark:border-gray-600 rounded-lg hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleSaveNote}
                disabled={!noteContent.trim()}
                className="px-6 py-3 bg-gradient-to-r from-purple-600 to-violet-600 hover:from-purple-700 hover:to-violet-700 disabled:from-gray-400 disabled:to-gray-500 text-white rounded-lg transition-all duration-200 disabled:cursor-not-allowed font-medium shadow-lg hover:shadow-xl border-2 border-purple-500 dark:border-purple-400 disabled:border-gray-400"
              >
                {editingNoteIndex !== null ? "💾 Update" : "💾 Save"} Note
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Quiz Selection Modal */}
      <QuizSelectionModal
        isOpen={isQuizModalOpen}
        onClose={() => setIsQuizModalOpen(false)}
        onGenerate={handleQuizGeneration}
        learningContentId={contentId}
        isGenerating={generateQuizMutation.isPending}
      />
      
      {/* Feedback Modal */}
      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={() => setIsFeedbackModalOpen(false)}
        contentId={contentId}
        contentTitle={title}
      />
    </div>
  );
}
