import * as React from "react"
import { Book<PERSON>, Bookmark<PERSON>heck, Trash2, ExternalLink } from "lucide-react"
import { <PERSON>ton } from "~/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card"
import { ScrollArea } from "~/components/ui/scroll-area"
import { Badge } from "~/components/ui/badge"

interface BookmarkItem {
  id: string
  title: string
  url: string
  description?: string
  tags: string[]
  createdAt: Date
}

interface BookmarkListViewProps {
  contentId: string
  className?: string
}

export function BookmarkListView({ contentId, className }: BookmarkListViewProps) {
  const [bookmarks, setBookmarks] = React.useState<BookmarkItem[]>([])
  const [isAdding, setIsAdding] = React.useState(false)
  const [newBookmark, setNewBookmark] = React.useState({
    title: "",
    url: "",
    description: "",
    tags: ""
  })

  // Load bookmarks from localStorage on mount
  React.useEffect(() => {
    const savedBookmarks = localStorage.getItem(`bookmarks-${contentId}`)
    if (savedBookmarks) {
      try {
        const parsedBookmarks = JSON.parse(savedBookmarks).map((bookmark: any) => ({
          ...bookmark,
          createdAt: new Date(bookmark.createdAt)
        }))
        setBookmarks(parsedBookmarks)
      } catch (error) {
        console.error('Failed to parse bookmarks:', error)
      }
    }
  }, [contentId])

  // Save bookmarks to localStorage whenever bookmarks change
  React.useEffect(() => {
    localStorage.setItem(`bookmarks-${contentId}`, JSON.stringify(bookmarks))
  }, [bookmarks, contentId])

  const handleAddBookmark = () => {
    if (!newBookmark.title.trim() || !newBookmark.url.trim()) return
    
    const bookmark: BookmarkItem = {
      id: Date.now().toString(),
      title: newBookmark.title.trim(),
      url: newBookmark.url.trim(),
      description: newBookmark.description.trim() || undefined,
      tags: newBookmark.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
      createdAt: new Date()
    }
    
    setBookmarks(prev => [bookmark, ...prev])
    setNewBookmark({ title: "", url: "", description: "", tags: "" })
    setIsAdding(false)
  }

  const handleDeleteBookmark = (id: string) => {
    setBookmarks(prev => prev.filter(bookmark => bookmark.id !== id))
  }

  const handleCancelAdd = () => {
    setIsAdding(false)
    setNewBookmark({ title: "", url: "", description: "", tags: "" })
  }

  const isValidUrl = (url: string) => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  }

  return (
    <div className={className}>
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Bookmarks</h3>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAdding(true)}
            disabled={isAdding}
          >
            <Bookmark className="h-4 w-4 mr-1" />
            Add Bookmark
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-4">
          {/* Add new bookmark form */}
          {isAdding && (
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Add New Bookmark</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <input
                    type="text"
                    placeholder="Bookmark title..."
                    value={newBookmark.title}
                    onChange={(e) => setNewBookmark(prev => ({ ...prev, title: e.target.value }))}
                    className="w-full px-3 py-2 text-sm border rounded-md bg-background"
                    autoFocus
                  />
                </div>
                <div>
                  <input
                    type="url"
                    placeholder="https://example.com"
                    value={newBookmark.url}
                    onChange={(e) => setNewBookmark(prev => ({ ...prev, url: e.target.value }))}
                    className="w-full px-3 py-2 text-sm border rounded-md bg-background"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="Description (optional)..."
                    value={newBookmark.description}
                    onChange={(e) => setNewBookmark(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-3 py-2 text-sm border rounded-md bg-background"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="Tags (comma separated)..."
                    value={newBookmark.tags}
                    onChange={(e) => setNewBookmark(prev => ({ ...prev, tags: e.target.value }))}
                    className="w-full px-3 py-2 text-sm border rounded-md bg-background"
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button variant="outline" size="sm" onClick={handleCancelAdd}>
                    Cancel
                  </Button>
                  <Button 
                    size="sm" 
                    onClick={handleAddBookmark}
                    disabled={!newBookmark.title.trim() || !newBookmark.url.trim()}
                  >
                    <BookmarkCheck className="h-4 w-4 mr-1" />
                    Save
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Bookmarks list */}
          {bookmarks.map((bookmark) => (
            <Card key={bookmark.id}>
              <CardHeader className="pb-2">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-sm font-medium flex items-center">
                      <Bookmark className="h-4 w-4 mr-2 text-blue-500" />
                      {bookmark.title}
                    </CardTitle>
                    {bookmark.description && (
                      <p className="text-xs text-muted-foreground mt-1">
                        {bookmark.description}
                      </p>
                    )}
                  </div>
                  <div className="flex space-x-1">
                    {isValidUrl(bookmark.url) && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(bookmark.url, '_blank')}
                        className="h-6 w-6 p-0"
                        title="Open link"
                      >
                        <ExternalLink className="h-3 w-3" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteBookmark(bookmark.id)}
                      className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                      title="Delete bookmark"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  <p className="text-xs text-muted-foreground break-all">
                    {bookmark.url}
                  </p>
                  {bookmark.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {bookmark.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  )}
                  <p className="text-xs text-muted-foreground">
                    Added {bookmark.createdAt.toLocaleDateString()}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}

          {bookmarks.length === 0 && !isAdding && (
            <div className="text-center py-8">
              <Bookmark className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <p className="text-muted-foreground text-sm">No bookmarks yet</p>
              <p className="text-muted-foreground text-xs mt-1">Save useful links for this learning content</p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}