'use client';

import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Send, MessageSquare, Bot, User, Loader2, AlertCircle, RotateCcw } from 'lucide-react';
// Note: tRPC replaced with placeholder - can be connected to chat API later
import { cn } from '~/lib/utils';
import { getEnvVar } from '~/lib/context.server';
import { useStreamingChat, type StreamingMessage } from '~/lib/hooks/use-streaming-chat';
import { TopicProposals } from '~/components/chat/TopicProposals';
import { useApiUtils } from '~/lib/hooks/use-api-utils';

interface ChatInterfaceProps {
  learningContentId: string;
  className?: string;
}

interface Message {
  id: string;
  content: string;
  senderRole: 'user' | 'assistant';
  createdAt: Date | string; // Can be string when coming from tRPC/JSON
  metadata?: {
    sources?: Array<{
      stepId: string;
      stepTitle: string;
      chunkIndex: number;
      score: number;
      contentId?: string;
      contentTitle?: string;
    }>;
    model?: string;
    processingTime?: number;
    proposedTopics?: string[];
  };
}

interface OptimisticMessage extends Omit<Message, 'createdAt'> {
  status: 'server' | 'optimistic-user' | 'streaming' | 'complete' | 'error';
  tempId?: string;
  createdAt: Date; // Always a Date object in processed messages
}

export function ChatInterface({ learningContentId, className }: ChatInterfaceProps) {
  const [message, setMessage] = useState('');
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [optimisticMessages, setOptimisticMessages] = useState<OptimisticMessage[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const utils = useApiUtils();

  // Real API implementation for chat functionality
  const getOrCreateConversationMutation = {
    mutate: async (params: { learningContentId: string }) => {
      try {
        const response = await fetch(`/api/chat?learningContentId=${params.learningContentId}`);
        const data = await response.json();
        
        if (data.success && data.conversations && data.conversations.length > 0) {
          // Use existing conversation
          setConversationId(data.conversations[0].id);
        } else {
          // Create new conversation
          const createResponse = await fetch('/api/conversations', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              action: 'create_conversation',
              learningContentId: params.learningContentId,
              title: 'Chat Session'
            })
          });
          const createData = await createResponse.json();
          if (createData.success) {
            setConversationId(createData.data.id);
          }
        }
      } catch (error) {
        console.error('Failed to get or create conversation:', error);
      }
    },
    isPending: false,
  };

  // Initialize conversation when component mounts
  const initializeConversation = useCallback(() => {
    if (learningContentId && !conversationId) {
      getOrCreateConversationMutation.mutate({ learningContentId });
    }
  }, [learningContentId, conversationId]);

  useEffect(() => {
    initializeConversation();
  }, [initializeConversation]);

  // Real conversation history
  const [historyData, setHistoryData] = useState<{ success: boolean; data: { messages: any[] } }>({ success: true, data: { messages: [] } });
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [historyError, setHistoryError] = useState<string | null>(null);
  
  const refetchHistory = useCallback(async () => {
    if (!conversationId) return;
    
    setIsLoadingHistory(true);
    try {
      const response = await fetch('/api/conversations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'get_messages',
          conversationId,
          limit: 50,
          offset: 0
        })
      });
      const data = await response.json();
      if (data.success) {
        setHistoryData({ success: true, data: { messages: data.data } });
        setHistoryError(null);
      } else {
        setHistoryError(data.error || 'Failed to load messages');
      }
    } catch (error) {
      setHistoryError('Failed to load conversation history');
      console.error('Failed to fetch history:', error);
    } finally {
      setIsLoadingHistory(false);
    }
  }, [conversationId]);
  
  // Load history when conversation ID changes
  useEffect(() => {
    if (conversationId) {
      refetchHistory();
    }
  }, [conversationId, refetchHistory]);

  // Streaming chat implementation
  const {
    sendStreamingMessage,
    isStreaming,
    error: streamingError,
    currentStreamingMessage,
    clearCurrentMessage,
  } = useStreamingChat({
    learningContentId,
    onMessageComplete: (message, userMessage) => {
      console.log('onMessageComplete called with message:', message);
      console.log('onMessageComplete called with userMessage:', userMessage);
      
      const messagesToAdd: Array<{
        id: string;
        content: string;
        senderRole: 'user' | 'assistant';
        createdAt: Date | string;
        messageType: 'text';
        metadata?: any;
      }> = [];
      
      // Add user message if provided
      if (userMessage) {
        messagesToAdd.push({
          id: userMessage.id,
          content: userMessage.content,
          senderRole: 'user',
          createdAt: userMessage.createdAt,
          messageType: 'text',
        });
      }
      
      // Add AI message
      messagesToAdd.push({
        id: message.id,
        content: message.content,
        senderRole: 'assistant',
        createdAt: message.createdAt,
        messageType: 'text',
        metadata: message.metadata,
      });
      
      console.log('messagesToAdd:', messagesToAdd);
      
      // Add both messages to history
      setHistoryData(prev => {
        const newData = {
          success: true,
          data: {
            messages: [...(prev.data?.messages || []), ...messagesToAdd]
          }
        };
        console.log('newData:', newData);
        return newData;
      });
      
      // Clear optimistic messages
       setOptimisticMessages([]);
       clearCurrentMessage();
       
       // Invalidate conversation memory to check for new topic proposals
       if (conversationId) {
         utils.conversations.getMemory.invalidate({ conversationId });
       }
       
       // Focus input after sending
       setTimeout(() => inputRef.current?.focus(), 100);
     },
     onError: (error) => {
      console.error('Streaming chat error:', error);
    },
  });

  // Legacy mutation interface for compatibility
  const sendMessageMutation = {
    mutate: (params: { conversationId: string; content: string }) => {
      sendMessageMutation.mutateAsync(params).catch(console.error);
    },
    mutateAsync: async (params: { conversationId: string; content: string; userMessage?: any }) => {
      if (!conversationId) {
        throw new Error('No conversation ID available');
      }
      return sendStreamingMessage(params.conversationId, params.content, params.userMessage);
    },
    isPending: isStreaming,
    error: streamingError,
  };

  // Combine server messages with optimistic messages and streaming message
  const allMessages: OptimisticMessage[] = useMemo(() => {
    const serverMessages: OptimisticMessage[] = historyData?.success
      ? historyData.data.messages.map((msg: Message) => ({
          ...msg,
          status: 'server' as const,
          // Ensure createdAt is a Date object (tRPC serializes dates as strings)
          createdAt: typeof msg.createdAt === 'string' ? new Date(msg.createdAt) : msg.createdAt,
        }))
      : [];

    // Include current streaming message if it exists
    const streamingMessages: OptimisticMessage[] = currentStreamingMessage ? [{
      ...currentStreamingMessage,
      status: currentStreamingMessage.status as OptimisticMessage['status'],
    }] : [];

    return [...serverMessages, ...optimisticMessages, ...streamingMessages].sort(
      (a, b) => {
        // Safely handle date comparison with fallback
        const aTime = a.createdAt instanceof Date ? a.createdAt.getTime() : new Date(a.createdAt).getTime();
        const bTime = b.createdAt instanceof Date ? b.createdAt.getTime() : new Date(b.createdAt).getTime();
        return aTime - bTime;
      }
    );
  }, [historyData, optimisticMessages, currentStreamingMessage]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [allMessages]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim() || !conversationId || sendMessageMutation.isPending) {
      return;
    }

    const userTempId = crypto.randomUUID();
    const now = new Date();
    const messageContent = message.trim();

    // Create user message data to pass to streaming hook
    const userMessageData = {
      id: userTempId,
      content: messageContent,
      senderRole: 'user' as const,
      createdAt: now,
      messageType: 'text' as const,
    };

    // Add optimistic user message immediately
    const optimisticUserMessage: OptimisticMessage = {
      ...userMessageData,
      status: 'optimistic-user',
      tempId: userTempId,
    };

    // Add user message to optimistic state
    setOptimisticMessages(prev => [...prev, optimisticUserMessage]);
    setMessage(''); // Clear input immediately

    try {
      // Pass user message data to the streaming hook
      await sendStreamingMessage(
        conversationId,
        messageContent,
        userMessageData
      );
      // Success is handled in the streaming chat hook's onMessageComplete
    } catch (error) {
      // Error handling is managed by the streaming chat hook
      console.error('Failed to send message:', error);
    }
  };

  const retryMessage = (aiTempId: string) => {
    // Find the user message that corresponds to this AI error
    const errorIndex = optimisticMessages.findIndex(msg => msg.tempId === aiTempId);
    if (errorIndex === -1) return;

    const userMessage = optimisticMessages[errorIndex - 1]; // User message should be right before AI message
    if (!userMessage || userMessage.senderRole !== 'user') return;

    // Remove the error message
    setOptimisticMessages(prev => prev.filter(msg => msg.tempId !== aiTempId));
    
    // Clear any current streaming message
    clearCurrentMessage();

    // Retry the API call
    if (!conversationId) return;

    const retryUserMessageData = {
      id: userMessage.id,
      content: userMessage.content,
      senderRole: 'user' as const,
      createdAt: userMessage.createdAt,
      messageType: 'text' as const,
    };

    sendStreamingMessage(
      conversationId,
      userMessage.content,
      retryUserMessageData
    ).catch((error) => {
      console.error('Retry failed:', error);
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e);
    }
  };

  if (getOrCreateConversationMutation.isPending) {
    return (
      <Card className={cn("h-full flex items-center justify-center", className)}>
        <CardContent className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-gray-400" />
          <p className="text-sm text-gray-500 dark:text-gray-400">Setting up chat...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("h-full flex flex-col", className)}>
      <CardHeader className="flex-shrink-0 pb-3">
        <div className="flex items-center gap-2 mb-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <CardTitle className="flex items-center text-lg">
            <MessageSquare className="h-5 w-5 mr-2 text-blue-500" />
            AI Assistant
          </CardTitle>
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Ask questions about this learning content
        </p>
        <div className="mt-2 px-2 py-1 bg-blue-50 dark:bg-blue-900/20 rounded-md">
          <p className="text-xs text-blue-700 dark:text-blue-300">
            💡 Context-aware responses using RAG search
          </p>
        </div>
      </CardHeader>

      <CardContent className="flex-1 flex flex-col min-h-0 p-4">
        {/* Messages Container */}
        <div className="flex-1 overflow-y-auto overflow-x-hidden mb-4 space-y-4 min-h-0">
          {isLoadingHistory ? (
            <div className="flex items-center justify-center py-8">
              <div className="flex flex-col items-center gap-3">
                <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                <p className="text-sm text-gray-500 dark:text-gray-400">Loading conversation history...</p>
              </div>
            </div>
          ) : allMessages.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <Bot className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-3" />
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Start a conversation
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400 max-w-xs mb-4">
                Ask any question about the learning material you&apos;re studying. I&apos;ll help you understand it better!
              </p>
              <div className="text-xs bg-gray-50 dark:bg-gray-800 rounded-lg p-3 max-w-sm">
                <p className="font-medium mb-2 text-gray-700 dark:text-gray-300">💡 Try asking:</p>
                <ul className="text-left space-y-1 text-gray-600 dark:text-gray-400">
                  <li>• "Explain the main concepts"</li>
                  <li>• "What are the key takeaways?"</li>
                  <li>• "Can you give me examples?"</li>
                </ul>
              </div>
            </div>
          ) : (
            <>
              {allMessages.map((msg) => (
                <div
                  key={msg.id}
                  className={cn(
                    "flex gap-3 w-full",
                    msg.senderRole === 'user' ? "justify-end" : "justify-start"
                  )}
                >
                  {/* Avatar */}
                  <div className={cn(
                    "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium",
                    msg.senderRole === 'user'
                      ? "bg-blue-500 order-2 mr-1"
                      : "bg-green-500 order-1"
                  )}>
                    {msg.senderRole === 'user' ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
                  </div>

                  {/* Message Bubble */}
                  <div className={cn(
                    "max-w-[340px] px-4 py-3 rounded-lg text-sm break-words",
                    msg.senderRole === 'user'
                      ? "bg-blue-500 text-white order-1"
                      : "bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 order-2",
                    msg.status === 'optimistic-user' && "opacity-90"
                  )}>
                    {/* Streaming state for AI */}
                    {msg.status === 'streaming' && (
                      <div className="space-y-2">
                        <div className="whitespace-pre-wrap break-words overflow-wrap-anywhere">
                          {msg.content}
                          <span className="inline-block w-2 h-4 bg-blue-500 animate-pulse ml-1"></span>
                        </div>
                      </div>
                    )}

                    {/* Error state for AI */}
                    {msg.status === 'error' && (
                      <div className="space-y-2">
                        <div className="flex items-center gap-2 text-red-500">
                          <AlertCircle className="h-4 w-4" />
                          <span>{msg.content}</span>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => msg.tempId && retryMessage(msg.tempId)}
                          className="text-xs"
                        >
                          <RotateCcw className="h-3 w-3 mr-1" />
                          Retry
                        </Button>
                      </div>
                    )}

                    {/* Normal message content */}
                    {(msg.status === 'server' || msg.status === 'optimistic-user' || msg.status === 'complete') && (
                      <div className="whitespace-pre-wrap break-words overflow-wrap-anywhere">
                        {msg.content}
                      </div>
                    )}

                    {/* Enhanced source information for server AI messages */}
                    {msg.senderRole === 'assistant' && (msg.status === 'server' || msg.status === 'complete') && msg.metadata?.sources && msg.metadata.sources.length > 0 && (
                      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <p className="text-xs font-medium text-gray-600 dark:text-gray-300">
                            Sources ({msg.metadata.sources.length})
                          </p>
                        </div>
                        <div className="space-y-2">
                          {msg.metadata.sources.slice(0, 3).map((source, index) => (
                            <div key={index} className="bg-gray-50 dark:bg-gray-800 rounded-md p-2 border border-gray-200 dark:border-gray-600">
                              <div className="flex items-start justify-between gap-2">
                                <div className="flex-1 min-w-0 overflow-hidden">
                                  <p className="text-xs font-medium text-gray-700 dark:text-gray-200 truncate">
                                    {source.stepTitle}
                                  </p>
                                  {source.contentTitle && (
                                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate">
                                      {source.contentTitle}
                                    </p>
                                  )}
                                </div>
                                {source.score && (
                                  <div className="flex-shrink-0">
                                    <span className="inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                      {Math.round(source.score * 100)}%
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                          {msg.metadata.sources.length > 3 && (
                            <p className="text-xs text-gray-500 dark:text-gray-400 italic">
                              +{msg.metadata.sources.length - 3} more sources
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
              
              {/* Processing indicator */}
              {sendMessageMutation.isPending && (
                <div className="flex items-start gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg max-w-[340px] ml-auto">
                  <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  </div>
                  <div className="flex-1 min-w-0 overflow-hidden">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-200 truncate">AI Assistant</span>
                      <span className="text-xs text-blue-600 dark:text-blue-400">thinking...</span>
                    </div>
                    <div className="flex items-center gap-1 text-xs text-gray-500 dark:text-gray-400">
                      <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                      <span className="ml-2 truncate">Searching knowledge base...</span>
                    </div>
                  </div>
                </div>
              )}

            </>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Topic Proposals */}
        {conversationId && (
          <TopicProposals 
            conversationId={conversationId}
            className="mb-4"
          />
        )}

        {/* Input Form */}
        <form onSubmit={handleSendMessage} className="flex-shrink-0">
          <div className="flex gap-2">
            <Input
              ref={inputRef}
              value={message}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="Ask a question about this learning material..."
              disabled={sendMessageMutation.isPending || !conversationId}
              className="flex-1"
              maxLength={2000}
            />
            <Button
              type="submit"
              disabled={!message.trim() || sendMessageMutation.isPending || !conversationId}
              size="icon"
              className="flex-shrink-0"
            >
              {sendMessageMutation.isPending ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </div>

          {/* Error messages */}
          {sendMessageMutation.error && (
            <div className="mt-2 flex items-center text-sm text-red-600 dark:text-red-400">
              <AlertCircle className="h-4 w-4 mr-1" />
              Failed to send message. Please try again.
            </div>
          )}
          {historyError && (
            <div className="mt-2 flex items-center text-sm text-red-600 dark:text-red-400">
              <AlertCircle className="h-4 w-4 mr-1" />
              Failed to load conversation history. Please refresh the page.
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
}