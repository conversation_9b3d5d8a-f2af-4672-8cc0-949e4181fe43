import * as React from "react"
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from "lucide-react"
import { SidebarRight, SidebarTab } from "~/components/sidebar-right"
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card"
import { <PERSON><PERSON> } from "~/components/ui/button"
import { Switch } from "~/components/ui/switch"

/**
 * Example usage of the reusable SidebarRight component.
 * This demonstrates how to create a custom sidebar with different content.
 */
export function SidebarRightExample() {
  const [isOpen, setIsOpen] = React.useState(false)
  const [isPinned, setIsPinned] = React.useState(false)

  const tabs: SidebarTab[] = [
    {
      id: "settings",
      label: "Settings",
      icon: Settings,
      content: (
        <Card className="h-full">
          <CardHeader>
            <CardTitle>Application Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span>Dark Mode</span>
              <Switch />
            </div>
            <div className="flex items-center justify-between">
              <span>Notifications</span>
              <Switch defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <span>Auto Save</span>
              <Switch defaultChecked />
            </div>
            <Button className="w-full mt-4">Save Settings</Button>
          </CardContent>
        </Card>
      )
    },
    {
      id: "users",
      label: "Users",
      icon: Users,
      content: (
        <Card className="h-full">
          <CardHeader>
            <CardTitle>Team Members</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {["Alice Johnson", "Bob Smith", "Carol Davis", "David Wilson"].map((name, i) => (
                <div key={i} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted">
                  <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-primary-foreground text-sm font-medium">
                    {name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <span className="text-sm">{name}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )
    },
    {
      id: "notifications",
      label: "Alerts",
      icon: Bell,
      content: (
        <Card className="h-full">
          <CardHeader>
            <CardTitle>Recent Notifications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { title: "New message received", time: "2 min ago", type: "info" },
                { title: "System update available", time: "1 hour ago", type: "warning" },
                { title: "Backup completed", time: "3 hours ago", type: "success" },
                { title: "Login from new device", time: "1 day ago", type: "alert" }
              ].map((notification, i) => (
                <div key={i} className="p-3 rounded-lg border">
                  <div className="font-medium text-sm">{notification.title}</div>
                  <div className="text-xs text-muted-foreground mt-1">{notification.time}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )
    }
  ]

  return (
    <>
      {/* Example trigger button */}
      <Button 
        onClick={() => setIsOpen(!isOpen)}
        variant="outline"
        className="mb-4"
      >
        {isOpen ? "Hide" : "Show"} Example Sidebar
      </Button>

      <SidebarRight
        isOpen={isOpen}
        isPinned={isPinned}
        title="Custom Sidebar"
        titleIcon={Settings}
        tabs={tabs}
        defaultTab="settings"
        onToggle={() => setIsOpen(!isOpen)}
        onTogglePin={() => setIsPinned(!isPinned)}
        width="20rem"
      />
    </>
  )
}