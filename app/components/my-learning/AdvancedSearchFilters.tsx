import { useState, useEffect } from 'react';
import {
  X,
  Clock,
  Tag,
  BookOpen,
  CheckCircle
} from 'lucide-react';
import { Button } from '~/components/ui/button';
import { Label } from '~/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '~/components/ui/select';
import { Checkbox } from '~/components/ui/checkbox';
import { Slider } from '~/components/ui/slider';
import type { MyLearningFilters } from '~/db/services/learning-content';
import { SearchFilterShell } from '~/components/shared/SearchFilterShell';
import { ActiveFiltersDisplay } from '~/components/shared/ActiveFiltersDisplay';
import { FilterField } from '~/components/shared/FilterField';

interface AdvancedSearchFiltersProps {
  filters: MyLearningFilters;
  onFiltersChange: (filters: MyLearningFilters) => void;
  className?: string;
}

export function AdvancedSearchFilters({
  filters,
  onFiltersChange,
  className = '',
}: AdvancedSearchFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [searchValue, setSearchValue] = useState(filters.search || '');
  const [readingTimeRange, setReadingTimeRange] = useState([
    filters.readingTimeRange?.min || 5,
    filters.readingTimeRange?.max || 120
  ]);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (searchValue !== filters.search) {
        onFiltersChange({ ...filters, search: searchValue || undefined });
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [searchValue, filters, onFiltersChange]);

  const handleFilterChange = (key: keyof MyLearningFilters, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
    });
  };

  const clearAllFilters = () => {
    setSearchValue('');
    setReadingTimeRange([5, 120]);
    onFiltersChange({
      limit: filters.limit,
      offset: filters.offset,
    });
  };

  const hasActiveFilters = () => {
    return !!(
      filters.search ||
      filters.learningLevel ||
      filters.contentType ||
      filters.completionStatus ||
      filters.isPublic !== undefined ||
      filters.tags?.length ||
      filters.dateRange ||
      filters.readingTimeRange
    );
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.learningLevel) count++;
    if (filters.contentType) count++;
    if (filters.completionStatus) count++;
    if (filters.isPublic !== undefined) count++;
    if (filters.tags?.length) count++;
    if (filters.dateRange) count++;
    if (filters.readingTimeRange) count++;
    return count;
  };

  return (
    <SearchFilterShell
      className={className}
      filters={filters}
      searchValue={searchValue}
      onSearchChange={setSearchValue}
      isExpanded={isExpanded}
      onExpandedChange={setIsExpanded}
      onSetFilter={(k, v) => handleFilterChange(k as keyof MyLearningFilters, v)}
      searchPlaceholder="Search learning content..."
      getActiveCount={getActiveFilterCount}
      renderAdvanced={({ filters }) => (
        <>
          {/* Learning Level */}
          <FilterField title="Learning Level" icon={BookOpen}>
            <Select
              value={filters.learningLevel || 'all-levels'}
              onValueChange={(value) =>
                handleFilterChange('learningLevel', value === 'all-levels' ? undefined : value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="All levels" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-levels">All levels</SelectItem>
                <SelectItem value="beginner">Beginner</SelectItem>
                <SelectItem value="intermediate">Intermediate</SelectItem>
                <SelectItem value="advanced">Advanced</SelectItem>
              </SelectContent>
            </Select>
          </FilterField>

          {/* Content Type */}
          <FilterField title="Content Type" icon={Tag}>
            <Select
              value={filters.contentType || 'all-types'}
              onValueChange={(value) =>
                handleFilterChange('contentType', value === 'all-types' ? undefined : value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-types">All types</SelectItem>
                <SelectItem value="standard">Standard</SelectItem>
                <SelectItem value="kwaci-primer">KWACI Primer</SelectItem>
              </SelectContent>
            </Select>
          </FilterField>

          {/* Completion Status */}
          <FilterField title="Completion Status" icon={CheckCircle}>
            <Select
              value={filters.completionStatus || 'all-statuses'}
              onValueChange={(value) =>
                handleFilterChange('completionStatus', value === 'all-statuses' ? undefined : value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-statuses">All statuses</SelectItem>
                <SelectItem value="not-started">Not Started</SelectItem>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </FilterField>

          {/* Visibility */}
          <FilterField title="Visibility">
            <Select
              value={filters.isPublic === true ? 'public' : filters.isPublic === false ? 'private' : 'all-visibility'}
              onValueChange={(value) => {
                if (value === 'all-visibility') {
                  handleFilterChange('isPublic', undefined);
                } else if (value === 'public') {
                  handleFilterChange('isPublic', true);
                } else if (value === 'private') {
                  handleFilterChange('isPublic', false);
                }
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="All content" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-visibility">All content</SelectItem>
                <SelectItem value="public">Public only</SelectItem>
                <SelectItem value="private">Private only</SelectItem>
              </SelectContent>
            </Select>
          </FilterField>

          {/* Reading Time Range */}
          <FilterField title="Reading Time (minutes)" icon={Clock} contentClassName="px-2" maxWidthClassName="max-w-none" className="md:col-span-2 lg:col-span-3 xl:col-span-4">
            <Slider
              value={readingTimeRange}
              onValueChange={setReadingTimeRange}
              onValueCommit={(value) =>
                handleFilterChange('readingTimeRange', {
                  min: value[0],
                  max: value[1]
                })
              }
              max={120}
              min={5}
              step={5}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-gray-500 mt-1">
              <span>{readingTimeRange[0]}m</span>
              <span>{readingTimeRange[1]}m</span>
            </div>
          </FilterField>

          {/* Sort Options */}
          <div className="grid grid-cols-2 gap-4">
            <FilterField title="Sort By">
              <Select
                value={filters.sortBy || 'updatedAt'}
                onValueChange={(value) =>
                  handleFilterChange('sortBy', value as any)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="updatedAt">Last Updated</SelectItem>
                  <SelectItem value="createdAt">Date Created</SelectItem>
                  <SelectItem value="title">Title</SelectItem>
                  <SelectItem value="progress">Progress</SelectItem>
                </SelectContent>
              </Select>
            </FilterField>

            <FilterField title="Order">
              <Select
                value={filters.sortOrder || 'desc'}
                onValueChange={(value) =>
                  handleFilterChange('sortOrder', value as 'asc' | 'desc')
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="desc">Descending</SelectItem>
                  <SelectItem value="asc">Ascending</SelectItem>
                </SelectContent>
              </Select>
            </FilterField>
          </div>

     
        </>
      )}
      renderActiveBadges={({ filters }) => {
        const badges = [] as Array<{ key: string; label: React.ReactNode; onRemove?: () => void; className?: string }>;
        if (filters.search) badges.push({ key: 'search', label: <>Search: "{filters.search}"</>, onRemove: () => handleFilterChange('search', undefined)});
        if (filters.learningLevel) badges.push({ key: 'level', label: <>Level: {filters.learningLevel}</>, onRemove: () => handleFilterChange('learningLevel', undefined) });
        if (filters.contentType) badges.push({ key: 'type', label: <>Type: {filters.contentType}</>, onRemove: () => handleFilterChange('contentType', undefined) });
        if (filters.completionStatus) badges.push({ key: 'status', label: <>Status: {filters.completionStatus}</>, onRemove: () => handleFilterChange('completionStatus', undefined) });
        if (filters.isPublic !== undefined) badges.push({ key: 'visibility', label: <>Visibility: {filters.isPublic ? 'Public' : 'Private'}</>, onRemove: () => handleFilterChange('isPublic', undefined) });
        if (filters.dateRange) badges.push({ key: 'date', label: <>Date range</>, onRemove: () => handleFilterChange('dateRange', undefined) });
        if (filters.readingTimeRange) badges.push({ key: 'rt', label: <>Reading time</>, onRemove: () => handleFilterChange('readingTimeRange', undefined) });
        return (
          <ActiveFiltersDisplay
            badges={badges}
            onClearAll={clearAllFilters}
          />
        );
      }}
    />
  );
}
