/**
 * Database connection retry utilities for Cloudflare Workers
 * 
 * Handles cold start issues where database connections aren't immediately ready
 */

/**
 * Retry configuration for database operations
 */
interface RetryConfig {
  maxAttempts: number;
  baseDelay: number; // milliseconds
  maxDelay: number; // milliseconds
  backoffMultiplier: number;
}

const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxAttempts: 3,
  baseDelay: 100, // Start with 100ms
  maxDelay: 2000, // Max 2 seconds
  backoffMultiplier: 2,
};

/**
 * Check if an error is a database connection error that should be retried
 */
function isDatabaseConnectionError(error: unknown): boolean {
  if (!(error instanceof Error)) return false;

  const message = error.message.toLowerCase();

  // Common database connection error patterns
  const connectionErrorPatterns = [
    'error code: 1042', // MySQL connection error
    'connection refused',
    'connection timeout',
    'connection reset',
    'connection lost',
    'connection closed',
    'connection failed',
    'network error',
    'timeout',
    'econnrefused',
    'econnreset',
    'etimedout',
    'socket hang up',
    'failed query',
    'database connection',
    'connection pool',
    // Supabase HTTP driver specific errors
    'supabase',
    'postgrest',
    'parameter binding',
    'invalid parameter',
  ];

  return connectionErrorPatterns.some(pattern => message.includes(pattern));
}

/**
 * Sanitize a parameter value for database queries in Cloudflare Workers + Supabase HTTP driver
 */
export function sanitizeDbParameter(value: string): string {
  if (typeof value !== 'string') {
    throw new Error('Parameter must be a string');
  }

  // Decode URL encoding and trim whitespace
  const decoded = decodeURIComponent(value).trim();

  // Log for debugging in development
  if (process.env.NODE_ENV !== 'production' && decoded !== value) {
    console.log('🔍 Parameter sanitized:', { original: value, sanitized: decoded });
  }

  return decoded;
}

/**
 * Sleep for a specified number of milliseconds
 */
function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Calculate delay for retry attempt with exponential backoff
 */
function calculateDelay(attempt: number, config: RetryConfig): number {
  const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt - 1);
  return Math.min(delay, config.maxDelay);
}

/**
 * Retry a database operation with exponential backoff
 * 
 * @param operation - The database operation to retry
 * @param config - Retry configuration (optional)
 * @returns Promise that resolves with the operation result
 */
export async function retryDatabaseOperation<T>(
  operation: () => Promise<T>,
  config: Partial<RetryConfig> = {}
): Promise<T> {
  const finalConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  let lastError: unknown;
  
  for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
    try {
      const result = await operation();
      
      // If we get here, the operation succeeded
      if (attempt > 1) {
        console.log(`✅ Database operation succeeded on attempt ${attempt}/${finalConfig.maxAttempts}`);
      }
      
      return result;
    } catch (error) {
      lastError = error;
      
      // If it's not a connection error, don't retry
      if (!isDatabaseConnectionError(error)) {
        console.log(`❌ Database operation failed with non-retryable error:`, error);
        throw error;
      }
      
      // If this was the last attempt, throw the error
      if (attempt === finalConfig.maxAttempts) {
        console.error(`❌ Database operation failed after ${finalConfig.maxAttempts} attempts:`, error);
        throw error;
      }
      
      // Calculate delay and wait before retrying
      const delay = calculateDelay(attempt, finalConfig);
      console.log(`⚠️  Database operation failed on attempt ${attempt}/${finalConfig.maxAttempts}, retrying in ${delay}ms:`, 
        error instanceof Error ? error.message : String(error));
      
      await sleep(delay);
    }
  }
  
  // This should never be reached, but just in case
  throw lastError;
}

/**
 * Wrapper for database queries that automatically retries on connection errors
 * 
 * Usage:
 * ```typescript
 * const result = await withDatabaseRetry(async () => {
 *   return await db.select().from(users).where(eq(users.id, userId));
 * });
 * ```
 */
export async function withDatabaseRetry<T>(
  operation: () => Promise<T>,
  config?: Partial<RetryConfig>
): Promise<T> {
  return retryDatabaseOperation(operation, config);
}

/**
 * Test database connection health
 */
export async function testDatabaseConnection(): Promise<boolean> {
  try {
    const { db } = await import('~/db');
    
    // Simple query to test connection
    await withDatabaseRetry(async () => {
      return await db.execute('SELECT 1 as test');
    }, { maxAttempts: 2, baseDelay: 50 });
    
    return true;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  }
}

/**
 * Warm up database connection (call this early in worker lifecycle)
 */
export async function warmupDatabaseConnection(): Promise<void> {
  try {
    console.log('🔥 Warming up database connection...');
    const isHealthy = await testDatabaseConnection();
    if (isHealthy) {
      console.log('✅ Database connection warmed up successfully');
    } else {
      console.warn('⚠️  Database connection warmup failed, but continuing...');
    }
  } catch (error) {
    console.warn('⚠️  Database connection warmup error:', error);
    // Don't throw - we want the worker to continue even if warmup fails
  }
}
