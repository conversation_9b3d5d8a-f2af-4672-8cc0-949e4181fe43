/**
 * Quiz Generation Service
 * Generates quiz questions from learning content using AI
 */

import { generateObject } from 'ai';
import { createAIProvider, selectOptimalModel, AI_CONFIG } from '../config/ai-config';
import { 
  aiQuizGenerationResponseSchema,
  quizQuestionSchema,
  type QuizType,
  type QuizDifficulty
} from '../schemas/quiz-generation';
import {
  QUIZ_GENERATION_SYSTEM_PROMPT,
  createQuizGenerationPromptWithQuotas
} from '../prompts/quiz-generation';
import type { LearningContent } from '~/db/schema/learning-content';
import type { QuizGenerationConfig } from '~/components/quiz/types';
import { log } from '~/lib/logger';

// Quiz generation options
interface QuizGenerationOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  validateContent?: boolean;
  retryOnFailure?: boolean;
  preferCostEffective?: boolean;
  env?: Record<string, any>;
}

// Generated quiz response interface
export interface GeneratedQuiz {
  id: string;
  title: string;
  description: string;
  learningContentId: string;
  questions: any[];
  estimatedDuration: number;
  totalPoints: number;
  difficulty: QuizDifficulty;
  metadata: {
    generatedAt: string;
    aiModel: string;
    sourceStepsUsed: string[];
    difficultyDistribution: Record<string, number>;
    typeDistribution: Record<string, number>;
  };
  isPublic: boolean;
  allowRetakes: boolean;
  showCorrectAnswers: boolean;
  shuffleQuestions: boolean;
  timeLimit?: number;
}

/**
 * Extract text content from a learning step block
 */
function extractTextFromBlock(block: any): string {
  const { type, data } = block;

  switch (type) {
    case 'paragraph':
      if (typeof data === 'string') {
        return data;
      } else if (Array.isArray(data)) {
        return data.join('\n');
      } else if (data && typeof data.text === 'string') {
        return data.text;
      } else if (data && Array.isArray(data.text)) {
        return data.text.join('\n');
      }
      return '';

    case 'infoBox':
      const infoData = data.title ? data : (data.data || data);
      const heading = infoData.title || infoData.heading ? `${infoData.title || infoData.heading}\n` : '';
      const content = infoData.content || '';
      const lines = Array.isArray(infoData.lines) ? infoData.lines.join('\n') : '';
      return heading + content + lines;

    case 'bulletList':
    case 'numberedList':
      if (Array.isArray(data)) {
        return data.join('\n');
      } else if (data && Array.isArray(data.items)) {
        return data.items.join('\n');
      }
      return '';

    case 'grid':
      const gridData = Array.isArray(data) ? data : (data.items || []);
      if (Array.isArray(gridData)) {
        return gridData.map((item: any) => `${item.title}: ${item.content}`).join('\n');
      }
      return '';

    case 'comparison':
      const comparisonData = Array.isArray(data) ? data : (data.items || []);
      if (Array.isArray(comparisonData)) {
        return comparisonData.map((item: any) =>
          `${item.label}: Before - ${item.before}, After - ${item.after}`
        ).join('\n');
      }
      return '';

    case 'table':
      const tableData = data.headers ? data : (data.data || data);
      if (tableData.headers && tableData.rows) {
        const headerRow = tableData.headers.join(' | ');
        const dataRows = tableData.rows.map((row: any[]) => row.join(' | ')).join('\n');
        return `${headerRow}\n${dataRows}`;
      }
      return '';

    case 'keyValueGrid':
      const kvData = Array.isArray(data) ? data : (data.items || []);
      if (Array.isArray(kvData)) {
        return kvData.map((item: any) => `${item.key}: ${item.value}`).join('\n');
      }
      return '';

    case 'scatterPlot':
      const plotData = data.data ? data : (data.config || data);
      if (plotData.data && Array.isArray(plotData.data)) {
        return plotData.data.map((point: any) =>
          `${point.label}: (${point.x}, ${point.y})`
        ).join('\n');
      }
      return '';

    default:
      return '';
  }
}

/**
 * Extract all text content from a learning step
 */
function extractStepContent(step: any): string {
  const title = step.title;
  const blockContents = step.blocks
    .map((block: any) => extractTextFromBlock(block))
    .filter((content: string) => content.trim().length > 0);

  return [title, ...blockContents].join('\n\n');
}

/**
 * Create question type quotas based on configuration
 */
function createQuestionTypeQuotas(quizTypes: QuizType[], questionsPerType: number): Map<QuizType, number> {
  const quotas = new Map<QuizType, number>();
  quizTypes.forEach(type => {
    quotas.set(type, questionsPerType);
  });
  return quotas;
}

/**
 * Distribute questions across content steps
 */
function distributeQuestionsAcrossSteps(
  totalQuestions: number,
  numberOfSteps: number,
  quizTypes: QuizType[],
  questionsPerType: number
): Array<{
  questionsToGenerate: number;
  typesToUse: QuizType[];
  typeQuotas: Map<QuizType, number>;
}> {
  const distribution = [];
  
  // Create global quotas for all question types
  const globalQuotas = createQuestionTypeQuotas(quizTypes, questionsPerType);
  
  // Distribute the global quotas across steps
  const remainingQuotas = new Map(globalQuotas);
  
  for (let i = 0; i < numberOfSteps; i++) {
    const stepQuotas = new Map<QuizType, number>();
    let stepQuestionCount = 0;
    
    // Distribute remaining quotas to this step
    for (const [type, remaining] of remainingQuotas) {
      if (remaining > 0) {
        // Calculate how many questions of this type to assign to this step
        const stepsLeft = numberOfSteps - i;
        const questionsForThisStep = Math.ceil(remaining / stepsLeft);
        
        stepQuotas.set(type, questionsForThisStep);
        stepQuestionCount += questionsForThisStep;
        
        // Update remaining quotas
        remainingQuotas.set(type, remaining - questionsForThisStep);
      } else {
        stepQuotas.set(type, 0);
      }
    }
    
    distribution.push({
      questionsToGenerate: stepQuestionCount,
      typesToUse: quizTypes.filter(type => stepQuotas.get(type)! > 0),
      typeQuotas: stepQuotas
    });
  }
  
  return distribution;
}

/**
 * Transform AI-generated question to match strict schema requirements
 */
function transformAIQuestionToStrict(rawQuestion: any, stepId: string, stepContent: string): any | null {
  try {
    const baseQuestion = {
      id: crypto.randomUUID(),
      type: rawQuestion.type,
      difficulty: rawQuestion.difficulty || 'medium',
      sourceStepId: stepId,
      sourceContent: stepContent.substring(0, 500),
      points: rawQuestion.points || 1,
    };

    // Transform based on question type
    switch (rawQuestion.type) {
      case 'flashcard':
        if (!rawQuestion.front || !rawQuestion.back) {
          log.warn('Flashcard question missing required fields');
          return null;
        }
        return {
          ...baseQuestion,
          type: 'flashcard',
          front: rawQuestion.front,
          back: rawQuestion.back,
          hint: rawQuestion.hint,
          correctFeedback: rawQuestion.correctFeedback,
          incorrectFeedback: rawQuestion.incorrectFeedback,
        };

      case 'multipleChoice':
        if (!rawQuestion.question || !rawQuestion.options || rawQuestion.options.length !== 4 || rawQuestion.correctAnswerIndex === undefined) {
          log.warn('Multiple choice question missing required fields');
          return null;
        }
        return {
          ...baseQuestion,
          type: 'multipleChoice',
          question: rawQuestion.question,
          options: rawQuestion.options,
          correctAnswerIndex: rawQuestion.correctAnswerIndex,
          explanation: rawQuestion.explanation,
          hint: rawQuestion.hint,
          correctFeedback: rawQuestion.correctFeedback,
          incorrectFeedback: rawQuestion.incorrectFeedback,
        };

      case 'trueFalse':
        if (!rawQuestion.statement || rawQuestion.correctAnswer === undefined) {
          log.warn('True/false question missing required fields');
          return null;
        }
        return {
          ...baseQuestion,
          type: 'trueFalse',
          statement: rawQuestion.statement,
          correctAnswer: rawQuestion.correctAnswer,
          explanation: rawQuestion.explanation,
          hint: rawQuestion.hint,
          correctFeedback: rawQuestion.correctFeedback,
          incorrectFeedback: rawQuestion.incorrectFeedback,
        };

      case 'fillInBlank':
        let blanks = rawQuestion.blanks;
        if (!rawQuestion.text || !blanks || blanks.length === 0) {
          log.warn('Fill in blank question missing required fields');
          return null;
        }
        return {
          ...baseQuestion,
          type: 'fillInBlank',
          text: rawQuestion.text,
          blanks: blanks,
          hint: rawQuestion.hint,
          correctFeedback: rawQuestion.correctFeedback,
          incorrectFeedback: rawQuestion.incorrectFeedback,
        };

      case 'matching':
        let pairs = rawQuestion.pairs;
        if (!pairs || pairs.length < 3) {
          log.warn('Matching question missing required pairs');
          return null;
        }
        return {
          ...baseQuestion,
          type: 'matching',
          instruction: rawQuestion.instruction || 'Match the following items:',
          pairs: pairs,
          hint: rawQuestion.hint,
          correctFeedback: rawQuestion.correctFeedback,
          incorrectFeedback: rawQuestion.incorrectFeedback,
        };

      case 'freeText':
        const question = rawQuestion.question;
        const answerType = rawQuestion.answerType || 'short';
        const maxLength = rawQuestion.maxLength || (answerType === 'short' ? 200 : 500);
        const sampleAnswer = rawQuestion.sampleAnswer || 'A comprehensive answer based on the provided content.';
        let evaluationCriteria = rawQuestion.evaluationCriteria || ['Demonstrates understanding of key concepts'];

        if (!question) {
          log.warn('Free text question missing required question field');
          return null;
        }

        return {
          ...baseQuestion,
          type: 'freeText',
          question: question,
          answerType: answerType,
          maxLength: maxLength,
          sampleAnswer: sampleAnswer,
          evaluationCriteria: evaluationCriteria,
          hint: rawQuestion.hint,
          correctFeedback: rawQuestion.correctFeedback,
          incorrectFeedback: rawQuestion.incorrectFeedback,
        };

      case 'ordering':
        if (!rawQuestion.items || !rawQuestion.correctOrder || rawQuestion.items.length < 3) {
          log.warn('Ordering question missing required fields');
          return null;
        }
        return {
          ...baseQuestion,
          type: 'ordering',
          instruction: rawQuestion.instruction || 'Arrange the following items in the correct order:',
          items: rawQuestion.items,
          correctOrder: rawQuestion.correctOrder,
          orderType: rawQuestion.orderType || 'logical',
          hint: rawQuestion.hint,
          correctFeedback: rawQuestion.correctFeedback,
          incorrectFeedback: rawQuestion.incorrectFeedback,
        };

      default:
        log.warn('Unknown question type', { type: rawQuestion.type });
        return null;
    }
  } catch (error) {
    log.warn('Failed to transform AI question', { error: error instanceof Error ? error.message : String(error) });
    return null;
  }
}

/**
 * Generate questions for a specific step
 */
async function generateQuestionsForStep(
  step: any,
  typeQuotas: Map<QuizType, number>,
  difficulty: QuizDifficulty,
  options: QuizGenerationOptions,
  includeHints: boolean = true,
  includeExplanations: boolean = true
): Promise<any[]> {
  const stepContent = extractStepContent(step);
  if (stepContent.trim().length < 50) {
    log.warn('Step has insufficient content for quiz generation', { stepId: step.id });
    return [];
  }

  const openrouter = createAIProvider(options.env);
  const primaryModel = options.model || selectOptimalModel(['paragraph'], 'intermediate', options.preferCostEffective);

  const userPrompt = createQuizGenerationPromptWithQuotas(
    stepContent,
    step.title,
    step.id,
    typeQuotas,
    difficulty,
    includeHints,
    includeExplanations
  );

  try {
    log.info('Generating questions for step', { stepId: step.id, model: primaryModel });
    const currentModel = openrouter(primaryModel);

    const result = await generateObject({
      model: currentModel,
      schema: aiQuizGenerationResponseSchema,
      system: QUIZ_GENERATION_SYSTEM_PROMPT,
      prompt: userPrompt,
      temperature: options.temperature || 0.9, // Higher temperature for better hint generation
      maxTokens: options.maxTokens || 4000,
    });

    const aiResponse = result.object;
    log.info('Generated questions for step', { stepId: step.id, questionCount: aiResponse.questions?.length || 0 });

    // Log raw AI response to debug feedback fields
    console.info('Raw AI response for step:', {
      stepId: step.id,
      questionsCount: aiResponse.questions?.length || 0,
      rawQuestions: aiResponse.questions?.map((q, index) => ({
        index: index + 1,
        type: q.type,
        hasCorrectFeedback: !!q.correctFeedback,
        hasIncorrectFeedback: !!q.incorrectFeedback,
        hasHint: !!q.hint,
        hint: q.hint,
        correctFeedback: q.correctFeedback,
        incorrectFeedback: q.incorrectFeedback,
        question: q.question || q.front || 'N/A'
      }))
    });

    // Transform and validate each question
    const validQuestions = [];
    for (const rawQuestion of aiResponse.questions || []) {
      console.info('Transforming raw question:', {
        stepId: step.id,
        type: rawQuestion.type,
        beforeTransform: {
          hasCorrectFeedback: !!rawQuestion.correctFeedback,
          hasIncorrectFeedback: !!rawQuestion.incorrectFeedback,
          correctFeedback: rawQuestion.correctFeedback,
          incorrectFeedback: rawQuestion.incorrectFeedback
        }
      });
      
      const transformedQuestion = transformAIQuestionToStrict(rawQuestion, step.id, stepContent);
      
      if (transformedQuestion) {
        console.info('After transformation:', {
          stepId: step.id,
          type: transformedQuestion.type,
          afterTransform: {
            hasCorrectFeedback: !!transformedQuestion.correctFeedback,
            hasIncorrectFeedback: !!transformedQuestion.incorrectFeedback,
            correctFeedback: transformedQuestion.correctFeedback,
            incorrectFeedback: transformedQuestion.incorrectFeedback
          }
        });
        validQuestions.push(transformedQuestion);
      }
    }

    log.info('Valid questions after transformation', { stepId: step.id, validQuestionCount: validQuestions.length });
    return validQuestions;

  } catch (error) {
    log.error('Question generation failed for step', { stepId: step.id, error: error instanceof Error ? error.message : String(error) });
    return [];
  }
}

/**
 * Main function to generate quiz from learning content
 */
export async function generateQuizFromContent(
  learningContent: LearningContent,
  config: QuizGenerationConfig,
  options: QuizGenerationOptions = {}
): Promise<GeneratedQuiz> {
  if (learningContent.steps.length === 0) {
    throw new Error('Learning content must have at least one step');
  }

  const totalTargetQuestions = config.quizTypes.length * config.questionsPerType;
  log.info('Quiz generation strategy', {
    totalSteps: learningContent.steps.length,
    targetQuestions: totalTargetQuestions,
    quizTypes: config.quizTypes,
    questionsPerType: config.questionsPerType
  });

  // Distribute questions across steps
  const questionDistribution = distributeQuestionsAcrossSteps(
    totalTargetQuestions,
    learningContent.steps.length,
    config.quizTypes,
    config.questionsPerType
  );

  const allQuestions: any[] = [];
  const sourceStepsUsed: string[] = [];

  // Generate questions for each step
  for (let i = 0; i < learningContent.steps.length; i++) {
    const step = learningContent.steps[i];
    const distribution = questionDistribution[i];

    if (distribution.questionsToGenerate === 0) {
      log.info('Skipping step - no questions allocated', { stepId: step.id });
      continue;
    }

    const stepQuestions = await generateQuestionsForStep(
      step,
      distribution.typeQuotas,
      config.difficulty,
      options,
      config.includeHints,
      config.includeExplanations
    );

    if (stepQuestions.length > 0) {
      allQuestions.push(...stepQuestions);
      sourceStepsUsed.push(step.id);
    }
  }

  // Calculate difficulty distribution
  const difficultyDistribution: Record<string, number> = {};
  const typeDistribution: Record<string, number> = {};

  allQuestions.forEach(q => {
    difficultyDistribution[q.difficulty] = (difficultyDistribution[q.difficulty] || 0) + 1;
    typeDistribution[q.type] = (typeDistribution[q.type] || 0) + 1;
  });

  // Calculate estimated duration (1.5 minutes per question on average)
  const estimatedDuration = Math.ceil(allQuestions.length * 1.5);
  const totalPoints = allQuestions.reduce((sum, q) => sum + (q.points || 1), 0);

  const generatedQuiz: GeneratedQuiz = {
    id: crypto.randomUUID(),
    title: `Quiz: ${learningContent.title}`,
    description: `Generated quiz based on: ${learningContent.description || learningContent.title}`,
    learningContentId: learningContent.id,
    questions: allQuestions,
    estimatedDuration,
    totalPoints,
    difficulty: config.difficulty,
    metadata: {
      generatedAt: new Date().toISOString(),
      aiModel: options.model || selectOptimalModel(['paragraph'], 'intermediate', options.preferCostEffective),
      sourceStepsUsed,
      difficultyDistribution,
      typeDistribution,
    },
    isPublic: false,
    allowRetakes: true,
    showCorrectAnswers: config.includeExplanations,
    shuffleQuestions: config.shuffleQuestions,
    timeLimit: config.timeLimit,
  };

  log.success('Quiz generation completed', {
    questionsGenerated: allQuestions.length,
    targetQuestions: totalTargetQuestions,
    estimatedDuration,
    totalPoints,
    typeDistribution,
    difficultyDistribution
  });

  return generatedQuiz;
}