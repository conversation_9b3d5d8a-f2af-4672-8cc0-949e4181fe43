/**
 * AI Related Content Generation Service
 * 
 * Generates related learning content that correlates with and expands understanding of a reference topic.
 * 
 * KEY CONCEPTS:
 * - 'Similar' means 'related/correlated' - content that helps explain, build upon, or provide deeper insight
 * - Examples: "Human Anatomy" → "How Human Eyes Work", "Programming Basics" → "Object-Oriented Principles"
 * - Implements level progression: beginner → intermediate → advanced (stays at advanced)
 * - Focuses on connected concepts that enhance comprehension of the original topic
 * 
 * LEVEL PROGRESSION:
 * - Beginner reference → Intermediate related content
 * - Intermediate reference → Advanced related content  
 * - Advanced reference → Advanced related content (maintains expert level)
 * 
 * This approach expands learning by providing correlated knowledge rather than repetitive content.
 */

import { generateObject } from "ai";
import {
  createAIProvider,
  selectOptimalModel,
  validateAIConfig,
  AI_CONFIG,
} from "../config/ai-config";
import {
  generatedLearningContentSchema,
  validateStepDataWithDetails,
  transformAIGeneratedData,
  type GeneratedLearningContent,
} from "../schemas/content-generation";
import {
  generatedKWACIPrimerSchema,
  aiKWACIPrimerResponseSchema,
  transformAIResponseToKWACIPrimer,
} from "../schemas/kwaci-primer";
import {
  KWACI_PRIMER_SYSTEM_PROMPT,
  createKWACIPrimerUserPrompt,
} from "../prompts/kwaci-primer";
import type { LearningContent } from "~/db/schema/learning-content";

export interface SimilarGenerationOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  retryOnFailure?: boolean;
  preferCostEffective?: boolean;
  env?: Record<string, any>;
}

export type SimilarGenerationResult =
  | ({ kind: "standard" } & GeneratedLearningContent)
  | ({
      kind: "kwaci-primer";
    } & import("../schemas/kwaci-primer").GeneratedKWACIPrimer);

/**
 * Determine the target learning level for similar content generation
 * Implements level progression to expand learning
 */
function getTargetLevel(currentLevel: string): string {
  const levelProgression = {
    'beginner': 'intermediate',
    'intermediate': 'advanced', 
    'advanced': 'advanced' // Stay at advanced for expert content
  };
  
  return levelProgression[currentLevel as keyof typeof levelProgression] || currentLevel;
}

/**
 * Extract domain and core concepts from reference content to identify related topics
 */
function extractDomainAndConcepts(reference: LearningContent) {
  const topic = reference.title.toLowerCase();
  const description = reference.description.toLowerCase();
  const content = [topic, description].join(' ');
  
  // Extract key domain indicators and concepts
  const domainKeywords = [];
  const concepts = [];
  
  // Simple keyword extraction for domain identification
  const words = content.split(/\s+/).filter(word => word.length > 3);
  
  // Identify potential domain areas
  const domains = {
    biology: ['anatomy', 'biology', 'human', 'body', 'organ', 'cell', 'tissue', 'system'],
    technology: ['programming', 'software', 'computer', 'algorithm', 'code', 'development'],
    science: ['physics', 'chemistry', 'science', 'theory', 'experiment', 'research'],
    business: ['business', 'management', 'strategy', 'marketing', 'finance', 'economics'],
    mathematics: ['math', 'mathematics', 'equation', 'formula', 'calculation', 'number']
  };
  
  for (const [domain, keywords] of Object.entries(domains)) {
    if (keywords.some(keyword => content.includes(keyword))) {
      domainKeywords.push(domain);
    }
  }
  
  return {
    primaryDomain: domainKeywords[0] || 'general',
    detectedConcepts: words.slice(0, 10), // First 10 significant words
    originalTopic: reference.title
  };
}

/**
 * Extract key characteristics from reference content with enhanced domain analysis
 */
function extractCharacteristics(reference: LearningContent) {
  // Derive topic from title; subject/context from description; structure from steps
  const topic = reference.title;
  const subject = reference.description;
  const level = reference.learningLevel;
  const contentType = reference.contentType;
  const targetLevel = getTargetLevel(level);
  const domainInfo = extractDomainAndConcepts(reference);

  // Collect block types used across steps
  const blockTypes = new Set<string>();
  for (const step of reference.steps as any[]) {
    for (const block of step.blocks || []) {
      if (block && block.type) blockTypes.add(block.type);
    }
  }

  // Heuristic: infer learning objectives from step titles
  const learningObjectives = (reference.steps as any[])
    .map((s) => (s?.title || "").trim())
    .filter(Boolean)
    .slice(0, 6);

  // Aggregate textual content for duplication checks
  const sourceText = [reference.title, reference.description]
    .concat(
      (reference.steps as any[]).flatMap((s) => {
        const texts: string[] = [s?.title || ""];
        for (const b of s?.blocks || []) {
          if (typeof b?.data === "string") texts.push(b.data);
          else if (Array.isArray(b?.data)) texts.push(b.data.join(" "));
          else if (b?.data && typeof b.data === "object") {
            try {
              texts.push(JSON.stringify(b.data));
            } catch {}
          }
        }
        return texts;
      })
    )
    .join(" \n ")
    .slice(0, 8000);

  return {
    topic,
    subject,
    level,
    targetLevel,
    contentType,
    domainInfo,
    preferredContentTypes: Array.from(blockTypes),
    learningObjectives,
    sourceText,
  };
}

/**
 * Generate related topic suggestions based on domain and concepts
 */
function generateRelatedTopicSuggestions(domainInfo: any, originalTopic: string): string {
  const { primaryDomain, detectedConcepts } = domainInfo;
  
  const relatedTopicExamples = {
    biology: [
      "If about human anatomy, explore specific organ functions (heart, brain, eyes, lungs)",
      "If about body systems, focus on cellular processes or molecular mechanisms", 
      "If about general biology, dive into specific biological processes or adaptations"
    ],
    technology: [
      "If about programming concepts, explore specific implementation patterns or frameworks",
      "If about algorithms, focus on real-world applications or optimization techniques",
      "If about software development, examine specific tools, methodologies, or case studies"
    ],
    science: [
      "If about physics principles, explore practical applications or related phenomena",
      "If about chemistry, focus on specific reactions, compounds, or industrial processes",
      "If about scientific methods, examine specific research techniques or case studies"
    ],
    business: [
      "If about general business, focus on specific industries, strategies, or case studies",
      "If about management, explore specific leadership styles, team dynamics, or organizational behavior",
      "If about marketing, examine specific channels, campaigns, or consumer psychology"
    ],
    mathematics: [
      "If about mathematical concepts, explore practical applications or related theorems",
      "If about equations, focus on specific problem-solving techniques or real-world uses",
      "If about statistics, examine specific analysis methods or case studies"
    ]
  };
  
  const suggestions = relatedTopicExamples[primaryDomain as keyof typeof relatedTopicExamples] || [
    "Explore specific applications, case studies, or deeper aspects of the topic",
    "Focus on practical implementations or real-world examples",
    "Examine related concepts that build upon or complement the original topic"
  ];
  
  return suggestions.join("\n- ");
}

/**
 * Create system and user prompts for generating related, correlated content that expands learning
 */
function createPrompts(reference: LearningContent) {
  const {
    topic,
    subject,
    level,
    targetLevel,
    contentType,
    domainInfo,
    preferredContentTypes,
    learningObjectives,
    sourceText,
  } = extractCharacteristics(reference);

  // For KWACI Primer, use specialized KWACI prompts with enhanced similarity logic
  if (contentType === "kwaci-primer") {
    const relatedTopicSuggestions = generateRelatedTopicSuggestions(domainInfo, topic);
    
    const system =
      KWACI_PRIMER_SYSTEM_PROMPT +
      `

IMPORTANT: You are generating RELATED content that correlates with and expands understanding of an existing KWACI primer.

Your task is to create content that:
- Is RELATED to but NOT the same as "${topic}" - think of it as exploring a connected concept that helps explain or build upon the original
- Advances the learning level from ${level} to ${targetLevel} to expand the learner's understanding
- Provides correlated knowledge that enhances comprehension of the original topic
- Follows the exact KWACI 9-section structure

EXAMPLES of "related/correlated" content:
- Original: "Human Anatomy" → Related: "How Human Eyes Work" or "Cardiovascular System Function"
- Original: "Programming Basics" → Related: "Object-Oriented Programming Principles" or "Algorithm Design Patterns"
- Original: "Business Strategy" → Related: "Market Analysis Techniques" or "Competitive Intelligence"

Domain-specific suggestions for ${domainInfo.primaryDomain}:
- ${relatedTopicSuggestions}

Reference context (to understand the foundation, NOT to duplicate):
${sourceText.slice(0, 4000)}`;

    const user = createKWACIPrimerUserPrompt(
      `Related topic that correlates with and expands upon: ${topic}`,
      targetLevel as any,
      `Generate related content at ${targetLevel} level that correlates with and builds understanding of the reference topic. Focus on connected concepts that enhance learning.`
    );

    return { system, user, level: targetLevel, contentType };
  }

  // For standard content, use enhanced similarity logic with level progression
  const relatedTopicSuggestions = generateRelatedTopicSuggestions(domainInfo, topic);
  
  const system = `You are an expert instructional designer specializing in creating related, correlated learning content.
Your task is to generate learning content that is RELATED to and CORRELATES with a reference topic, expanding the learner's understanding through connected concepts.

CORE PRINCIPLES:
- Generate content that is RELATED to but NOT the same as the reference topic
- Think of "similar" as "correlated" - content that helps explain, build upon, or provide deeper insight into the original concept
- Advance the learning level from ${level} to ${targetLevel} to expand understanding
- Focus on connected concepts that enhance comprehension of the original topic

EXAMPLES of "related/correlated" content:
- Original: "Human Anatomy" → Related: "How Human Eyes Work" or "Cardiovascular System Function"
- Original: "Programming Basics" → Related: "Object-Oriented Programming Principles" or "Algorithm Design Patterns"  
- Original: "Business Strategy" → Related: "Market Analysis Techniques" or "Competitive Intelligence"
- Original: "Basic Mathematics" → Related: "Mathematical Problem-Solving Techniques" or "Real-World Math Applications"

Domain-specific guidance for ${domainInfo.primaryDomain}:
- ${relatedTopicSuggestions}

Guidelines:
- Target difficulty level: ${targetLevel} (progressed from ${level})
- Do NOT repeat exact sentences, bullet items, or code snippets from the reference
- Focus on correlated concepts that build understanding of the original topic
- Prefer content types different from the reference where reasonable while preserving overall structure and coherence
- Keep explanations accurate and concrete; add clarifying context where helpful
- Ensure all structured fields match the output schema strictly

## CRITICAL: Data Structure Examples
For each content type, the "data" field must follow these EXACT formats:

**paragraph**: Use string or array of strings directly (NOT objects):
✅ Correct: "This is a paragraph"
✅ Correct: ["First paragraph", "Second paragraph"]
❌ Wrong: {"content": "text"} or {"text": "text"}

**infoBox**: Use object with heading and lines:
✅ Correct: {"heading": "Important", "lines": ["Point 1", "Point 2"]}

**bulletList**: Use array of strings directly:
✅ Correct: ["Item 1", "Item 2", "Item 3"]
❌ Wrong: {"items": ["Item 1", "Item 2"]}

**numberedList**: Use array of strings directly:
✅ Correct: ["Step 1", "Step 2", "Step 3"]

**grid**: Use array of objects with title and content:
✅ Correct: [{"title": "Title 1", "content": "Description 1"}, {"title": "Title 2", "content": "Description 2"}]
❌ Wrong: {"title": "Title", "content": "Content"} or {"items": [...]}

**comparison**: Use array of objects with label, before, after:
✅ Correct: [{"label": "Feature", "before": "Old way", "after": "New way"}]

**table**: Use object with headers and rows:
✅ Correct: {"headers": ["Col1", "Col2"], "rows": [["A", "B"], ["C", "D"]]}

**scatterPlot**: Use object with data array and axis labels:
✅ Correct: {"data": [{"x": 10, "y": 20, "label": "Point 1"}], "xLabel": "Time", "yLabel": "Value", "title": "Chart Title"}

**keyValueGrid**: Use array of objects with key and value:
✅ Correct: [{"key": "Term", "value": "Definition"}]`;

  const desiredTypesHint = preferredContentTypes.length
    ? `The reference used these content block types: ${preferredContentTypes.join(", ")}.
Where reasonable, vary the block types to include alternatives (e.g., if reference had bulletList, consider grid or comparison).`
    : "";

  const objectivesText = learningObjectives.length
    ? `Reference learning objectives/step headings include: ${learningObjectives.join("; ")}.`
    : "";

  const user = `Generate RELATED learning content that correlates with and expands understanding of: "${topic}".
Target audience level: ${targetLevel} (progressed from ${level}).
Content format: ${contentType}.
${desiredTypesHint}
${objectivesText}

Your task:
- Create content about a RELATED topic that helps explain, build upon, or provide deeper insight into the original concept
- Think "correlated knowledge" - what would help someone understand the original topic better?
- Progress the difficulty from ${level} to ${targetLevel} to expand learning
- Focus on connected concepts rather than the exact same topic

Reference context (to understand the foundation and identify related concepts, NOT to duplicate):
--- BEGIN REFERENCE CONTEXT ---
Original Topic: ${topic}
Description: ${subject}
Detected Domain: ${domainInfo.primaryDomain}
Key Concepts: ${domainInfo.detectedConcepts.join(', ')}

Content Details:
${sourceText}
--- END REFERENCE CONTEXT ---

Requirements:
- Generate content about a RELATED topic that correlates with and enhances understanding of the original
- Target ${targetLevel} difficulty level to expand the learner's knowledge
- Focus on connected concepts, applications, or deeper aspects that build upon the foundation
- Add complementary knowledge without repeating the original content
- Output must strictly match the required JSON schema for ${contentType}.

Examples of good "related" topics:
- If original is about "Human Anatomy", generate content about "How Specific Organs Function" or "Body System Interactions"
- If original is about "Programming Basics", generate content about "Code Organization Principles" or "Debugging Strategies"
- If original is about "${topic}", consider: ${relatedTopicSuggestions.split('\n- ')[0]}`;

  return { system, user, level: targetLevel, contentType };
}

/**
 * Basic duplication check using token overlap ratio (very lightweight)
 */
function similarityRatio(a: string, b: string) {
  const A = new Set(a.toLowerCase().split(/\s+/).filter(Boolean));
  const B = new Set(b.toLowerCase().split(/\s+/).filter(Boolean));
  const inter = [...A].filter((w) => B.has(w)).length;
  const union = new Set([...A, ...B]).size || 1;
  return inter / union;
}

/**
 * Enhance and validate generated content; ensure it's related but not duplicative
 */
function validateAndEnhance(
  generated: any,
  referenceText: string,
  model: string,
  contentType: "standard" | "kwaci-primer",
  reference: LearningContent,
  targetLevel: string
) {
  const now = new Date().toISOString();

  if (contentType === "standard") {
    // Validate each step's data shape
    for (const step of generated.steps) {
      const transformed = transformAIGeneratedData(step.type, step.data);
      const { isValid, error } = validateStepDataWithDetails(
        step.type,
        transformed
      );
      if (!isValid) {
        throw new Error(
          `Invalid step data for type ${step.type}: ${error || "unknown error"}`
        );
      }
      step.data = transformed;
    }

    const flatText = [generated.title, generated.description]
      .concat(
        generated.steps.flatMap((s: any) => [
          s.title,
          typeof s.data === "string" ? s.data : JSON.stringify(s.data),
        ])
      )
      .join(" ")
      .slice(0, 8000);

    // For related content, we expect some correlation but not exact duplication
    // Adjust similarity threshold - we want related content, not completely different content
    const sim = similarityRatio(referenceText || "", flatText);
    if (sim > 0.8) {
      // Too similar - likely duplicating rather than creating related content
      throw new Error(
        "Generated content is too similar to the reference. Please generate more distinctly related content."
      );
    }
    
    // Also check if content is too different (no correlation)
    if (sim < 0.1) {
      console.warn("Generated content may be too unrelated to the reference topic.");
    }

    // Ensure metadata exists with basic AI generation info
    generated.metadata = {
      ...(generated.metadata || {}),
      aiModel: model,
      generatedAt: now
    };
    
    // Update the learning level to the target level (ensure proper typing)
    generated.learningLevel = targetLevel as "beginner" | "intermediate" | "advanced";

    return { kind: "standard", ...generated } as SimilarGenerationResult;
  }

  // KWACI Primer content: use proper transformation with target level
  const kwaciInput = {
    topic: `Related topic that correlates with: ${reference.title}`,
    learningLevel: targetLevel as "beginner" | "intermediate" | "advanced",
    focusAreas: "Related content that expands understanding of the original topic",
  };

  const transformedKWACI = transformAIResponseToKWACIPrimer(
    generated as any,
    kwaciInput,
    model
  );
  
  // The transformAIResponseToKWACIPrimer already sets proper metadata
  // No need to add custom properties that don't exist in the schema

  return {
    kind: "kwaci-primer",
    ...transformedKWACI,
  } as SimilarGenerationResult;
}

/**
 * Generate similar learning content from a reference item
 */
export async function generateSimilarLearningContent(
  reference: LearningContent,
  options: SimilarGenerationOptions = {}
): Promise<SimilarGenerationResult> {
  validateAIConfig();

  const finalOptions = {
    temperature: options.temperature ?? 0.7,
    maxTokens: options.maxTokens ?? 3500,
    retryOnFailure: options.retryOnFailure ?? true,
    preferCostEffective: options.preferCostEffective ?? true,
    ...options,
  };

  const { system, user, level, contentType } = createPrompts(reference);
  const { targetLevel } = extractCharacteristics(reference);

  const selectedModel =
    options.model ||
    selectOptimalModel(
      ["paragraph", "bulletList"],
      targetLevel as "beginner" | "intermediate" | "advanced", // Use target level for model selection
      finalOptions.preferCostEffective
    );
  const openrouter = createAIProvider(finalOptions.env);
  const model = openrouter(selectedModel);

  const schema =
    contentType === "kwaci-primer"
      ? aiKWACIPrimerResponseSchema
      : generatedLearningContentSchema;

  const call = async () => {
    const result = await generateObject({
      model,
      schema: schema as any,
      system,
      prompt: user,
      temperature: finalOptions.temperature,
      maxTokens: finalOptions.maxTokens,
    });

    return validateAndEnhance(
      result.object,
      extractCharacteristics(reference).sourceText,
      selectedModel,
      contentType,
      reference,
      targetLevel
    );
  };

  // Simple retry if enabled
  if (!finalOptions.retryOnFailure) return await call();

  let lastErr: any;
  for (let i = 0; i < 2; i++) {
    try {
      return await call();
    } catch (err) {
      lastErr = err;
    }
  }
  throw lastErr || new Error("Failed to generate similar content");
}
