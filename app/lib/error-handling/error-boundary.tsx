import { useRouteError, isRouteErrorResponse, Links, Meta, Scripts } from "react-router";
import { Button } from "~/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import React, { Component, ErrorInfo, ReactNode } from "react";

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: ReactNode;
}

export class ErrorBoundaryClass extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const error = this.state.error;
      
      return (
        <html lang="en">
          <head>
            <meta charSet="utf-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1" />
            <Meta />
            <Links />
          </head>
          <body>
            <div className="min-h-screen flex items-center justify-center p-4">
              <Card className="w-full max-w-md">
                <CardHeader>
                  <CardTitle className="text-destructive">Application Error</CardTitle>
                  <CardDescription>
                    Something went wrong. Please try again.
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    {error?.message || "An unexpected error occurred."}
                  </p>
                  <Button
                    onClick={() => window.location.reload()}
                    className="w-full"
                  >
                    Try Again
                  </Button>
                </CardContent>
              </Card>
            </div>
            <Scripts />
          </body>
        </html>
      );
    }

    return this.props.children;
  }
}

// For React Router v7, export a hook-free ErrorBoundary to avoid hydration-time crashes
export function ErrorBoundary() {
  // Hook-free generic fallback to avoid hydration-time hook crashes
  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <div className="min-h-screen flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-destructive">Application Error</CardTitle>
              <CardDescription>
                Something went wrong. Please try again.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button onClick={() => window.location.reload()} className="w-full">
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
        <Scripts />
      </body>
    </html>
  );
}

export function RouteErrorBoundary() {
  const error = useRouteError();
  
  if (isRouteErrorResponse(error)) {
    // Handle 401 Unauthorized responses - redirect to login
    if (error.status === 401) {
      console.log('[ErrorBoundary] 401 Unauthorized detected, redirecting to login');
      
      // Extract additional error information if available
      let errorDetails = { reason: 'Route 401 Error', source: 'error-boundary' };
      
      // Try to get additional error context from the response
      if (error.data && typeof error.data === 'object') {
        errorDetails = { ...errorDetails, ...error.data };
      }
      
      // Use useEffect to handle the redirect after component mounts
      React.useEffect(() => {
        // Clear any existing session data
        if (typeof window !== 'undefined') {
          // Dispatch session expired event for cleanup
          window.dispatchEvent(new CustomEvent('session-expired', {
            detail: errorDetails
          }));
          
          // Redirect to login
          window.location.href = '/login';
        }
      }, []);
      
      return (
        <html lang="en">
          <head>
            <meta charSet="utf-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1" />
            <Meta />
            <Links />
          </head>
          <body>
            <div className="min-h-screen flex items-center justify-center p-4">
              <Card className="w-full max-w-md">
                <CardHeader>
                  <CardTitle className="text-blue-600">Session Expired</CardTitle>
                  <CardDescription>
                    Your session has expired. Redirecting to login...
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                  <p className="text-sm text-muted-foreground text-center">
                    Please wait while we redirect you to the login page.
                  </p>
                </CardContent>
              </Card>
            </div>
            <Scripts />
          </body>
        </html>
      );
    }
    
    return (
      <html lang="en">
        <head>
          <meta charSet="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <Meta />
          <Links />
        </head>
        <body>
          <div className="min-h-screen flex items-center justify-center p-4">
            <Card className="w-full max-w-md">
              <CardHeader>
                <CardTitle className="text-destructive">
                  {error.status} {error.statusText}
                </CardTitle>
                <CardDescription>
                  Something went wrong. Please try again.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  {error.data?.message || "An unexpected error occurred."}
                </p>
                {error.data && typeof error.data === 'object' && (
                  <details className="text-xs">
                    <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                      Error Details
                    </summary>
                    <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                      {JSON.stringify(error.data, null, 2)}
                    </pre>
                  </details>
                )}
                <Button 
                  onClick={() => window.location.reload()} 
                  className="w-full"
                >
                  Try Again
                </Button>
              </CardContent>
            </Card>
          </div>
          <Scripts />
        </body>
      </html>
    )
  }

  if (error instanceof Error) {
    return (
      <html lang="en">
        <head>
          <meta charSet="utf-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1" />
          <Meta />
          <Links />
        </head>
        <body>
          <div className="min-h-screen flex items-center justify-center p-4">
            <Card className="w-full max-w-md">
              <CardHeader>
                <CardTitle className="text-destructive">Application Error</CardTitle>
                <CardDescription>
                  Something went wrong. Please try again.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  {error.message}
                </p>
                <Button
                  onClick={() => window.location.reload()}
                  className="w-full"
                >
                  Try Again
                </Button>
              </CardContent>
            </Card>
          </div>
          <Scripts />
        </body>
      </html>
    )
  }

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        <div className="min-h-screen flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader>
              <CardTitle className="text-destructive">Unknown Error</CardTitle>
              <CardDescription>
                Something went wrong. Please try again.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={() => window.location.reload()}
                className="w-full"
              >
                Try Again
              </Button>
            </CardContent>
          </Card>
        </div>
        <Scripts />
      </body>
    </html>
  )
}