import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON>otateCc<PERSON>, 
  <PERSON>ert<PERSON><PERSON>cle, 
  Play,
  Trophy,
  Clock
} from "lucide-react";
import type { QuizWithProgress } from "~/routes/api.quizzes";
import type { QuizDifficulty } from "~/components/quiz/types";

// Types for progress indicators
export interface ProgressIndicator {
  icon: any; // Lucide icon component
  color: string;
  bgColor: string;
  text: string;
  description: string;
  percentage?: number;
}

export interface ButtonProps {
  text: string;
  variant: 'default' | 'outline' | 'secondary' | 'ghost' | 'destructive';
  icon: any; // Lucide icon component
  disabled?: boolean;
}

// Calculate quiz progress from attempts data
export function getQuizProgress(attempts: any[]): QuizWithProgress['progress'] {
  if (attempts.length === 0) {
    return {
      status: 'not-started',
      completionPercentage: 0,
      totalAttempts: 0,
      timeSpent: 0,
    };
  }

  const completedAttempts = attempts.filter(attempt => attempt.isCompleted);
  const inProgressAttempts = attempts.filter(attempt => !attempt.isCompleted);
  
  let status: 'not-started' | 'in-progress' | 'completed';
  // Determine status based on the most recent attempt
  // Assuming attempts are ordered by most recent first
  const mostRecentAttempt = attempts[0];
  
  if (mostRecentAttempt?.isCompleted) {
    status = 'completed';
  } else if (inProgressAttempts.length > 0) {
    status = 'in-progress';
  } else {
    status = 'not-started';
  }

  // Calculate best score from completed attempts
  let bestScore;
  if (completedAttempts.length > 0) {
    const bestAttempt = completedAttempts.reduce((best, current) => {
      const currentPercentage = current.score?.percentage || 0;
      const bestPercentage = best.score?.percentage || 0;
      return currentPercentage > bestPercentage ? current : best;
    });
    
    if (bestAttempt.score) {
      bestScore = {
        percentage: bestAttempt.score.percentage,
        earnedPoints: bestAttempt.score.earnedPoints,
        totalPoints: bestAttempt.score.totalPoints,
      };
    }
  }

  // Calculate latest score from the most recent completed attempt
  let latestScore;
  if (completedAttempts.length > 0) {
    // Find the most recent completed attempt (attempts are ordered by most recent first)
    const latestCompletedAttempt = completedAttempts.find(attempt => attempt.isCompleted && attempt.score);
    
    if (latestCompletedAttempt && latestCompletedAttempt.score) {
      latestScore = {
        percentage: latestCompletedAttempt.score.percentage,
        earnedPoints: latestCompletedAttempt.score.earnedPoints,
        totalPoints: latestCompletedAttempt.score.totalPoints,
      };
    }
  }

  // Calculate total time spent
  const totalTimeSpent = attempts.reduce((total, attempt) => {
    return total + (attempt.totalTimeSpent || 0);
  }, 0);

  // Calculate completion percentage
  const completionPercentage = completedAttempts.length > 0 ? 100 : 
    (inProgressAttempts.length > 0 ? 50 : 0);

  return {
    status,
    completionPercentage,
    bestScore,
    latestScore,
    totalAttempts: attempts.length,
    lastAttemptAt: attempts[0]?.startedAt,
    timeSpent: totalTimeSpent,
  };
}

// Get progress indicator based on quiz status
export function getProgressIndicator(quiz: QuizWithProgress): ProgressIndicator {
  const progress = quiz.progress;
  
  if (!progress) {
    return {
      icon: AlertCircle,
      color: 'text-gray-500',
      bgColor: 'bg-gray-100 dark:bg-gray-800',
      text: 'Not Started',
      description: 'Ready to begin',
      percentage: 0,
    };
  }

  switch (progress.status) {
    case 'completed':
      return {
        icon: CheckCircle,
        color: 'text-green-600',
        bgColor: 'bg-green-50 dark:bg-green-900/20',
        text: 'Completed',
        description: progress.bestScore 
          ? `Best Score: ${progress.bestScore.percentage}%`
          : 'Quiz completed',
        percentage: 100,
      };
    case 'in-progress':
      return {
        icon: RotateCcw,
        color: 'text-blue-600',
        bgColor: 'bg-blue-50 dark:bg-blue-900/20',
        text: 'In Progress',
        description: `${progress.completionPercentage}% complete`,
        percentage: progress.completionPercentage,
      };
    default:
      return {
        icon: AlertCircle,
        color: 'text-gray-500',
        bgColor: 'bg-gray-100 dark:bg-gray-800',
        text: 'Not Started',
        description: 'Ready to begin',
        percentage: 0,
      };
  }
}

// Get button props based on quiz status
export function getButtonProps(quiz: QuizWithProgress): ButtonProps {
  const progress = quiz.progress;

  if (!progress || progress.status === 'not-started') {
    return {
      text: 'Take Quiz',
      variant: 'default',
      icon: Play,
      disabled: false,
    };
  }

  if (progress.status === 'in-progress') {
    return {
      text: 'Continue Quiz',
      variant: 'default',
      icon: RotateCcw,
      disabled: false,
    };
  }

  if (progress.status === 'completed') {
    return {
      text: 'Retake Quiz',
      variant: 'outline',
      icon: RotateCcw,
      disabled: false,
    };
  }

  return {
    text: 'Take Quiz',
    variant: 'default',
    icon: Play,
    disabled: false,
  };
}

// Get difficulty color classes
export function getDifficultyColor(difficulty: QuizDifficulty): string {
  switch (difficulty) {
    case 'easy':
      return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800';
    case 'medium':
      return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800';
    case 'hard':
      return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800';
    default:
      return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800';
  }
}

// Get difficulty badge color classes
export function getDifficultyBadgeColor(difficulty: QuizDifficulty): string {
  switch (difficulty) {
    case 'easy':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'hard':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
  }
}

// Format date for display
export function formatDate(date: Date | string, options?: Intl.DateTimeFormatOptions): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  const defaultOptions: Intl.DateTimeFormatOptions = {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
  };

  return new Intl.DateTimeFormat('en-US', { ...defaultOptions, ...options }).format(dateObj);
}

// Format relative date (e.g., "2 days ago")
export function formatRelativeDate(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return `${diffInWeeks} week${diffInWeeks === 1 ? '' : 's'} ago`;
  }

  return formatDate(dateObj);
}

// Format time duration (seconds to human readable)
export function formatTimeSpent(seconds: number): string {
  if (seconds < 60) {
    return `${seconds}s`;
  }

  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) {
    return `${minutes}m`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (hours < 24) {
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  }

  const days = Math.floor(hours / 24);
  const remainingHours = hours % 24;
  
  return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`;
}

// Format quiz score for display
export function formatQuizScore(score: { earnedPoints: number; totalPoints: number; percentage: number }): string {
  return `${score.earnedPoints}/${score.totalPoints} (${score.percentage}%)`;
}

// Get completion status color
export function getCompletionStatusColor(status: 'completed' | 'in-progress' | 'not-started'): string {
  switch (status) {
    case 'completed':
      return 'text-green-600 bg-green-50 dark:text-green-400 dark:bg-green-900/20';
    case 'in-progress':
      return 'text-blue-600 bg-blue-50 dark:text-blue-400 dark:bg-blue-900/20';
    case 'not-started':
      return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/20';
    default:
      return 'text-gray-600 bg-gray-50 dark:text-gray-400 dark:bg-gray-900/20';
  }
}

// Calculate quiz statistics for a group
export interface QuizGroupStats {
  total: number;
  completed: number;
  inProgress: number;
  notStarted: number;
  totalQuestions: number;
  totalDuration: number;
  completionPercentage: number;
  averageScore?: number;
}

export function calculateQuizGroupStats(quizzes: QuizWithProgress[]): QuizGroupStats {
  const total = quizzes.length;
  const completed = quizzes.filter(q => q.progress?.status === 'completed').length;
  const inProgress = quizzes.filter(q => q.progress?.status === 'in-progress').length;
  const notStarted = quizzes.filter(q => q.progress?.status === 'not-started').length;
  
  const totalQuestions = quizzes.reduce((sum, quiz) => sum + quiz.questions.length, 0);
  const totalDuration = quizzes.reduce((sum, quiz) => sum + quiz.estimatedDuration, 0);
  
  const completionPercentage = total > 0 ? Math.round((completed / total) * 100) : 0;

  // Calculate average score from completed quizzes
  const completedQuizzesWithScores = quizzes.filter(q => 
    q.progress?.status === 'completed' && q.progress.bestScore
  );
  
  const averageScore = completedQuizzesWithScores.length > 0
    ? Math.round(
        completedQuizzesWithScores.reduce((sum, quiz) => 
          sum + (quiz.progress?.bestScore?.percentage || 0), 0
        ) / completedQuizzesWithScores.length
      )
    : undefined;

  return {
    total,
    completed,
    inProgress,
    notStarted,
    totalQuestions,
    totalDuration,
    completionPercentage,
    averageScore,
  };
}
