/**
 * Utility functions for optimized materialized view refreshes
 * Implements both application-level and database-level debouncing and throttling
 * to reduce unnecessary database operations
 */

import { sql } from "drizzle-orm";
import type { Database } from "~/db/connection";
import { log } from "../logger";
import { materializedViewProcessor } from "../services/materialized-view-processor";

// Configuration
const DEBOUNCE_DELAY = 5000; // 5 seconds
const THROTTLE_INTERVAL = 10000; // 10 seconds
const MAX_RETRIES = 3;
const RETRY_DELAY = 1000; // 1 second

// Global state for debouncing and throttling
let debounceTimer: NodeJS.Timeout | null = null;
let lastRefreshTime = 0;
let isRefreshing = false;
let pendingRefresh = false;

// Track refresh statistics
interface RefreshStats {
  totalRequests: number;
  debouncedRequests: number;
  throttledRequests: number;
  successfulRefreshes: number;
  failedRefreshes: number;
  averageRefreshTime: number;
}

const refreshStats: RefreshStats = {
  totalRequests: 0,
  debouncedRequests: 0,
  throttledRequests: 0,
  successfulRefreshes: 0,
  failedRefreshes: 0,
  averageRefreshTime: 0,
};

/**
 * Debounced refresh - delays execution until activity stops
 * Batches multiple rapid changes into a single refresh
 */
export async function debouncedRefreshLeaderboard(db: Database): Promise<void> {
  refreshStats.totalRequests++;
  
  // Clear existing timer
  if (debounceTimer) {
    clearTimeout(debounceTimer);
    refreshStats.debouncedRequests++;
  }

  // Set new timer
  debounceTimer = setTimeout(async () => {
    await executeRefresh(db, 'debounced');
  }, DEBOUNCE_DELAY);

  log.info('Materialized view refresh debounced', {
    delay: DEBOUNCE_DELAY,
    type: 'debounced'
  });
}

/**
 * Throttled refresh - limits execution frequency
 * Ensures refresh doesn't happen more than once per interval
 */
export async function throttledRefreshLeaderboard(db: Database): Promise<void> {
  refreshStats.totalRequests++;
  const now = Date.now();
  const timeSinceLastRefresh = now - lastRefreshTime;

  if (timeSinceLastRefresh >= THROTTLE_INTERVAL) {
    // Execute immediately if enough time has passed
    lastRefreshTime = now;
    await executeRefresh(db, 'throttled');
  } else {
    // Schedule for later if within throttle interval
    refreshStats.throttledRequests++;
    const remainingTime = THROTTLE_INTERVAL - timeSinceLastRefresh;
    
    if (!pendingRefresh) {
      pendingRefresh = true;
      setTimeout(async () => {
        lastRefreshTime = Date.now();
        pendingRefresh = false;
        await executeRefresh(db, 'throttled-delayed');
      }, remainingTime);

      log.info('Materialized view refresh throttled', {
        remainingTime,
        type: 'throttled-delayed'
      });
    }
  }
}

/**
 * Combined debounced + throttled refresh
 * Uses debouncing for batching with throttling as a safety net
 */
export async function optimizedRefreshLeaderboard(db: Database): Promise<void> {
  refreshStats.totalRequests++;
  const now = Date.now();
  const timeSinceLastRefresh = now - lastRefreshTime;

  // If it's been too long since last refresh, execute immediately (throttle override)
  if (timeSinceLastRefresh >= THROTTLE_INTERVAL) {
    // Clear any pending debounce
    if (debounceTimer) {
      clearTimeout(debounceTimer);
      debounceTimer = null;
    }
    
    lastRefreshTime = now;
    await executeRefresh(db, 'optimized-immediate');
  } else {
    // Otherwise, use debouncing
    await debouncedRefreshLeaderboard(db);
  }
}

/**
 * Core refresh execution with retry logic and monitoring
 */
async function executeRefresh(db: Database, refreshType: string): Promise<void> {
  if (isRefreshing) {
    log.warn('Materialized view refresh already in progress, skipping', {
      type: refreshType
    });
    return;
  }

  isRefreshing = true;
  const startTime = Date.now();

  try {
    await retryRefresh(db, MAX_RETRIES);
    
    const duration = Date.now() - startTime;
    refreshStats.successfulRefreshes++;
    refreshStats.averageRefreshTime = 
      (refreshStats.averageRefreshTime * (refreshStats.successfulRefreshes - 1) + duration) / 
      refreshStats.successfulRefreshes;
    
    log.info('Materialized view refresh completed', {
      type: refreshType,
      duration,
      success: true
    });

    // Track performance metrics
    trackRefreshMetrics(refreshType, duration, true);

  } catch (error) {
    const duration = Date.now() - startTime;
    refreshStats.failedRefreshes++;
    
    log.error('Materialized view refresh failed', {
      type: refreshType,
      duration,
      error: error instanceof Error ? error.message : 'Unknown error',
      success: false
    });

    // Track failure metrics
    trackRefreshMetrics(refreshType, duration, false);
    
    throw error;
  } finally {
    isRefreshing = false;
  }
}

/**
 * Retry logic for refresh operations
 */
async function retryRefresh(db: Database, maxRetries: number): Promise<void> {
  let lastError: Error | null = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      await db.execute(sql`REFRESH MATERIALIZED VIEW CONCURRENTLY user_points_leaderboard_mv;`);
      return; // Success
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown refresh error');
      
      log.warn('Materialized view refresh attempt failed', {
        attempt,
        maxRetries,
        error: lastError.message
      });

      // Wait before retry (except on last attempt)
      if (attempt < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, RETRY_DELAY * attempt));
      }
    }
  }

  throw lastError;
}

/**
 * Track refresh performance metrics
 */
function trackRefreshMetrics(type: string, duration: number, success: boolean): void {
  // This could be extended to send metrics to monitoring services
  // For now, we'll just log structured data
  log.info('Refresh metrics', {
    metric: 'materialized_view_refresh',
    type,
    duration,
    success,
    timestamp: new Date().toISOString()
  });
}

/**
 * Immediate refresh (for critical operations)
 * Bypasses debouncing/throttling for urgent updates
 */
export async function immediateRefreshLeaderboard(db: Database): Promise<void> {
  await executeRefresh(db, 'immediate');
}

/**
 * Get refresh status and metrics
 */
export function getRefreshStatus() {
  return {
    isRefreshing,
    pendingRefresh,
    lastRefreshTime,
    timeSinceLastRefresh: Date.now() - lastRefreshTime,
    stats: { ...refreshStats }
  };
}

/**
 * Clear all pending refreshes (useful for testing or shutdown)
 */
export function clearPendingRefreshes(): void {
  if (debounceTimer) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }
  pendingRefresh = false;
  log.info('Cleared all pending materialized view refreshes');
}