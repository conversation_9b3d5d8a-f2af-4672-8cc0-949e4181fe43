/**
 * Session management utilities for analytics tracking
 */

// Generate a unique session ID for analytics tracking
export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Get or create session ID from sessionStorage
export function getOrCreateSessionId(): string {
  if (typeof window === 'undefined') {
    // Server-side: generate a new session ID
    return generateSessionId();
  }

  const storageKey = 'kwaci_learning_session_id';
  let sessionId = sessionStorage.getItem(storageKey);
  
  if (!sessionId) {
    sessionId = generateSessionId();
    sessionStorage.setItem(storageKey, sessionId);
  }
  
  return sessionId;
}

// Clear current session ID (useful for logout or session reset)
export function clearSessionId(): void {
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem('kwaci_learning_session_id');
  }
}

// Check if session ID exists
export function hasSessionId(): boolean {
  if (typeof window === 'undefined') {
    return false;
  }
  
  return sessionStorage.getItem('kwaci_learning_session_id') !== null;
}