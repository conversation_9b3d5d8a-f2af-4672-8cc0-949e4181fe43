// Environment configuration - safe for both server and client
const getNodeEnv = () => {
  if (typeof process !== 'undefined' && process.env) {
    return process.env.NODE_ENV;
  }
  // Fallback for client-side
  return 'development';
};

const nodeEnv = getNodeEnv();
const isDevelopment = nodeEnv === 'development';
const isProduction = nodeEnv === 'production';
const isTest = nodeEnv === 'test';
const isBrowser = typeof window !== 'undefined';

// Conditional winston import for server-side only
let winston: any = null;
if (!isBrowser) {
  try {
    winston = require('winston');
  } catch (error) {
    console.warn('Winston not available, falling back to console logging');
  }
}

// Custom log levels with colors
const logLevels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

const logColors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'blue',
};

// Add colors to winston (server-side only)
if (winston) {
  winston.addColors(logColors);
}

// Custom format for beautiful console output (server-side only)
const consoleFormat = winston ? winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.colorize({ all: true }),
  winston.format.printf((info: any) => {
    const { timestamp, level, message, requestId, ...meta } = info;
    
    // Format the main message
    let logMessage = `${timestamp} [${level}]`;
    
    // Add request ID if present
    if (requestId) {
      logMessage += ` [${requestId}]`;
    }
    
    logMessage += `: ${message}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      logMessage += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return logMessage;
  })
) : null;

// File format for production logs (server-side only)
const fileFormat = winston ? winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
) : null;

// Create transports based on environment (server-side only)
const transports: any[] = [];

if (winston) {
  // Console transport for development
  if (isDevelopment || isTest) {
    transports.push(
      new winston.transports.Console({
        level: isDevelopment ? 'debug' : 'error',
        format: consoleFormat,
      })
    );
  }

  // File transports for production
  if (isProduction) {
    transports.push(
      new winston.transports.File({
        filename: 'logs/error.log',
        level: 'error',
        format: fileFormat,
      }),
      new winston.transports.File({
        filename: 'logs/combined.log',
        level: 'info',
        format: fileFormat,
      })
    );
  }
}

// Create the logger instance (server-side only)
const logger = winston ? winston.createLogger({
  levels: logLevels,
  level: isDevelopment ? 'debug' : isProduction ? 'info' : 'error',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true })
  ),
  transports,
  // Don't exit on handled exceptions
  exitOnError: false,
}) : null;

// Enhanced logging interface with request tracking
class Logger {
  private static instance: Logger;
  private winston: any;
  private enabled: boolean;
  private isBrowser: boolean;

  private constructor() {
    this.winston = logger;
    this.isBrowser = typeof window !== 'undefined';
    // Can be controlled via environment variable
    this.enabled = this.isBrowser ? true : (typeof process !== 'undefined' && process.env ? process.env.DISABLE_LOGGING !== 'true' : true);
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  // Enable/disable logging dynamically
  enable(): void {
    this.enabled = true;
  }

  disable(): void {
    this.enabled = false;
  }

  isEnabled(): boolean {
    return this.enabled;
  }

  // Core logging methods
  error(message: string, meta?: any, requestId?: string): void {
    if (!this.enabled) return;
    if (this.winston) {
      this.winston.error(message, { ...meta, requestId });
    } else {
      // Browser fallback
      const logMessage = this.formatBrowserMessage('ERROR', message, requestId, meta);
      console.error(logMessage);
    }
  }

  warn(message: string, meta?: any, requestId?: string): void {
    if (!this.enabled) return;
    if (this.winston) {
      this.winston.warn(message, { ...meta, requestId });
    } else {
      // Browser fallback
      const logMessage = this.formatBrowserMessage('WARN', message, requestId, meta);
      console.warn(logMessage);
    }
  }

  info(message: string, meta?: any, requestId?: string): void {
    if (!this.enabled) return;
    if (this.winston) {
      this.winston.info(message, { ...meta, requestId });
    } else {
      // Browser fallback
      const logMessage = this.formatBrowserMessage('INFO', message, requestId, meta);
      console.info(logMessage);
    }
  }

  http(message: string, meta?: any, requestId?: string): void {
    if (!this.enabled) return;
    if (this.winston) {
      this.winston.http(message, { ...meta, requestId });
    } else {
      // Browser fallback
      const logMessage = this.formatBrowserMessage('HTTP', message, requestId, meta);
      console.log(logMessage);
    }
  }

  debug(message: string, meta?: any, requestId?: string): void {
    if (!this.enabled) return;
    if (this.winston) {
      this.winston.debug(message, { ...meta, requestId });
    } else {
      // Browser fallback
      const logMessage = this.formatBrowserMessage('DEBUG', message, requestId, meta);
      console.debug(logMessage);
    }
  }

  // Helper method for browser message formatting
  private formatBrowserMessage(level: string, message: string, requestId?: string, meta?: any): string {
    const timestamp = new Date().toISOString().replace('T', ' ').substring(0, 19);
    let logMessage = `${timestamp} [${level}]`;
    
    if (requestId) {
      logMessage += ` [${requestId}]`;
    }
    
    logMessage += `: ${message}`;
    
    if (meta && Object.keys(meta).length > 0) {
      logMessage += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return logMessage;
  }

  // Convenience methods with emojis for better visual distinction
  success(message: string, meta?: any, requestId?: string): void {
    this.info(`✅ ${message}`, meta, requestId);
  }

  failure(message: string, meta?: any, requestId?: string): void {
    this.error(`❌ ${message}`, meta, requestId);
  }

  start(message: string, meta?: any, requestId?: string): void {
    this.info(`🚀 ${message}`, meta, requestId);
  }

  complete(message: string, meta?: any, requestId?: string): void {
    this.info(`🎉 ${message}`, meta, requestId);
  }

  processing(message: string, meta?: any, requestId?: string): void {
    this.info(`⚙️ ${message}`, meta, requestId);
  }

  api(message: string, meta?: any, requestId?: string): void {
    this.http(`📡 ${message}`, meta, requestId);
  }

  database(message: string, meta?: any, requestId?: string): void {
    this.debug(`💾 ${message}`, meta, requestId);
  }

  ai(message: string, meta?: any, requestId?: string): void {
    this.info(`🤖 ${message}`, meta, requestId);
  }

  validation(message: string, meta?: any, requestId?: string): void {
    this.debug(`🔍 ${message}`, meta, requestId);
  }

  // Request-scoped logger
  withRequestId(requestId: string) {
    return {
      error: (message: string, meta?: any) => this.error(message, meta, requestId),
      warn: (message: string, meta?: any) => this.warn(message, meta, requestId),
      info: (message: string, meta?: any) => this.info(message, meta, requestId),
      http: (message: string, meta?: any) => this.http(message, meta, requestId),
      debug: (message: string, meta?: any) => this.debug(message, meta, requestId),
      success: (message: string, meta?: any) => this.success(message, meta, requestId),
      failure: (message: string, meta?: any) => this.failure(message, meta, requestId),
      start: (message: string, meta?: any) => this.start(message, meta, requestId),
      complete: (message: string, meta?: any) => this.complete(message, meta, requestId),
      processing: (message: string, meta?: any) => this.processing(message, meta, requestId),
      api: (message: string, meta?: any) => this.api(message, meta, requestId),
      database: (message: string, meta?: any) => this.database(message, meta, requestId),
      ai: (message: string, meta?: any) => this.ai(message, meta, requestId),
      validation: (message: string, meta?: any) => this.validation(message, meta, requestId),
    };
  }

  // Performance timing
  time(label: string): void {
    if (!this.enabled) return;
    console.time(label);
  }

  timeEnd(label: string): void {
    if (!this.enabled) return;
    console.timeEnd(label);
  }

  // Group logging for related operations
  group(label: string): void {
    if (!this.enabled) return;
    console.group(label);
  }

  groupEnd(): void {
    if (!this.enabled) return;
    console.groupEnd();
  }
}

// Export singleton instance
export const log = Logger.getInstance();

// Export types for TypeScript
export type LogLevel = keyof typeof logLevels;
export type LogMeta = Record<string, any>;

// Export logger class for advanced usage
export { Logger };

// Environment helpers
export const logConfig = {
  isDevelopment,
  isProduction,
  isTest,
  isEnabled: () => log.isEnabled(),
  enable: () => log.enable(),
  disable: () => log.disable(),
};