/**
 * Authentication provider that wraps Supabase Auth
 * and provides session management capabilities with automatic session monitoring
 */

import { createContext, useContext, useEffect, useState } from 'react';
import { useNavigate } from 'react-router';
import { supabase, type User, type Session } from '~/lib/auth-client';
import { SessionMonitor, setupGlobalFetchInterceptor, useGlobalSessionExpiration } from './session-monitor';
import { log } from '~/lib/logger';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signOut: () => Promise<boolean>;
  refreshSession: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * Authentication provider component
 * Manages Supabase Auth session state and provides auth context with session monitoring
 */
export function AuthProvider({ children }: AuthProviderProps) {
  const navigate = useNavigate();
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const refreshSession = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      if (error) {
        console.error('Failed to refresh session:', error);
        setUser(null);
        setSession(null);
        return;
      }

      // Check if session is valid and not expired
      if (session && session.expires_at) {
        const now = Math.floor(Date.now() / 1000);
        if (session.expires_at <= now) {
          console.log('Session expired, clearing state');
          setUser(null);
          setSession(null);
          return;
        }
      }

      setSession(session);
      setUser(session?.user ?? null);

      // Ensure HttpOnly cookies are in sync for server API access
      if (session?.access_token) {
        try {
          await fetch('/api/auth/sync', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              access_token: session.access_token,
              refresh_token: session.refresh_token,
              expires_at: session.expires_at ?? Math.floor(Date.now() / 1000) + 3600,
            }),
          });
        } catch (e) {
          console.warn('Auth cookie sync on refresh failed (non-blocking):', e);
        }
      }
    } catch (error) {
      console.error('Failed to refresh session:', error);
      setUser(null);
      setSession(null);
    }
  };

  const signOut = async () => {
    let logoutSuccessful = false;
    
    try {
      // Clear local state immediately to prevent race conditions
      setUser(null);
      setSession(null);

      // Only attempt Supabase signOut if there's an active session
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      
      let supabaseSignOutSuccess = true;
      if (currentSession) {
        const { error } = await supabase.auth.signOut();
        if (error && error.message !== 'Auth session missing!') {
          console.error('Failed to sign out from Supabase:', error);
          supabaseSignOutSuccess = false;
        }
      }

      // Clear server-side HttpOnly cookies
      let cookieClearSuccess = true;
      try {
        const response = await fetch('/api/auth/sync', {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' },
        });
        if (!response.ok) {
          console.warn('Server-side cookie clear returned non-OK status:', response.status);
          cookieClearSuccess = false;
        }
      } catch (e) {
        console.warn('Failed to clear server-side session cookies:', e);
        cookieClearSuccess = false;
      }

      // Only consider logout successful if both operations succeeded
      logoutSuccessful = supabaseSignOutSuccess && cookieClearSuccess;
      
      if (logoutSuccessful) {
        console.log('Logout completed successfully, navigating to login');
        navigate('/login', { replace: true });
      } else {
        console.error('Logout process had errors, but local state cleared. Manual navigation required.');
        // Don't navigate automatically if logout had errors
        // User can manually navigate or retry logout
      }
    } catch (error) {
      console.error('Failed to sign out:', error);
      logoutSuccessful = false;
      // Clear local state even if logout fails
      setUser(null);
      setSession(null);
      // Don't navigate if there were errors
    }
    
    return logoutSuccessful;
  };

  // Set up global fetch interceptor on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setupGlobalFetchInterceptor();
      log.info('Global fetch interceptor initialized for Supabase Auth');
    }
  }, []);

  useEffect(() => {
    // Get initial session
    const initializeAuth = async () => {
      setIsLoading(true);
      await refreshSession();
      setIsLoading(false);
    };

    initializeAuth();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        log.info('Auth state changed:', event);
        setSession(session);
        setUser(session?.user ?? null);

        if (event === 'SIGNED_OUT') {
          setUser(null);
          setSession(null);
        } else if (event === 'SIGNED_IN' && session?.user) {
          // Sync user to application database when they sign in via server API
          try {
            await fetch('/api/user-sync', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ user: session.user }),
            });
          } catch (e) {
            console.warn('User sync failed (non-blocking):', e);
          }

          // Also sync Supabase tokens to HttpOnly cookies so server can authenticate direct /api/* requests
          try {
            await fetch('/api/auth/sync', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                access_token: session.access_token,
                refresh_token: session.refresh_token,
                expires_at: (session.expires_at ?? 0) * 1000 > 0 ? session.expires_at : Math.floor(Date.now() / 1000) + 3600,
              }),
            });
          } catch (e) {
            console.warn('Auth cookie sync failed (non-blocking):', e);
          }
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const value: AuthContextType = {
    user,
    session,
    isLoading,
    isAuthenticated: !!user,
    signOut,
    refreshSession,
  };

  return (
    <AuthContext.Provider value={value}>
      <SessionMonitor>
        <GlobalSessionHandler>
          {children}
        </GlobalSessionHandler>
      </SessionMonitor>
    </AuthContext.Provider>
  );
}

/**
 * Internal component to handle global session expiration events
 */
function GlobalSessionHandler({ children }: { children: React.ReactNode }) {
  // Set up global session expiration listener
  useGlobalSessionExpiration();

  return <>{children}</>;
}

/**
 * Hook to use the auth context
 */
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}