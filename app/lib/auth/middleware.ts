import type { LoaderFunctionArgs, ActionFunctionArgs } from 'react-router';
import { requireAuthSession, requireAuth, createAuthContext, type AuthSession, type AuthContext } from './supabase-server';

/**
 * Middleware for API routes that require authentication
 * Throws a 401 Response if authentication fails
 */
export async function withApiAuth<T>(
  handler: (args: LoaderFunctionArgs | ActionFunctionArgs, authSession: AuthSession) => Promise<T>
) {
  return async (args: LoaderFunctionArgs | ActionFunctionArgs): Promise<T> => {
    try {
      const authSession = await requireAuthSession(args.request);
      return await handler(args, authSession);
    } catch (error) {
      // If it's already a Response (401), re-throw it
      if (error instanceof Response) {
        throw error;
      }
      
      // Otherwise, create a 401 response
      const url = new URL(args.request.url);
      const isApi = url.pathname.startsWith('/api');
      const body = JSON.stringify({ success: false, error: 'UNAUTHORIZED' });
      
      throw new Response(isApi ? body : 'Unauthorized', {
        status: 401,
        headers: { 'Content-Type': isApi ? 'application/json' : 'text/plain' },
      });
    }
  };
}

/**
 * Middleware for dashboard routes that require authentication
 * Redirects to login if authentication fails
 */
export async function withDashboardAuth<T>(
  handler: (args: LoaderFunctionArgs | ActionFunctionArgs, authContext: AuthContext) => Promise<T>
) {
  return async (args: LoaderFunctionArgs | ActionFunctionArgs): Promise<T> => {
    try {
      const authContext = await requireAuth(args.request);
      return await handler(args, authContext);
    } catch (error) {
      // If it's already a Response (redirect), re-throw it
      if (error instanceof Response) {
        throw error;
      }
      
      // Otherwise, create a redirect to login
      const url = new URL(args.request.url);
      const redirectTo = encodeURIComponent(url.pathname + url.search);
      throw new Response(null, {
        status: 302,
        headers: {
          Location: `/login?redirectTo=${redirectTo}`,
        },
      });
    }
  };
}

/**
 * Middleware for routes that optionally use authentication
 * Provides auth context but doesn't enforce authentication
 */
export async function withOptionalAuth<T>(
  handler: (args: LoaderFunctionArgs | ActionFunctionArgs, authContext: AuthContext) => Promise<T>
) {
  return async (args: LoaderFunctionArgs | ActionFunctionArgs): Promise<T> => {
    const authContext = await createAuthContext(args.request);
    return await handler(args, authContext);
  };
}

/**
 * Higher-order function to create authenticated loaders
 */
export function createAuthenticatedLoader<T>(
  loader: (args: LoaderFunctionArgs, authSession: AuthSession) => Promise<T>,
  options: { type: 'api' | 'dashboard' } = { type: 'dashboard' }
) {
  if (options.type === 'api') {
    return withApiAuth(loader);
  }
  
  return async (args: LoaderFunctionArgs): Promise<T> => {
    const authContext = await requireAuth(args.request);
    // Convert AuthContext to AuthSession for compatibility
    if (!authContext.session || !authContext.user) {
      throw new Response(null, {
        status: 302,
        headers: {
          Location: `/login?redirectTo=${encodeURIComponent(new URL(args.request.url).pathname)}`,
        },
      });
    }
    
    const authSession: AuthSession = {
      user: authContext.user,
      session: authContext.session,
    };
    
    return await loader(args, authSession);
  };
}

/**
 * Higher-order function to create authenticated actions
 */
export function createAuthenticatedAction<T>(
  action: (args: ActionFunctionArgs, authSession: AuthSession) => Promise<T>,
  options: { type: 'api' | 'dashboard' } = { type: 'dashboard' }
) {
  if (options.type === 'api') {
    return withApiAuth(action);
  }
  
  return async (args: ActionFunctionArgs): Promise<T> => {
    const authContext = await requireAuth(args.request);
    // Convert AuthContext to AuthSession for compatibility
    if (!authContext.session || !authContext.user) {
      throw new Response(null, {
        status: 302,
        headers: {
          Location: `/login?redirectTo=${encodeURIComponent(new URL(args.request.url).pathname)}`,
        },
      });
    }
    
    const authSession: AuthSession = {
      user: authContext.user,
      session: authContext.session,
    };
    
    return await action(args, authSession);
  };
}

/**
 * Utility to extract user information from authenticated requests
 */
export function getAuthenticatedUser(authSession: AuthSession) {
  return {
    id: authSession.user.id,
    email: authSession.user.email,
    emailVerified: authSession.user.email_confirmed_at != null,
    metadata: authSession.user.user_metadata,
    appMetadata: authSession.user.app_metadata,
  };
}

/**
 * Utility to check if a session is expired
 */
export function isSessionExpired(session: AuthSession['session']): boolean {
  if (!session.expires_at) {
    return false; // No expiration time means it doesn't expire
  }
  
  const now = Math.floor(Date.now() / 1000);
  return session.expires_at < now;
}

/**
 * Utility to get session time remaining in seconds
 */
export function getSessionTimeRemaining(session: AuthSession['session']): number | null {
  if (!session.expires_at) {
    return null; // No expiration time
  }
  
  const now = Math.floor(Date.now() / 1000);
  return Math.max(0, session.expires_at - now);
}

/**
 * Type guards for authentication
 */
export function isAuthenticatedSession(authContext: AuthContext): authContext is AuthContext & {
  session: NonNullable<AuthContext['session']>;
  user: NonNullable<AuthContext['user']>;
  isAuthenticated: true;
} {
  return authContext.isAuthenticated && !!authContext.session && !!authContext.user;
}

export function isAuthSession(value: any): value is AuthSession {
  return value && typeof value === 'object' && 'user' in value && 'session' in value;
}