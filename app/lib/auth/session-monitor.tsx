/**
 * Session monitoring component for automatic logout on session expiration
 *
 * This component monitors the user's session state and automatically
 * logs out users when their session expires or when 401 responses are received.
 *
 * Updated for Supabase Auth compatibility.
 */

import { useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router';
import { useAuth } from '~/lib/auth/auth-provider';
import { toast } from 'sonner';
import { log } from '~/lib/logger';

interface SessionMonitorProps {
  children: React.ReactNode;
}

/**
 * Session monitoring component that handles automatic logout
 */
export function SessionMonitor({ children }: SessionMonitorProps) {
  const { session, user, isLoading, signOut } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const hasLoggedOut = useRef(false);
  const lastSessionCheck = useRef<Date | null>(null);

  // Handle session expiration and errors
  useEffect(() => {
    // Skip if we're already on login page, have already logged out, or still loading
    if (location.pathname === '/login' || hasLoggedOut.current || isLoading) {
      return;
    }

    // Check if session exists but is expired
    if (session?.expires_at) {
      const expiresAt = new Date(session.expires_at * 1000); // Supabase uses Unix timestamp
      const now = new Date();

      if (expiresAt <= now) {
        log.warn('Session expired:', {
          expiresAt: expiresAt.toISOString(),
          now: now.toISOString(),
          pathname: location.pathname
        });
        handleSessionExpired('Your session has expired');
        return;
      }
    } else if (!user && !isLoading) {
      // No session and not loading - user might be logged out
      log.warn('No session found:', { pathname: location.pathname });
      // Only handle this on protected routes, and only if we haven't already logged out
      if (location.pathname.startsWith('/dashboard') && !hasLoggedOut.current) {
        // Don't trigger logout if there was never a session to begin with
        // This prevents the 403 error cycle
        log.info('Redirecting to login due to missing session');
        navigate('/login', { replace: true });
        return;
      }
    }

    // Update last session check timestamp
    lastSessionCheck.current = new Date();
  }, [session, user, isLoading, location.pathname]);

  // Set up periodic session validation for protected routes
  useEffect(() => {
    // Only monitor session on protected routes (dashboard pages)
    if (!location.pathname.startsWith('/dashboard')) {
      return;
    }

    const interval = setInterval(() => {
      // Skip if we're not on a protected route, have already logged out, or still loading
      if (!location.pathname.startsWith('/dashboard') || hasLoggedOut.current || isLoading) {
        return;
      }

      // Check if session is still valid
      if (session?.expires_at) {
        const expiresAt = new Date(session.expires_at * 1000); // Supabase uses Unix timestamp
        const now = new Date();

        if (expiresAt <= now) {
          log.warn('Session expired during periodic check:', {
            expiresAt: expiresAt.toISOString(),
            now: now.toISOString(),
            pathname: location.pathname
          });
          handleSessionExpired('Your session has expired');
        }
      } else if (!user && !isLoading) {
        // Session is null but no error - might be expired
        log.warn('No session found during periodic check:', { pathname: location.pathname });
        // Give it a moment in case it's still loading
        setTimeout(() => {
          if (!session && !user && !hasLoggedOut.current && !isLoading) {
            // Don't trigger logout if there was never a session to begin with
            // This prevents the 403 error cycle
            log.info('Redirecting to login due to missing session (periodic check)');
            navigate('/login', { replace: true });
          }
        }, 1000);
      }
    }, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, [session, user, isLoading, location.pathname]);

  // Handle automatic logout
  const handleSessionExpired = async (reason: string) => {
    if (hasLoggedOut.current) {
      return; // Prevent multiple logout attempts
    }

    hasLoggedOut.current = true;

    try {
      log.info('Initiating automatic logout:', { reason, pathname: location.pathname });

      // Show user-friendly message
      toast.warning('Session expired. Please sign in again.', {
        duration: 4000,
      });

      // Only attempt to sign out if we have a session or user
      if (session || user) {
        const logoutSuccessful = await signOut();
        if (!logoutSuccessful) {
          log.warn('Logout failed during session expiration, but continuing with redirect');
        }
      } else {
        log.info('No session to sign out from, proceeding to redirect');
      }

      // Navigate to login page
      navigate('/login', { replace: true });

    } catch (error) {
      log.error('Failed to sign out during session expiration:', error);
      // Force navigation to login even if signOut fails
      navigate('/login', { replace: true });
    }
  };

  return <>{children}</>;
}

/**
 * Global fetch interceptor for handling 401 responses
 * This should be set up once when the app initializes
 * Enhanced with better error detection and retry logic
 */
export function setupGlobalFetchInterceptor() {
  // Store the original fetch function
  const originalFetch = window.fetch;
  
  // Override the global fetch function
  window.fetch = async (...args) => {
    try {
      const response = await originalFetch(...args);
      
      // Check for 401 Unauthorized responses
      if (response.status === 401) {
        const url = typeof args[0] === 'string' ? args[0] : 
                   args[0] instanceof Request ? args[0].url : 
                   args[0] instanceof URL ? args[0].toString() : 'unknown';
        
        // Extract additional error information from headers
        const authError = response.headers.get('X-Auth-Error');
        const redirectTo = response.headers.get('X-Redirect-To');
        
        log.warn('401 Unauthorized response detected:', { 
          url, 
          authError, 
          redirectTo,
          headers: Object.fromEntries(response.headers.entries())
        });
        
        // Only handle 401s from our API endpoints, not external services
        if (url.includes('/api/') || url.includes('/dashboard/')) {
          // Try to get error details from response body
          let errorDetails = { reason: '401 Unauthorized', url, authError, redirectTo };
          
          try {
            const responseClone = response.clone();
            const contentType = response.headers.get('content-type');
            
            if (contentType && contentType.includes('application/json')) {
              const errorData = await responseClone.json();
              errorDetails = { ...errorDetails, ...errorData };
            }
          } catch (e) {
            // Ignore JSON parsing errors
            log.warn('Failed to parse 401 response body:', e);
          }
          
          // Dispatch a custom event that the SessionMonitor can listen to
          window.dispatchEvent(new CustomEvent('session-expired', {
            detail: errorDetails
          }));
        }
      }
      
      return response;
    } catch (error) {
      // Re-throw network errors
      throw error;
    }
  };
}

/**
 * Hook to handle global session expiration events
 * This should be used in the root component to handle session expiration
 * from anywhere in the app (fetch interceptor, error boundaries, etc.)
 * Enhanced with better error messaging and retry logic
 */
export function useGlobalSessionExpiration() {
  const navigate = useNavigate();
  const location = useLocation();
  const { signOut, session, user } = useAuth();
  const hasLoggedOut = useRef(false);

  useEffect(() => {
    const handleSessionExpired = async (event: CustomEvent) => {
      // Skip if we're already on login page or have already logged out
      if (location.pathname === '/login' || hasLoggedOut.current) {
        return;
      }

      hasLoggedOut.current = true;

      const details = event.detail || {};
      const { reason, url, authError, redirectTo } = details;

      try {
        log.info('Handling global session expiration:', { reason, url, authError, redirectTo, pathname: location.pathname });

        // Determine the appropriate message based on error details
        let message = 'Session expired. Please sign in again.';
        let duration = 4000;
        
        if (authError) {
          switch (authError) {
            case 'session_expired':
              message = 'Your session has expired. Please log in again.';
              break;
            case 'invalid_token':
              message = 'Your authentication token is invalid. Please log in again.';
              break;
            case 'token_refresh_failed':
              message = 'Unable to refresh your session. Please log in again.';
              break;
            case 'no_session':
              message = 'No active session found. Please log in to continue.';
              break;
            default:
              message = `Authentication error: ${authError}. Please log in again.`;
          }
        }

        // Show appropriate warning to the user
        toast.warning(message, {
          duration,
          description: url ? `Failed request: ${url}` : undefined,
        });

        // Only attempt to sign out if we have a session or user
        if (session || user) {
          const logoutSuccessful = await signOut();
          if (!logoutSuccessful) {
            log.warn('Logout failed in global session expiration handler, but continuing with redirect');
          }
        } else {
          log.info('No session to sign out from in global handler, proceeding to redirect');
        }

        // Navigate to login page, preserving the intended destination if available
        const loginPath = redirectTo || '/login';
        const currentPath = location.pathname;
        
        // If we're not already on the login page, preserve the current path for redirect after login
        if (currentPath !== '/login' && currentPath !== '/') {
          navigate(`${loginPath}?redirect=${encodeURIComponent(currentPath)}`, { replace: true });
        } else {
          navigate(loginPath, { replace: true });
        }

      } catch (error) {
        log.error('Failed to handle global session expiration:', error);
        
        // Show error message
        toast.error('An error occurred while signing out. Redirecting to login...', {
          duration: 3000,
        });
        
        // Force navigation to login even if signOut fails
        navigate('/login', { replace: true });
      }
    };

    // Listen for session expiration events
    window.addEventListener('session-expired', handleSessionExpired as unknown as EventListener);

    return () => {
      window.removeEventListener('session-expired', handleSessionExpired as unknown as EventListener);
    };
  }, [navigate, location.pathname, signOut]);
}