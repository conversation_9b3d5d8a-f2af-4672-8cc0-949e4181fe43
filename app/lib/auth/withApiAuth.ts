import { requireAuthSession } from "~/lib/auth/supabase-server";

export type ApiHandler<T = any> = (args: {
  request: Request;
  user: { id: string; email?: string | null };
}) => Promise<Response> | Response;

/**
 * Wrap API route loader/action to enforce auth and unify 401 responses.
 */
export function withApiAuth(handler: ApiHandler) {
  return async function (args: { request: Request }) {
    try {
      const auth = await requireAuthSession(args.request);
      return await handler({ request: args.request, user: { id: auth.user.id, email: auth.user.email } });
    } catch (err) {
      if (err instanceof Response && err.status === 401) {
        return new Response(JSON.stringify({ success: false, error: "UNAUTHORIZED" }), {
          status: 401,
          headers: { "Content-Type": "application/json" },
        });
      }
      // Re-throw other errors to be handled by route's error logic
      throw err;
    }
  };
}

