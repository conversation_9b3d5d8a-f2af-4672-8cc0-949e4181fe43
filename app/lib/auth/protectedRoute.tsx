import React from "react";
import { useAuth } from "~/lib/auth/auth-provider";

/**
 * Protects a component by checking client auth state.
 * Shows nothing until auth is resolved to avoid hydration race.
 */
export function withProtectedRoute<P extends React.JSX.IntrinsicAttributes>(Component: React.ComponentType<P>) {
  return function ProtectedWrapper(props: P) {
    const { isLoading, isAuthenticated } = useAuth();

    if (isLoading) return null; // avoid rendering until context ready
    if (!isAuthenticated) {
      // Let the session-monitor / router handle redirect. Render nothing.
      return null;
    }

    return <Component {...props} />;
  };
}

