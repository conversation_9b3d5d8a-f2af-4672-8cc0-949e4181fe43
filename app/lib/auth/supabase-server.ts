/**
 * Supabase Auth server utilities for React Router 7
 *
 * This module provides comprehensive server-side authentication utilities
 * using Supabase SSR for proper session validation across all routes.
 *
 * Implementation follows Supabase's official SSR patterns and best practices
 * for React Router applications.
 */

import type { User, Session } from '@supabase/supabase-js';
import { createServerClient } from '@supabase/ssr';
import { createClient } from '@supabase/supabase-js';
import { redirect } from 'react-router';

/**
 * Get Supabase configuration from environment variables
 */
function getSupabaseConfig() {
  const supabaseUrl = process.env.SUPABASE_URL || (import.meta as any).env?.VITE_SUPABASE_URL;
  const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || (import.meta as any).env?.VITE_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error('Missing Supabase configuration. Please set SUPABASE_URL and SUPABASE_ANON_KEY environment variables.');
  }
  
  return { supabaseUrl, supabaseAnonKey };
}

/**
 * Create a Supabase server client with proper cookie handling
 */
export function createSupabaseServerClient(request: Request, responseHeaders?: Headers) {
  const { supabaseUrl, supabaseAnonKey } = getSupabaseConfig();
  
  return createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get: (name: string) => {
        const cookieHeader = request.headers.get('Cookie');
        if (!cookieHeader) return undefined;
        
        const cookies = Object.fromEntries(
          cookieHeader
            .split(';')
            .map((c) => c.trim())
            .filter(Boolean)
            .map((c) => {
              const idx = c.indexOf('=');
              const name = idx >= 0 ? c.slice(0, idx) : c;
              const value = idx >= 0 ? c.slice(idx + 1) : '';
              return [name, decodeURIComponent(value)];
            })
        );
        
        return cookies[name];
      },
      set: (name: string, value: string, options: any) => {
        if (!responseHeaders) return;
        
        let cookie = `${name}=${encodeURIComponent(value)}`;
        
        if (options?.maxAge) {
          cookie += `; Max-Age=${options.maxAge}`;
        }
        if (options?.expires) {
          cookie += `; Expires=${options.expires.toUTCString()}`;
        }
        if (options?.path) {
          cookie += `; Path=${options.path}`;
        }
        if (options?.domain) {
          cookie += `; Domain=${options.domain}`;
        }
        if (options?.secure) {
          cookie += '; Secure';
        }
        if (options?.httpOnly) {
          cookie += '; HttpOnly';
        }
        if (options?.sameSite) {
          cookie += `; SameSite=${options.sameSite}`;
        }
        
        responseHeaders.append('Set-Cookie', cookie);
      },
      remove: (name: string, options: any) => {
        if (!responseHeaders) return;
        
        let cookie = `${name}=; Max-Age=0`;
        
        if (options?.path) {
          cookie += `; Path=${options.path}`;
        }
        if (options?.domain) {
          cookie += `; Domain=${options.domain}`;
        }
        
        responseHeaders.append('Set-Cookie', cookie);
      },
    },
  });
}

/**
 * Get the current session from the request using proper SSR validation
 */
export async function getSession(request: Request, responseHeaders?: Headers): Promise<Session | null> {
  try {
    const supabase = createSupabaseServerClient(request, responseHeaders);
    const { data: { session }, error } = await supabase.auth.getSession();
    
    if (error) {
      console.error('Session validation error:', error.message);
      return null;
    }
    
    // Validate session is not expired
    if (session && session.expires_at) {
      const expiresAt = new Date(session.expires_at * 1000);
      if (expiresAt <= new Date()) {
        console.log('Session expired, removing invalid session');
        await supabase.auth.signOut();
        return null;
      }
    }
    
    return session;
  } catch (error) {
    console.error('Error getting session:', error);
    return null;
  }
}

/**
 * Get the current user from the request using proper SSR validation
 */
export async function getUser(request: Request, responseHeaders?: Headers): Promise<User | null> {
  try {
    const supabase = createSupabaseServerClient(request, responseHeaders);
    
    // First get the session, then extract user from it
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error('Session validation error:', sessionError.message);
      return null;
    }
    
    if (!session) {
      // No session available
      return null;
    }
    
    // Validate session is not expired
    if (session.expires_at) {
      const expiresAt = new Date(session.expires_at * 1000);
      if (expiresAt <= new Date()) {
        console.log('Session expired, cannot get user');
        return null;
      }
    }
    
    // Return user from session
    return session.user;
  } catch (error) {
    console.error('Error getting user:', error);
    return null;
  }
}

/**
 * Extract Bearer token from Authorization header
 */
function getBearerToken(request: Request): string | null {
  const auth = request.headers.get('Authorization') || request.headers.get('authorization');
  if (!auth) return null;
  const match = auth.match(/^Bearer\s+(.+)$/i);
  return match ? match[1] : null;
}

/**
 * Authentication context interface for server-side use
 */
export interface AuthSession {
  user: User;
  session: Session;
}

/**
 * Require authentication for a route with proper server-side validation
 * Throws a Response with 401 status if authentication fails
 * Enhanced with session refresh and better error handling
 */
export async function requireAuthSession(request: Request, responseHeaders?: Headers): Promise<AuthSession> {
  const url = new URL(request.url);
  
  // First try to get session using SSR client
  let session = await getSession(request, responseHeaders);
  let user = await getUser(request, responseHeaders);
  
  // If session exists but is near expiry, try to refresh it
  if (session && session.expires_at) {
    const expiresAt = new Date(session.expires_at * 1000);
    const now = new Date();
    const timeUntilExpiry = expiresAt.getTime() - now.getTime();
    const fiveMinutes = 5 * 60 * 1000; // 5 minutes in milliseconds
    
    // If session expires within 5 minutes, try to refresh
    if (timeUntilExpiry < fiveMinutes && timeUntilExpiry > 0) {
      console.log('Session near expiry, attempting refresh');
      const refreshedSession = await refreshSession(request, responseHeaders);
      if (refreshedSession) {
        session = refreshedSession;
        user = await getUser(request, responseHeaders);
      }
    }
  }
  
  if (session && user) {
    return { user, session };
  }
  
  // If no session, try Bearer token validation
  const bearer = getBearerToken(request);
  if (bearer) {
    try {
      const { supabaseUrl, supabaseAnonKey } = getSupabaseConfig();
      const supabase = createSupabaseServerClient(request, responseHeaders);
      const { data, error } = await supabase.auth.getUser(bearer);
      
      if (!error && data?.user) {
        // Create a minimal session object for Bearer token auth
        const tokenSession = {
          access_token: bearer,
          token_type: 'bearer',
          user: data.user,
          expires_at: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
        } as Session;
        
        return { user: data.user, session: tokenSession };
      }
    } catch (error) {
      console.error('Bearer token validation failed:', error);
    }
  }
  
  // Authentication failed - throw appropriate response with enhanced error details
  const isApi = url.pathname.startsWith('/api');
  const errorDetails = {
    success: false,
    error: 'UNAUTHORIZED',
    message: 'Authentication required. Please sign in to continue.',
    code: 'SESSION_EXPIRED',
    timestamp: new Date().toISOString()
  };
  
  const body = JSON.stringify(errorDetails);
  
  // Add headers to help client-side error handling
  const headers = new Headers({
    'Content-Type': isApi ? 'application/json' : 'text/plain',
    'X-Auth-Error': 'session-expired',
    'X-Redirect-To': '/login'
  });
  
  throw new Response(isApi ? body : 'Unauthorized', {
    status: 401,
    headers,
  });
}

/**
 * Get user ID from request with proper server-side validation
 */
export async function getUserId(request: Request, responseHeaders?: Headers): Promise<string | null> {
  const user = await getUser(request, responseHeaders);
  return user?.id || null;
}

/**
 * Get user email from request with proper server-side validation
 */
export async function getUserEmail(request: Request, responseHeaders?: Headers): Promise<string | null> {
  const user = await getUser(request, responseHeaders);
  return user?.email || null;
}

/**
 * Check if user email is verified with proper server-side validation
 */
export async function isEmailVerified(request: Request, responseHeaders?: Headers): Promise<boolean> {
  const user = await getUser(request, responseHeaders);
  return user?.email_confirmed_at != null;
}

/**
 * Create authentication context for loaders/actions with proper validation
 */
export async function createAuthContext(request: Request, responseHeaders?: Headers) {
  const session = await getSession(request, responseHeaders);
  const user = await getUser(request, responseHeaders);
  
  return {
    session,
    user,
    isAuthenticated: !!(session && user),
    userId: user?.id || null,
  };
}

/**
 * Utility to check if a request is authenticated with proper server-side validation
 */
export async function isAuthenticated(request: Request, responseHeaders?: Headers): Promise<boolean> {
  const session = await getSession(request, responseHeaders);
  const user = await getUser(request, responseHeaders);
  return !!(session && user);
}

/**
 * Utility function to handle authentication redirects for dashboard routes
 */
export async function requireAuth(request: Request, responseHeaders?: Headers) {
  const authenticated = await isAuthenticated(request, responseHeaders);
  
  if (!authenticated) {
    const url = new URL(request.url);
    const redirectTo = encodeURIComponent(url.pathname + url.search);
    throw redirect(`/login?redirectTo=${redirectTo}`);
  }
  
  return await createAuthContext(request, responseHeaders);
}

/**
 * Legacy compatibility types for easier migration
 */
export type SessionResult = Session | null;
export type AuthContext = {
  session: SessionResult;
  user: User | null;
  isAuthenticated: boolean;
  userId: string | null;
};

/**
 * Utility function to refresh session tokens when needed
 */
export async function refreshSession(request: Request, responseHeaders?: Headers): Promise<Session | null> {
  try {
    const supabase = createSupabaseServerClient(request, responseHeaders);
    const { data: { session }, error } = await supabase.auth.refreshSession();
    
    if (error) {
      console.error('Session refresh error:', error.message);
      return null;
    }
    
    return session;
  } catch (error) {
    console.error('Error refreshing session:', error);
    return null;
  }
}
