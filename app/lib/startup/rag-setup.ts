/**
 * RAG System Startup Configuration
 * Initializes the RAG services and validates environment configuration
 */

import { log } from "../logger";
import { validateServicesConfig, SERVICES_CONFIG } from "../rag/config/services-config";
import { createEmbeddingService } from "../rag/services/embedding-service";
import { createRagSearchService } from "../rag/services/rag-search-service";
import { createVectorIndexingService } from "../rag/services/vector-indexing-service";
import type { EmbeddingService, VectorIndexingService, VectorSearchService } from "../rag/types/index";
import { getEnv, hasContext } from "../context.server";

// Global service instances
let embeddingService: EmbeddingService | null = null;
let vectorIndexingService: VectorIndexingService | null = null;
let ragSearchService: VectorSearchService | null = null;

interface RagServices {
  embeddingService: EmbeddingService;
  vectorIndexingService: VectorIndexingService;
  ragSearchService: VectorSearchService;
}

/**
 * Initialize RAG services with environment configuration
 */
export async function initializeRagServices(env?: Record<string, any>): Promise<RagServices> {
  try {
    log.info("Initializing RAG services...");

    // Get environment from context if available, fallback to provided env
    let effectiveEnv = env;
    if (hasContext()) {
      try {
        effectiveEnv = getEnv();
        log.info("Using environment from Cloudflare Workers context");
      } catch (error) {
        log.warn("Failed to get environment from context, using provided env");
      }
    }

    // 1. Validate configuration
    await validateRagConfiguration(effectiveEnv);

    // 2. Initialize embedding service
    embeddingService = createEmbeddingService(effectiveEnv);
    log.info("✅ Embedding service initialized (Voyage AI)");

    // 3. Initialize vector indexing service (requires Cloudflare Vectorize index)
    // Note: In Cloudflare Workers environment, the vectorize index will be available
    // Check for both binding name patterns
    const vectorizeIndex = effectiveEnv?.VECTORIZE_INDEX || effectiveEnv?.LEARNING_CONTENT_EMBEDDINGS;
    if (vectorizeIndex) {
      vectorIndexingService = createVectorIndexingService(vectorizeIndex, effectiveEnv);
      log.info("✅ Vector indexing service initialized (Cloudflare Vectorize)");
    } else {
      log.warn("⚠️ Vectorize index not available, skipping vector indexing service");
      vectorIndexingService = null;
    }

    // 4. Initialize RAG search service
    if (vectorIndexingService) {
      ragSearchService = createRagSearchService(vectorIndexingService);
      log.info("✅ RAG search service initialized");
    } else {
      log.warn("⚠️ RAG search service not initialized (vector indexing service unavailable)");
      ragSearchService = null;
    }

    // 5. Test basic functionality
    await testRagServices();

    log.info("🚀 RAG services initialized successfully");

    return {
      embeddingService: embeddingService!,
      vectorIndexingService: vectorIndexingService!,
      ragSearchService: ragSearchService!,
    };
  } catch (error) {
    log.error("❌ Failed to initialize RAG services:", error);
    throw error;
  }
}

/**
 * Validate RAG configuration and environment variables
 */
async function validateRagConfiguration(env?: Record<string, any>): Promise<void> {
  try {
    log.info("Validating RAG configuration...");

    // Validate services configuration with custom error handling
    try {
      validateServicesConfig();
    } catch (error) {
      // Allow configuration to proceed with warnings for missing optional components
      log.warn("RAG services configuration has warnings:", error);
    }

    // Check environment variables with fallbacks
    const voyageApiKey = env?.['VOYAGE_API_KEY'] || 
                        env?.['VOYAGE_AI_API_KEY'] || 
                        process.env.VOYAGE_API_KEY || 
                        SERVICES_CONFIG.voyageApiKey;

    const vectorizeIndexName = env?.['VECTORIZE_INDEX_NAME'] || 
                              env?.['LEARNING_CONTENT_EMBEDDINGS'] ||
                              process.env.VECTORIZE_INDEX_NAME || 
                              SERVICES_CONFIG.vectorizeIndexName;

    const warnings: string[] = [];

    if (!voyageApiKey || voyageApiKey.trim() === '') {
      warnings.push('VOYAGE_API_KEY is not configured - embedding generation will fail');
    }

    if (!vectorizeIndexName || vectorizeIndexName.trim() === '') {
      warnings.push('VECTORIZE_INDEX_NAME is not configured - vector storage will be unavailable');
    }

    if (warnings.length > 0) {
      log.warn("RAG configuration warnings:\n" + warnings.join('\n'));
      log.info("RAG services will initialize with limited functionality");
    } else {
      log.info("✅ RAG configuration validated successfully");
    }
  } catch (error) {
    log.error("❌ RAG configuration validation failed:", error);
    throw error;
  }
}

/**
 * Test basic RAG services functionality
 */
async function testRagServices(): Promise<void> {
  try {
    log.info("Testing RAG services...");

    if (!embeddingService) {
      throw new Error('Embedding service not initialized');
    }

    // Test embedding generation with a simple text
    const testText = "This is a test for RAG system initialization.";
    const embeddingResult = await embeddingService.generateEmbedding(testText);

    if (!embeddingResult.embedding || embeddingResult.embedding.length === 0) {
      throw new Error('Embedding generation test failed');
    }

    if (embeddingResult.dimensions !== SERVICES_CONFIG.defaultEmbedding.dimensions) {
      throw new Error(`Embedding dimensions mismatch: expected ${SERVICES_CONFIG.defaultEmbedding.dimensions}, got ${embeddingResult.dimensions}`);
    }

    log.info(`✅ RAG services test passed (embedding: ${embeddingResult.embedding.length} dimensions)`);
  } catch (error) {
    log.error("❌ RAG services test failed:", error);
    throw error;
  }
}

/**
 * Get initialized RAG services (singleton pattern)
 */
export function getRagServices(): RagServices | null {
  if (!embeddingService || !vectorIndexingService || !ragSearchService) {
    return null;
  }

  return {
    embeddingService,
    vectorIndexingService,
    ragSearchService,
  };
}

/**
 * Get embedding service instance
 */
export function getEmbeddingService(): EmbeddingService | null {
  return embeddingService;
}

/**
 * Get vector indexing service instance
 */
export function getVectorIndexingService(): VectorIndexingService | null {
  return vectorIndexingService;
}

/**
 * Get RAG search service instance
 */
export function getRagSearchService(): VectorSearchService | null {
  return ragSearchService;
}

/**
 * Shutdown RAG services and cleanup resources
 */
export async function shutdownRagServices(): Promise<void> {
  try {
    log.info("Shutting down RAG services...");

    // Reset service instances
    embeddingService = null;
    vectorIndexingService = null;
    ragSearchService = null;

    log.info("✅ RAG services shut down successfully");
  } catch (error) {
    log.error("❌ Error shutting down RAG services:", error);
  }
}

/**
 * Health check for RAG services
 */
export async function checkRagHealth(): Promise<{
  isHealthy: boolean;
  services: {
    embedding: boolean;
    vectorIndexing: boolean;
    ragSearch: boolean;
  };
  configuration: {
    voyageApiKey: boolean;
    vectorizeIndex: boolean;
  };
  errors: string[];
}> {
  const errors: string[] = [];
  const services = {
    embedding: false,
    vectorIndexing: false,
    ragSearch: false,
  };
  const configuration = {
    voyageApiKey: false,
    vectorizeIndex: false,
  };

  try {
    // Check configuration
    configuration.voyageApiKey = !!(SERVICES_CONFIG.voyageApiKey && SERVICES_CONFIG.voyageApiKey.trim() !== '');
    configuration.vectorizeIndex = !!(SERVICES_CONFIG.vectorizeIndexName && SERVICES_CONFIG.vectorizeIndexName.trim() !== '');

    if (!configuration.voyageApiKey) {
      errors.push('VOYAGE_API_KEY not configured');
    }

    if (!configuration.vectorizeIndex) {
      errors.push('VECTORIZE_INDEX_NAME not configured');
    }

    // Check service instances
    services.embedding = embeddingService !== null;
    services.vectorIndexing = vectorIndexingService !== null;
    services.ragSearch = ragSearchService !== null;

    if (!services.embedding) {
      errors.push('Embedding service not initialized');
    }

    if (!services.vectorIndexing) {
      errors.push('Vector indexing service not initialized');
    }

    if (!services.ragSearch) {
      errors.push('RAG search service not initialized');
    }

    // Test embedding service if available
    if (embeddingService && configuration.voyageApiKey) {
      try {
        await embeddingService.generateEmbedding("health check");
      } catch (error) {
        errors.push(`Embedding service test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

  } catch (error) {
    errors.push(`Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  return {
    isHealthy: errors.length === 0,
    services,
    configuration,
    errors,
  };
}

/**
 * Force re-initialization of RAG services
 */
export async function reinitializeRagServices(env?: Record<string, any>): Promise<void> {
  try {
    log.info("Re-initializing RAG services...");
    
    await shutdownRagServices();
    await initializeRagServices(env);
    
    log.info("✅ RAG services re-initialized successfully");
  } catch (error) {
    log.error("❌ Failed to re-initialize RAG services:", error);
    throw error;
  }
}