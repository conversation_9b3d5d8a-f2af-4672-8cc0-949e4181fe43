/**
 * Startup script for materialized view optimization
 * Initializes the background processor and applies optimized database triggers
 */

import { db } from "~/db";
import { sql } from "drizzle-orm";
import { materializedViewProcessor } from "../services/materialized-view-processor";
import { log } from "../logger";
import { readFileSync } from "fs";
import { join } from "path";

/**
 * Initialize materialized view optimization system
 */
export async function initializeMaterializedViewOptimization(): Promise<void> {
  try {
    log.info("Initializing materialized view optimization system...");

    // 1. Apply optimized database triggers
    await applyOptimizedTriggers();

    // 2. Start the background processor
    await materializedViewProcessor.start();

    log.info("Materialized view optimization system initialized successfully");
  } catch (error) {
    log.error("Failed to initialize materialized view optimization system:", error);
    throw error;
  }
}

/**
 * Shutdown materialized view optimization system
 */
export async function shutdownMaterializedViewOptimization(): Promise<void> {
  try {
    log.info("Shutting down materialized view optimization system...");
    
    await materializedViewProcessor.stop();
    
    log.info("Materialized view optimization system shut down successfully");
  } catch (error) {
    log.error("Error shutting down materialized view optimization system:", error);
  }
}

/**
 * Apply optimized database triggers
 */
async function applyOptimizedTriggers(): Promise<void> {
  try {
    log.info("Applying optimized database triggers...");

    // Read the optimized triggers SQL file
    const sqlFilePath = join(process.cwd(), "create_optimized_database_triggers.sql");
    
    let sqlContent: string;
    try {
      sqlContent = readFileSync(sqlFilePath, "utf-8");
    } catch (error) {
      log.warn("Optimized triggers SQL file not found, skipping trigger optimization");
      return;
    }

    // Split SQL content into individual statements
    const statements = sqlContent
      .split(";")
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith("--"));

    // Execute each statement
    for (const statement of statements) {
      try {
        await db.execute(sql.raw(statement));
      } catch (error) {
        // Log warning but continue with other statements
        log.warn(`Failed to execute SQL statement: ${statement.substring(0, 100)}...`, error);
      }
    }

    log.info("Optimized database triggers applied successfully");
  } catch (error) {
    log.error("Failed to apply optimized database triggers:", error);
    // Don't throw here - the system can still work with the old triggers
  }
}

/**
 * Check if the optimization system is healthy
 */
export async function checkMaterializedViewHealth(): Promise<{
  isHealthy: boolean;
  queueStatus: any;
  lastRefresh: Date | null;
  errors: string[];
}> {
  const errors: string[] = [];
  let queueStatus = null;
  let lastRefresh = null;

  try {
    // Check queue status
    queueStatus = await materializedViewProcessor.getQueueStatus();
    
    // Check if there are any stuck processing items
    if (queueStatus.processing_count > 0) {
      const processingTime = queueStatus.last_completed_at 
        ? Date.now() - queueStatus.last_completed_at.getTime()
        : Date.now();
      
      if (processingTime > 300000) { // 5 minutes
        errors.push("Materialized view refresh has been processing for too long");
      }
    }

    // Check for recent failures
    if (queueStatus.last_failed_at) {
      const timeSinceFailure = Date.now() - queueStatus.last_failed_at.getTime();
      if (timeSinceFailure < 300000) { // 5 minutes
        errors.push("Recent materialized view refresh failure detected");
      }
    }

    lastRefresh = queueStatus.last_completed_at;

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    errors.push(`Failed to check materialized view health: ${errorMessage}`);
  }

  return {
    isHealthy: errors.length === 0,
    queueStatus,
    lastRefresh,
    errors,
  };
}

/**
 * Force refresh the materialized view (for emergency use)
 */
export async function forceRefreshMaterializedView(): Promise<void> {
  try {
    log.info("Force refreshing materialized view...");
    
    const processedCount = await materializedViewProcessor.forceProcess();
    
    if (processedCount === 0) {
      // No queued items, perform immediate refresh
      await db.execute(sql`REFRESH MATERIALIZED VIEW CONCURRENTLY user_points_leaderboard_mv`);
      log.info("Force refresh completed (immediate)");
    } else {
      log.info(`Force refresh completed (processed ${processedCount} queued items)`);
    }
  } catch (error) {
    log.error("Force refresh failed:", error);
    throw error;
  }
}