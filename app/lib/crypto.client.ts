// Client-side decryption utility
// Note: The encryption key should be derived from a secure source
// In a real application, you might want to fetch this from a secure endpoint
// or derive it from user session data

// Secure per-quiz key management (no hardcoded keys)
// We use an ephemeral ECDH key exchange with the server to obtain a wrapped per-quiz key.

const quizKeyCache: Map<string, { key: CryptoKey; expiresAt: number }> = new Map();

async function generateClientECDH(): Promise<CryptoKeyPair> {
  return crypto.subtle.generateKey({ name: 'ECDH', namedCurve: 'P-256' }, true, ['deriveKey', 'deriveBits']);
}

async function deriveKekFromServerPub(
  quizId: string,
  clientPrivateKey: CryptoKey,
  serverPublicKeyJwk: JsonWebKey
): Promise<CryptoKey> {
  const serverPubKey = await crypto.subtle.importKey(
    'jwk',
    serverPublicKeyJwk,
    { name: 'ECDH', namedCurve: 'P-256' },
    false,
    []
  );
  const sharedSecret = await crypto.subtle.deriveBits({ name: 'ECDH', public: serverPubKey }, clientPrivateKey, 256);
  const ikmKey = await crypto.subtle.importKey('raw', sharedSecret, { name: 'HKDF' }, false, ['deriveKey']);
  const hkdfSalt = new TextEncoder().encode(`quiz-kek-salt:${quizId}`);
  const hkdfInfo = new TextEncoder().encode('kwaci-learning-quiz-kek');
  return crypto.subtle.deriveKey(
    { name: 'HKDF', salt: hkdfSalt, info: hkdfInfo, hash: 'SHA-256' },
    ikmKey,
    { name: 'AES-GCM', length: 256 },
    false,
    ['decrypt']
  );
}

async function base64ToBytes(b64: string): Promise<Uint8Array> {
  const bin = atob(b64);
  const bytes = new Uint8Array(bin.length);
  for (let i = 0; i < bin.length; i++) bytes[i] = bin.charCodeAt(i);
  return bytes;
}

async function fetchQuizDecryptionKey(quizId: string): Promise<CryptoKey> {
  // Reuse cached key if valid
  const now = Date.now();
  const cached = quizKeyCache.get(quizId);
  if (cached && cached.expiresAt > now + 5_000) return cached.key; // 5s safety

  // Generate client ephemeral ECDH and send public key to server
  const clientKeyPair = await generateClientECDH();
  const clientPublicKeyJwk = await crypto.subtle.exportKey('jwk', clientKeyPair.publicKey);

  const res = await fetch(`/api/quiz-key/${quizId}`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
      body: JSON.stringify({ clientPublicKeyJwk }),
    }
  );
  if (!res.ok) throw new Error(`Failed to fetch quiz key: ${res.status}`);
  const json = await res.json();
  if (!json.success) throw new Error(json.error || 'Key exchange failed');

  const { wrappedKey, iv, serverPublicKeyJwk, expiresAt } = json.data;
  const kek = await deriveKekFromServerPub(quizId, clientKeyPair.privateKey, serverPublicKeyJwk);

  const ivBytes = await base64ToBytes(iv);
  const wrappedBytes = await base64ToBytes(wrappedKey);
  const ivBuf = ivBytes as unknown as BufferSource;
  const wrappedBuf = wrappedBytes as unknown as BufferSource;
  const rawKey = await crypto.subtle.decrypt({ name: 'AES-GCM', iv: ivBuf }, kek, wrappedBuf);
  const contentKey = await crypto.subtle.importKey('raw', rawKey, { name: 'AES-GCM' }, false, ['decrypt']);

  const exp = expiresAt ? Date.parse(expiresAt) : now + 5 * 60 * 1000;
  quizKeyCache.set(quizId, { key: contentKey, expiresAt: exp });
  return contentKey;
}

async function getQuizKey(quizId: string): Promise<CryptoKey> {
  return fetchQuizDecryptionKey(quizId);
}

// Decrypt answer data on client side
export async function decryptAnswerData(encryptedData: string, quizId: string): Promise<any> {
  try {
    const key = await getQuizKey(quizId);
    const combined = new Uint8Array(atob(encryptedData).split('').map(c => c.charCodeAt(0)));
    
    const iv = combined.slice(0, 12);
    const encrypted = combined.slice(12);
    
    const decrypted = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: (iv as unknown as BufferSource)
      },
      key,
      (encrypted as unknown as BufferSource)
    );
    
    const decodedData = new TextDecoder().decode(decrypted);
    return JSON.parse(decodedData);
  } catch (error) {
    console.error('Client decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
}

// Decrypt specific answer fields based on question type
export async function decryptQuestionAnswers(question: any, quizId: string): Promise<any> {
  const decryptedQuestion = { ...question };
  
  try {
    switch (question.type) {
      case 'multipleChoice':
        if (typeof question.correctAnswerIndex === 'string') {
          decryptedQuestion.correctAnswerIndex = await decryptAnswerData(question.correctAnswerIndex, quizId);
        }
        break;
        
      case 'trueFalse':
        if (typeof question.correctAnswer === 'string') {
          decryptedQuestion.correctAnswer = await decryptAnswerData(question.correctAnswer, quizId);
        }
        break;
        
      case 'fillInBlank':
        if (typeof question.blanks === 'string') {
          decryptedQuestion.blanks = await decryptAnswerData(question.blanks, quizId);
        }
        break;
        
      case 'matching':
          
          // Check if pairs is already an array (not encrypted)
          if (Array.isArray(question.pairs)) {
            console.log('Pairs already an array, no decryption needed:', question.pairs);
            decryptedQuestion.pairs = question.pairs;
          } else if (typeof question.pairs === 'string') {
            try {
              console.log('Starting decryption of pairs string...');
              const decryptedPairs = await decryptAnswerData(question.pairs, quizId);
              
              decryptedQuestion.pairs = decryptedPairs;
            } catch (error) {
              console.error('Error decrypting matching pairs:', error);
              // Fallback to original pairs if decryption fails
              decryptedQuestion.pairs = question.pairs;
            }
          } else {
            console.log('Pairs is neither array nor string, using as-is:', question.pairs);
            decryptedQuestion.pairs = question.pairs;
          }
          break;
        
      case 'ordering':
        if (typeof question.correctOrder === 'string') {
          decryptedQuestion.correctOrder = await decryptAnswerData(question.correctOrder, quizId);
        }
        break;
        
      case 'freeText':
        if (typeof question.sampleAnswer === 'string') {
          decryptedQuestion.sampleAnswer = await decryptAnswerData(question.sampleAnswer, quizId);
        }
        break;
        
      case 'flashcard':
        if (typeof question.back === 'string') {
          decryptedQuestion.back = await decryptAnswerData(question.back, quizId);
        }
        break;
    }
  } catch (error) {
    console.error('Error decrypting question answers:', error);
    // Return original question if decryption fails
    return question;
  }
  
  return decryptedQuestion;
}

// Utility to check if a value is encrypted (base64 string)
export function isEncrypted(value: any): boolean {
  if (typeof value !== 'string') return false;
  
  try {
    // Check if it's a valid base64 string
    const decoded = atob(value);
    return decoded.length > 12; // Should have at least IV (12 bytes) + some data
  } catch {
    return false;
  }
}

// Decrypt answer fields from submit-answer response
export async function decryptAnswerFields(answerFields: Record<string, any>, quizId: string): Promise<Record<string, any>> {
  const decrypted: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(answerFields)) {
    if (isEncrypted(value)) {
      try {
        decrypted[key] = await decryptAnswerData(value, quizId);
      } catch (error) {
        console.error(`Failed to decrypt ${key}:`, error);
        decrypted[key] = value; // Keep original if decryption fails
      }
    } else {
      decrypted[key] = value;
    }
  }
  
  return decrypted;
}