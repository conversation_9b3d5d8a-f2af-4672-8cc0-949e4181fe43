/**
 * Background service to process queued materialized view refreshes
 * This service listens for PostgreSQL notifications and processes refresh requests
 * with proper debouncing and throttling
 */

import { db } from "~/db";
import { sql } from "drizzle-orm";
import { log } from "../logger";

interface RefreshQueueStatus {
  pending_count: number;
  processing_count: number;
  last_completed_at: Date | null;
  last_failed_at: Date | null;
}

class MaterializedViewProcessor {
  private isProcessing = false;
  private processInterval: NodeJS.Timeout | null = null;
  private notificationListener: any = null;
  private readonly PROCESS_INTERVAL_MS = 5000; // Process queue every 5 seconds
  private readonly MAX_RETRIES = 3;

  /**
   * Start the background processor
   */
  async start(): Promise<void> {
    if (this.isProcessing) {
      log.warn("MaterializedViewProcessor is already running");
      return;
    }

    this.isProcessing = true;
    log.info("Starting MaterializedViewProcessor");

    // Start periodic processing
    this.processInterval = setInterval(() => {
      this.processQueue().catch((error) => {
        log.error("Error in periodic queue processing:", error);
      });
    }, this.PROCESS_INTERVAL_MS);

    // Set up PostgreSQL notification listener
    await this.setupNotificationListener();

    // Process any existing queue items
    await this.processQueue();

    log.info("MaterializedViewProcessor started successfully");
  }

  /**
   * Stop the background processor
   */
  async stop(): Promise<void> {
    if (!this.isProcessing) {
      return;
    }

    log.info("Stopping MaterializedViewProcessor");
    this.isProcessing = false;

    if (this.processInterval) {
      clearInterval(this.processInterval);
      this.processInterval = null;
    }

    if (this.notificationListener) {
      await this.notificationListener.close();
      this.notificationListener = null;
    }

    log.info("MaterializedViewProcessor stopped");
  }

  /**
   * Set up PostgreSQL LISTEN/NOTIFY for real-time processing
   */
  private async setupNotificationListener(): Promise<void> {
    try {
      // Note: This is a simplified version. In a real implementation,
      // you might want to use a dedicated PostgreSQL connection for LISTEN/NOTIFY
      // For now, we'll rely on the periodic processing
      log.info("Notification listener setup (using periodic processing)");
    } catch (error) {
      log.error("Failed to setup notification listener:", error);
    }
  }

  /**
   * Process queued materialized view refresh requests
   */
  async processQueue(): Promise<void> {
    if (!this.isProcessing) {
      return;
    }

    try {
      const startTime = Date.now();
      
      // Call the database function to process queued refreshes
      const result = await db.execute(
        sql`SELECT process_queued_mv_refreshes() as processed_count`
      );
      
      const processedCount = result[0]?.processed_count || 0;
      const duration = Date.now() - startTime;

      if (processedCount > 0) {
        log.info(`Processed ${processedCount} materialized view refresh(es) in ${duration}ms`);
      }

      // Log queue status periodically
      if (Math.random() < 0.1) { // 10% chance to log status
        await this.logQueueStatus();
      }

    } catch (error) {
      log.error("Error processing materialized view refresh queue:", error);
    }
  }

  /**
   * Get current queue status
   */
  async getQueueStatus(): Promise<RefreshQueueStatus> {
    try {
      const result = await db.execute(
        sql`SELECT * FROM get_mv_refresh_queue_status()`
      );
      
      const row = result[0];
      return {
        pending_count: Number(row?.pending_count || 0),
        processing_count: Number(row?.processing_count || 0),
        last_completed_at: row?.last_completed_at ? new Date(row.last_completed_at) : null,
        last_failed_at: row?.last_failed_at ? new Date(row.last_failed_at) : null,
      };
    } catch (error) {
      log.error("Error getting queue status:", error);
      return {
        pending_count: 0,
        processing_count: 0,
        last_completed_at: null,
        last_failed_at: null,
      };
    }
  }

  /**
   * Log current queue status
   */
  private async logQueueStatus(): Promise<void> {
    try {
      const status = await this.getQueueStatus();
      
      if (status.pending_count > 0 || status.processing_count > 0) {
        log.info(`MV Refresh Queue Status: ${status.pending_count} pending, ${status.processing_count} processing`);
      }

      if (status.last_failed_at) {
        const timeSinceFailure = Date.now() - status.last_failed_at.getTime();
        if (timeSinceFailure < 60000) { // Less than 1 minute ago
          log.warn(`Recent materialized view refresh failure at ${status.last_failed_at.toISOString()}`);
        }
      }
    } catch (error) {
      log.error("Error logging queue status:", error);
    }
  }

  /**
   * Force process the queue immediately (for testing/debugging)
   */
  async forceProcess(): Promise<number> {
    try {
      const result = await db.execute(
        sql`SELECT process_queued_mv_refreshes() as processed_count`
      );
      
      const processedCount = result[0]?.processed_count || 0;
      log.info(`Force processed ${processedCount} materialized view refresh(es)`);
      
      return processedCount;
    } catch (error) {
      log.error("Error force processing queue:", error);
      throw error;
    }
  }

  /**
   * Clear failed queue items (for maintenance)
   */
  async clearFailedItems(): Promise<number> {
    try {
      const result = await db.execute(
        sql`
          DELETE FROM materialized_view_refresh_queue 
          WHERE view_name = 'user_points_leaderboard_mv' 
            AND status = 'failed' 
            AND processed_at < NOW() - INTERVAL '1 hour'
          RETURNING id
        `
      );
      
      const clearedCount = result.length;
      if (clearedCount > 0) {
        log.info(`Cleared ${clearedCount} failed materialized view refresh items`);
      }
      
      return clearedCount;
    } catch (error) {
      log.error("Error clearing failed queue items:", error);
      throw error;
    }
  }
}

// Export singleton instance
export const materializedViewProcessor = new MaterializedViewProcessor();

// Export types
export type { RefreshQueueStatus };