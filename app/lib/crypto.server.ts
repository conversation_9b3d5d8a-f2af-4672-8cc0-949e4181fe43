import { webcrypto } from 'node:crypto';

// Use Node.js crypto for server-side operations
const crypto = webcrypto as Crypto;

// Environment variable for encryption key (should be set in production)
const ENCRYPTION_KEY = process.env.QUIZ_ENCRYPTION_KEY || 'default-key-for-development-only-change-in-production';

// Generate a consistent key from the environment variable
async function getEncryptionKey(): Promise<CryptoKey> {
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    new TextEncoder().encode(ENCRYPTION_KEY.padEnd(32, '0').slice(0, 32)),
    { name: 'PBKDF2' },
    false,
    ['deriveKey']
  );

  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: new TextEncoder().encode('quiz-salt'),
      iterations: 100000,
      hash: 'SHA-256'
    },
    keyMaterial,
    { name: 'AES-GCM', length: 256 },
    false,
    ['encrypt', 'decrypt']
  );
}

// Generate base key material from env (server-only)
async function getBaseKeyMaterial(): Promise<ArrayBuffer> {
  return new TextEncoder().encode(ENCRYPTION_KEY.padEnd(32, '0').slice(0, 32)).buffer;
}

// Derive deterministic per-quiz AES-GCM key from master key using HMAC-SHA256
export async function deriveQuizKey(quizId: string): Promise<CryptoKey> {
  const masterHmacKey = await crypto.subtle.importKey(
    'raw',
    await getBaseKeyMaterial(),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  const digest = await crypto.subtle.sign('HMAC', masterHmacKey, new TextEncoder().encode(`quiz:${quizId}`));
  return crypto.subtle.importKey('raw', digest, { name: 'AES-GCM' }, false, ['encrypt', 'decrypt']);
}

// Return raw per-quiz key bytes (for wrapping/transport). Keep usage minimal.
export async function deriveQuizKeyBytes(quizId: string): Promise<Uint8Array> {
  const masterHmacKey = await crypto.subtle.importKey(
    'raw',
    await getBaseKeyMaterial(),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  const digest = await crypto.subtle.sign('HMAC', masterHmacKey, new TextEncoder().encode(`quiz:${quizId}`));
  return new Uint8Array(digest);
}


// Encrypt answer data
export async function encryptAnswerData(data: any, opts?: { quizId?: string }): Promise<string> {
  try {
    const key = await getEncryptionKey();
    const iv = crypto.getRandomValues(new Uint8Array(12)); // 96-bit IV for AES-GCM
    const encodedData = new TextEncoder().encode(JSON.stringify(data));

    const encrypted = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: iv
      },
      opts?.quizId ? await deriveQuizKey(opts.quizId) : key,
      encodedData
    );

    // Combine IV and encrypted data
    const combined = new Uint8Array(iv.length + encrypted.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(encrypted), iv.length);

    // Return as base64 string
    return btoa(String.fromCharCode(...combined));
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
}

// Decrypt answer data (for server-side validation if needed)
export async function decryptAnswerData(encryptedData: string, opts?: { quizId?: string }): Promise<any> {
  try {
    const key = await getEncryptionKey();
    const combined = new Uint8Array(atob(encryptedData).split('').map(c => c.charCodeAt(0)));

    const iv = combined.slice(0, 12);
    const encrypted = combined.slice(12);

    const decrypted = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: iv
      },
      opts?.quizId ? await deriveQuizKey(opts.quizId) : key,
      encrypted
    );

    const decodedData = new TextDecoder().decode(decrypted);
    return JSON.parse(decodedData);
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
}

// Encrypt specific answer fields based on question type
export async function encryptQuestionAnswers(question: any, opts?: { quizId?: string }): Promise<any> {
  const encryptedQuestion = { ...question };

  switch (question.type) {
    case 'multipleChoice':
      if (question.correctAnswerIndex !== undefined) {
        encryptedQuestion.correctAnswerIndex = await encryptAnswerData(question.correctAnswerIndex, opts);
      }
      break;

    case 'trueFalse':
      if (question.correctAnswer !== undefined) {
        encryptedQuestion.correctAnswer = await encryptAnswerData(question.correctAnswer, opts);
      }
      break;

    case 'fillInBlank':
      if (question.blanks) {
        encryptedQuestion.blanks = await encryptAnswerData(question.blanks, opts);
      }
      break;

    case 'matching':
      if (question.pairs) {
        encryptedQuestion.pairs = await encryptAnswerData(question.pairs, opts);
      }
      break;

    case 'ordering':
      if (question.correctOrder) {
        encryptedQuestion.correctOrder = await encryptAnswerData(question.correctOrder, opts);
      }
      break;

    case 'freeText':
      if (question.sampleAnswer) {
        encryptedQuestion.sampleAnswer = await encryptAnswerData(question.sampleAnswer, opts);
      }
      break;

    case 'flashcard':
      if (question.back) {
        encryptedQuestion.back = await encryptAnswerData(question.back, opts);
      }
      break;
  }

  return encryptedQuestion;
}

// Decrypt specific answer fields based on question type (server-side)
export async function decryptQuestionAnswers(question: any, opts?: { quizId?: string }): Promise<any> {
  const decryptedQuestion = { ...question };

  try {
    switch (question.type) {
      case 'multipleChoice':
        if (typeof question.correctAnswerIndex === 'string') {
          decryptedQuestion.correctAnswerIndex = await decryptAnswerData(question.correctAnswerIndex, opts);
        }
        break;

      case 'trueFalse':
        if (typeof question.correctAnswer === 'string') {
          decryptedQuestion.correctAnswer = await decryptAnswerData(question.correctAnswer, opts);
        }
        break;

      case 'fillInBlank':
        if (typeof question.blanks === 'string') {
          decryptedQuestion.blanks = await decryptAnswerData(question.blanks, opts);
        }
        break;

      case 'matching':
        if (typeof question.pairs === 'string') {
          decryptedQuestion.pairs = await decryptAnswerData(question.pairs, opts);
        }
        break;

      case 'ordering':
        if (typeof question.correctOrder === 'string') {
          decryptedQuestion.correctOrder = await decryptAnswerData(question.correctOrder, opts);
        }
        break;

      case 'freeText':
        if (typeof question.sampleAnswer === 'string') {
          decryptedQuestion.sampleAnswer = await decryptAnswerData(question.sampleAnswer, opts);
        }
        break;

      case 'flashcard':
        if (typeof question.back === 'string') {
          decryptedQuestion.back = await decryptAnswerData(question.back, opts);
        }
        break;
    }
  } catch (error) {
    console.error('Error decrypting question answers:', error);
    // Return original question if decryption fails
    return question;
  }

  return decryptedQuestion;
}