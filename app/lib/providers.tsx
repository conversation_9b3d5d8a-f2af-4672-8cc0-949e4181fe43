import { ThemeProvider } from "~/lib/theme/theme-provider"
import { QueryProvider } from "~/lib/query-provider"
import { AuthProvider } from "~/lib/auth/auth-provider"
import { TooltipProvider } from "~/components/ui/tooltip"

interface ProvidersProps {
  children: React.ReactNode
}

export function Providers({ children }: ProvidersProps) {
  return (
    <TooltipProvider delayDuration={0}>
      <QueryProvider>
        <AuthProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange={false}
          >
            {children}
          </ThemeProvider>
        </AuthProvider>
      </QueryProvider>
    </TooltipProvider>
  )
}