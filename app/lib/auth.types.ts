/**
 * TypeScript type definitions for the authentication system
 * Updated for Supabase Auth integration
 */

import type { User as SupabaseUser, Session as SupabaseSession } from '@supabase/supabase-js';

/**
 * User information from Supabase Auth
 */
export type User = SupabaseUser;

/**
 * Session information from Supabase Auth
 */
export type Session = SupabaseSession;

/**
 * Auth context type for React components
 */
export interface AuthContextType {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
}

/**
 * Session result from getSession function
 * Can be null if user is not authenticated
 */
export type SessionResult = Session | null;

/**
 * Context for React Router integration
 */
export interface AuthContext {
  session: SessionResult;
  isAuthenticated: boolean;
  userId: string | null;
  user: User | null;
}

/**
 * Supabase configuration options
 */
export interface SupabaseConfig {
  url: string;
  anonKey: string;
}

/**
 * Environment variables required for Supabase authentication
 */
export interface AuthEnvironment {
  SUPABASE_URL: string;
  SUPABASE_ANON_KEY: string;
  VITE_SUPABASE_URL: string;
  VITE_SUPABASE_ANON_KEY: string;
  DATABASE_URL: string;
}

/**
 * Legacy Better Auth types (for migration reference)
 * @deprecated Use Supabase types instead
 */
export interface LegacyAuthSession {
  session: {
    id: string;
    userId: string;
    expiresAt: Date;
    token: string;
    createdAt: Date;
    updatedAt: Date;
    ipAddress?: string | null;
    userAgent?: string | null;
  };
  user: {
    id: string;
    email: string;
    name: string;
    avatar?: string | null;
    emailVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
  };
}