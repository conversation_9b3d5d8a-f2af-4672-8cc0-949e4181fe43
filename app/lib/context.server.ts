/**
 * Global context store for Cloudflare Workers using AsyncLocalStorage
 * 
 * This provides request-scoped global access to environment variables
 * without needing to pass context through every function call.
 */

import { AsyncLocalStorage } from 'async_hooks';
import type { ExecutionContext } from '@cloudflare/workers-types';

/**
 * Hyperdrive binding interface
 */
export interface HyperdriveBinding {
  connectionString: string;
}

/**
 * Cloudflare Workers environment interface
 */
export interface CloudflareEnv {
  DATABASE_URL?: string;
  HYPERDRIVE?: HyperdriveBinding;
  BETTER_AUTH_SECRET?: string;
  BETTER_AUTH_URL?: string;
  BETTER_AUTH_TRUSTED_ORIGINS?: string;
  [key: string]: any;
}

/**
 * Complete Cloudflare Workers context
 */
export interface CloudflareContext {
  env: CloudflareEnv;
  ctx: ExecutionContext;
  request: Request;
}

/**
 * AsyncLocalStorage instance for storing Cloudflare Workers context
 */
const contextStore = new AsyncLocalStorage<CloudflareContext>();

/**
 * Get the current Cloudflare Workers environment variables
 * 
 * @returns Environment variables from the current request context
 * @throws Error if called outside of a request context
 */
export function getEnv(): CloudflareEnv {
  const context = contextStore.getStore();
  if (!context) {
    throw new Error(
      'Cloudflare Workers context not available. ' +
      'Make sure this is called within a request handler that uses runWithContext.'
    );
  }
  return context.env;
}

/**
 * Get the current Cloudflare Workers execution context
 * 
 * @returns Execution context from the current request
 * @throws Error if called outside of a request context
 */
export function getExecutionContext(): ExecutionContext {
  const context = contextStore.getStore();
  if (!context) {
    throw new Error(
      'Cloudflare Workers context not available. ' +
      'Make sure this is called within a request handler that uses runWithContext.'
    );
  }
  return context.ctx;
}

/**
 * Get the current request object
 * 
 * @returns Request object from the current request context
 * @throws Error if called outside of a request context
 */
export function getRequest(): Request {
  const context = contextStore.getStore();
  if (!context) {
    throw new Error(
      'Cloudflare Workers context not available. ' +
      'Make sure this is called within a request handler that uses runWithContext.'
    );
  }
  return context.request;
}

/**
 * Get the complete Cloudflare Workers context
 * 
 * @returns Complete context object
 * @throws Error if called outside of a request context
 */
export function getContext(): CloudflareContext {
  const context = contextStore.getStore();
  if (!context) {
    throw new Error(
      'Cloudflare Workers context not available. ' +
      'Make sure this is called within a request handler that uses runWithContext.'
    );
  }
  return context;
}

/**
 * Run a function within the Cloudflare Workers context
 * 
 * This should be called at the top level of your worker's fetch handler
 * to make the context available to all subsequent function calls.
 * 
 * @param context - Cloudflare Workers context (env, ctx, request)
 * @param fn - Function to execute within the context
 * @returns Result of the function execution
 */
export function runWithContext<T>(
  context: CloudflareContext,
  fn: () => T | Promise<T>
): T | Promise<T> {
  return contextStore.run(context, fn);
}

/**
 * Check if we're currently running within a Cloudflare Workers context
 * 
 * @returns True if context is available, false otherwise
 */
export function hasContext(): boolean {
  return contextStore.getStore() !== undefined;
}

/**
 * Safely get environment variable with fallback
 * 
 * @param key - Environment variable key
 * @param fallback - Fallback value if not found
 * @returns Environment variable value or fallback
 */
export function getEnvVar(key: string, fallback?: string): string | undefined {
  try {
    const env = getEnv();
    return env[key] || fallback;
  } catch {
    return fallback;
  }
}

/**
 * Get environment variable or throw if not found
 * 
 * @param key - Environment variable key
 * @param errorMessage - Custom error message
 * @returns Environment variable value
 * @throws Error if variable is not found
 */
export function requireEnvVar(key: string, errorMessage?: string): string {
  const value = getEnvVar(key);
  if (!value) {
    throw new Error(
      errorMessage || 
      `Required environment variable ${key} is not set. ` +
      `Please configure it in your Cloudflare Workers environment.`
    );
  }
  return value;
}
