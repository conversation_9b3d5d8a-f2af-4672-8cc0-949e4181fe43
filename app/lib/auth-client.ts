/**
 * Supabase Auth client configuration for React Router 7
 *
 * This module provides client-side authentication utilities
 * for React components and hooks using Supabase Auth.
 */

import { createBrowserClient } from '@supabase/ssr';

/**
 * Get environment variables with fallbacks
 */
function getSupabaseConfig() {
  const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL;
  const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Missing Supabase configuration. Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY environment variables.');
    throw new Error('Missing Supabase configuration');
  }

  return { supabaseUrl, supabaseAnonKey };
}

/**
 * Supabase client instance
 * This provides all authentication functionality through Supabase Auth
 */
export const supabase = (() => {
  try {
    const { supabaseUrl, supabase<PERSON>nonKey } = getSupabaseConfig();

    const client = createBrowserClient(supabaseUrl, supabaseAnonKey, {
      cookies: {
        get: (key: string) => {
          if (typeof document === 'undefined') return undefined;
          const match = document.cookie.match(new RegExp('(?:^|; )' + key.replace(/([.$?*|{}()\[\]\\\/\+^])/g, '\\$1') + '=([^;]*)'));
          return match ? decodeURIComponent(match[1]) : undefined;
        },
        set: (key: string, value: string, options?: any) => {
          if (typeof document === 'undefined') return;
          let cookie = `${key}=${encodeURIComponent(value)}; Path=${options?.path ?? '/'};`;
          if (options?.domain) cookie += ` Domain=${options.domain};`;
          if (options?.maxAge) cookie += ` Max-Age=${options.maxAge};`;
          if (options?.expires) cookie += ` Expires=${options.expires.toUTCString()};`;
          if (options?.sameSite) cookie += ` SameSite=${options.sameSite};`;
          if (options?.secure ?? window.location.protocol === 'https:') cookie += ' Secure;';
          document.cookie = cookie;
        },
        remove: (key: string, options?: any) => {
          if (typeof document === 'undefined') return;
          document.cookie = `${key}=; Path=${options?.path ?? '/'}; Max-Age=0;`;
        },
      },
    });

    console.log('Supabase client created successfully');
    return client;
  } catch (error) {
    console.error('Failed to create Supabase client:', error);
    throw error;
  }
})();

/**
 * Type exports for better TypeScript support
 */
export type SupabaseClient = typeof supabase;
export type User = NonNullable<Awaited<ReturnType<typeof supabase.auth.getUser>>['data']['user']>;
export type Session = NonNullable<Awaited<ReturnType<typeof supabase.auth.getSession>>['data']['session']>;