/**
 * Content chunking service for learning materials
 *
 * This service implements intelligent chunking strategies optimized for educational content,
 * following best practices from research on RAG and vector indexing.
 */

import type {
  ChunkingService,
  LearningContent,
  LearningContentStep,
  ContentChunk,
  ChunkMetadata,
  ChunkingStrategy
} from '../types/index';
import { ChunkingError } from '../types/index';
import { SERVICES_CONFIG, getRecommendedChunkingStrategy } from '../config/services-config';
import { estimateTokenCount } from './embedding-service';

/**
 * Learning content chunking service implementation
 */
export class LearningContentChunkingService implements ChunkingService {
  private config = SERVICES_CONFIG.chunking;

  /**
   * Chunk entire learning content into optimized chunks
   */
  async chunkContent(
    content: LearningContent,
    strategy?: ChunkingStrategy
  ): Promise<ContentChunk[]> {
    try {
      const chunks: ContentChunk[] = [];

      for (const step of content.steps) {
        const stepChunks = await this.chunkStep(step, strategy);
        chunks.push(...stepChunks);
      }

      return chunks;
    } catch (error) {
      throw new ChunkingError(`Failed to chunk content: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Chunk a single learning content step
   */
  async chunkStep(
    step: LearningContentStep,
    strategy?: ChunkingStrategy
  ): Promise<ContentChunk[]> {
    try {
      const chunks: ContentChunk[] = [];
      let chunkIndex = 0;

      for (const block of step.blocks) {
        const blockText = this.extractTextFromBlock(block);
        if (!blockText.trim()) continue;

        // ENHANCEMENT: Include step title in the embedded content for better searchability
        // This allows exact step title matches to return the correct step
        const enhancedBlockText = this.enhanceTextWithStepTitle(blockText, step.title, block.type);

        // Determine chunking strategy based on block type
        const chunkingStrategy = strategy || getRecommendedChunkingStrategy(block.type);

        const blockChunks = await this.chunkText(enhancedBlockText, chunkingStrategy, {
          learningContentId: '', // Will be set by the indexing service
          stepId: step.id,
          stepTitle: step.title,
          chunkIndex,
          blockId: block.id,
          blockType: block.type,
          chunkingStrategy,
          chunkSize: this.config.defaultChunkSize,
        });

        // Update chunk indices
        blockChunks.forEach(chunk => {
          chunk.metadata.chunkIndex = chunkIndex++;
        });

        chunks.push(...blockChunks);
      }

      return chunks;
    } catch (error) {
      throw new ChunkingError(`Failed to chunk step: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Chunk raw text using specified strategy
   */
  async chunkText(
    text: string,
    strategy?: ChunkingStrategy,
    metadata?: Partial<ChunkMetadata>
  ): Promise<ContentChunk[]> {
    if (!text.trim()) {
      return [];
    }

    const chunkingStrategy = strategy || this.config.defaultStrategy;

    try {
      switch (chunkingStrategy) {
        case 'fixed-size':
          return this.chunkByFixedSize(text, metadata);
        case 'sentence-based':
          return this.chunkBySentence(text, metadata);
        case 'paragraph-based':
          return this.chunkByParagraph(text, metadata);
        case 'semantic':
          return this.chunkBySemantic(text, metadata);
        case 'content-aware':
          return this.chunkByContentAware(text, metadata);
        default:
          throw new Error(`Unknown chunking strategy: ${chunkingStrategy}`);
      }
    } catch (error) {
      throw new ChunkingError(`Failed to chunk text with strategy ${chunkingStrategy}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Fixed-size chunking with token-based splitting
   */
  private chunkByFixedSize(text: string, metadata?: Partial<ChunkMetadata>): ContentChunk[] {
    const config = this.config.strategies['fixed-size'];
    const chunks: ContentChunk[] = [];

    // Simple word-based approximation for token chunking
    const words = text.split(/\s+/);
    const wordsPerToken = 0.75; // Approximate ratio
    const wordsPerChunk = Math.floor(config.chunkSize * wordsPerToken);
    const overlapWords = Math.floor(config.chunkOverlap * wordsPerToken);

    for (let i = 0; i < words.length; i += wordsPerChunk - overlapWords) {
      const chunkWords = words.slice(i, i + wordsPerChunk);
      const chunkText = chunkWords.join(' ');

      if (chunkText.trim()) {
        chunks.push(this.createChunk(chunkText, metadata, 'fixed-size'));
      }
    }

    return chunks;
  }

  /**
   * Sentence-based chunking
   */
  private chunkBySentence(text: string, metadata?: Partial<ChunkMetadata>): ContentChunk[] {
    const config = this.config.strategies['sentence-based'];
    const sentences = this.splitIntoSentences(text);
    const chunks: ContentChunk[] = [];

    for (let i = 0; i < sentences.length; i += config.maxSentencesPerChunk - config.chunkOverlap) {
      const chunkSentences = sentences.slice(i, i + config.maxSentencesPerChunk);
      const chunkText = chunkSentences.join(' ');

      if (chunkText.trim()) {
        chunks.push(this.createChunk(chunkText, metadata, 'sentence-based'));
      }
    }

    return chunks;
  }

  /**
   * Paragraph-based chunking
   */
  private chunkByParagraph(text: string, metadata?: Partial<ChunkMetadata>): ContentChunk[] {
    const config = this.config.strategies['paragraph-based'];
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim());
    const chunks: ContentChunk[] = [];

    for (let i = 0; i < paragraphs.length; i += config.maxParagraphsPerChunk) {
      const chunkParagraphs = paragraphs.slice(i, i + config.maxParagraphsPerChunk);
      const chunkText = chunkParagraphs.join('\n\n');

      if (chunkText.trim()) {
        chunks.push(this.createChunk(chunkText, metadata, 'paragraph-based'));
      }
    }

    return chunks;
  }

  /**
   * Semantic chunking (simplified implementation)
   * In a full implementation, this would use embeddings to determine semantic boundaries
   */
  private chunkBySemantic(text: string, metadata?: Partial<ChunkMetadata>): ContentChunk[] {
    const config = this.config.strategies['semantic'];

    // For now, fall back to sentence-based chunking with semantic considerations
    const sentences = this.splitIntoSentences(text);
    const chunks: ContentChunk[] = [];
    let currentChunk: string[] = [];

    for (const sentence of sentences) {
      currentChunk.push(sentence);
      const currentText = currentChunk.join(' ');
      const tokenCount = estimateTokenCount(currentText);

      // Create chunk if we hit size limits or semantic boundaries
      if (tokenCount >= config.maxChunkSize || this.isSemanticBoundary(sentence)) {
        if (currentChunk.length > 0) {
          chunks.push(this.createChunk(currentText, metadata, 'semantic'));
          currentChunk = [];
        }
      }
    }

    // Add remaining sentences as final chunk
    if (currentChunk.length > 0) {
      chunks.push(this.createChunk(currentChunk.join(' '), metadata, 'semantic'));
    }

    return chunks;
  }

  /**
   * Content-aware chunking that respects block boundaries
   */
  private chunkByContentAware(text: string, metadata?: Partial<ChunkMetadata>): ContentChunk[] {
    const config = this.config.strategies['content-aware'];

    // If we have block information, treat each block as a semantic unit
    if (metadata?.blockType && config.respectBlockBoundaries) {
      // For structured content types, keep the entire block together if possible
      const tokenCount = estimateTokenCount(text);

      if (tokenCount <= config.chunkSize) {
        return [this.createChunk(text, metadata, 'content-aware')];
      }
    }

    // For text without block metadata, treat as a single semantic unit if small enough
    const tokenCount = estimateTokenCount(text);
    if (tokenCount <= config.chunkSize) {
      return [this.createChunk(text, metadata, 'content-aware')];
    }

    // Fall back to fixed-size chunking for large blocks, but preserve content-aware strategy
    const fixedSizeChunks = this.chunkByFixedSize(text, metadata);
    // Update strategy to reflect that this was content-aware chunking that fell back
    return fixedSizeChunks.map(chunk => ({
      ...chunk,
      metadata: {
        ...chunk.metadata,
        chunkingStrategy: 'content-aware'
      }
    }));
  }

  /**
   * Extract text content from a learning content block
   */
  private extractTextFromBlock(block: any): string {
    if (!block.data) return '';

    switch (block.type) {
      case 'paragraph':
        // Handle both formats: { text: "..." } and direct string
        return block.data.text || (typeof block.data === 'string' ? block.data : '');
      case 'infoBox':
        return `${block.data.title || ''}\n${block.data.content || ''}`;
      case 'bulletList':
      case 'numberedList':
        // Handle both formats: { items: [...] } and direct array
        const listItems = block.data.items || (Array.isArray(block.data) ? block.data : []);
        return listItems.join('\n');
      case 'grid':
        // Handle both formats: { items: [...] } and direct array
        const gridItems = block.data.items || (Array.isArray(block.data) ? block.data : []);
        return gridItems.map((item: any) =>
          `${item.title || ''}\n${item.content || item.description || ''}`
        ).join('\n\n');
      case 'comparison':
        // Handle both formats: old format with leftTitle/rightTitle and new format with array
        if (Array.isArray(block.data)) {
          return block.data.map((item: any) =>
            `${item.label || ''}: ${item.before || ''} vs ${item.after || ''}`
          ).join('\n');
        } else {
          return `${block.data.leftTitle || ''}: ${block.data.leftContent || ''}\n${block.data.rightTitle || ''}: ${block.data.rightContent || ''}`;
        }
      case 'table': {
        const headers = (block.data.headers || []).join(' | ');
        const rows = (block.data.rows || []).map((row: any[]) => row.join(' | ')).join('\n');
        return `${headers}\n${rows}`;
      }
      case 'keyValueGrid':
        // Handle both formats: { items: [...] } and direct array
        const kvItems = block.data.items || (Array.isArray(block.data) ? block.data : []);
        return kvItems.map((item: any) =>
          `${item.key || ''}: ${item.value || ''}`
        ).join('\n');
      case 'scatterPlot':
        // Extract meaningful text from chart data
        const title = block.data.title || '';
        const xLabel = block.data.xLabel || '';
        const yLabel = block.data.yLabel || '';
        return `Chart: ${title}. X-axis: ${xLabel}. Y-axis: ${yLabel}.`;
      default:
        // For unknown types, try to extract any text content
        if (typeof block.data === 'string') {
          return block.data;
        } else if (block.data && typeof block.data === 'object') {
          // Extract text from common properties
          const textFields = ['text', 'content', 'description', 'title', 'value'];
          const extractedTexts = textFields
            .map(field => block.data[field])
            .filter(text => typeof text === 'string' && text.length > 0);

          if (extractedTexts.length > 0) {
            return extractedTexts.join(' ');
          }
        }
        return JSON.stringify(block.data);
    }
  }

  /**
   * Split text into sentences using simple heuristics
   */
  private splitIntoSentences(text: string): string[] {
    return text
      .split(/[.!?]+/)
      .map(s => s.trim())
      .filter(s => s.length > 0)
      .map(s => s + '.');
  }

  /**
   * Simple heuristic to detect semantic boundaries
   */
  private isSemanticBoundary(sentence: string): boolean {
    // Look for transition words and phrases that indicate topic changes
    const transitionPatterns = [
      /^(however|nevertheless|furthermore|moreover|additionally|in contrast|on the other hand)/i,
      /^(next|then|finally|in conclusion|to summarize)/i,
      /^(first|second|third|lastly)/i,
    ];

    return transitionPatterns.some(pattern => pattern.test(sentence.trim()));
  }

  /**
   * Create a content chunk with metadata
   */
  private createChunk(
    text: string,
    metadata?: Partial<ChunkMetadata>,
    strategy?: ChunkingStrategy
  ): ContentChunk {
    const chunkId = this.generateChunkId();
    const tokens = estimateTokenCount(text);

    return {
      id: chunkId,
      text: text.trim(),
      tokens,
      metadata: {
        learningContentId: metadata?.learningContentId || '',
        stepId: metadata?.stepId || '',
        stepTitle: metadata?.stepTitle || '',
        chunkIndex: metadata?.chunkIndex || 0,
        blockId: metadata?.blockId,
        blockType: metadata?.blockType,
        chunkingStrategy: strategy || this.config.defaultStrategy,
        chunkSize: metadata?.chunkSize || this.config.defaultChunkSize,
        chunkOverlap: metadata?.chunkOverlap,
      },
    };
  }

  /**
   * Enhance block text with step title for better searchability
   *
   * This method strategically includes the step title in the embedded content
   * to ensure exact step title searches return the correct results.
   */
  private enhanceTextWithStepTitle(blockText: string, stepTitle: string, blockType?: string): string {
    // Clean step title (remove emojis and extra formatting for embedding)
    const cleanStepTitle = stepTitle.replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, '').trim();

    // For the first block of a step (usually index 0), include the step title prominently
    // This ensures step title searches find the right content
    if (blockType === 'paragraph' || blockType === 'infoBox') {
      // For paragraphs and info boxes, prepend the step title as context
      return `${cleanStepTitle}\n\n${blockText}`;
    } else {
      // For other block types, include step title as context but less prominently
      return `[Step: ${cleanStepTitle}] ${blockText}`;
    }
  }

  /**
   * Generate unique chunk ID
   */
  private generateChunkId(): string {
    return `chunk_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}

/**
 * Create chunking service instance
 */
export function createChunkingService(): ChunkingService {
  return new LearningContentChunkingService();
}