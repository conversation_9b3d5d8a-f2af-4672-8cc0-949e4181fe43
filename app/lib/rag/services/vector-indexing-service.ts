/**
 * Vector indexing service using LangChain.js and Cloudflare Vectorize
 *
 * This service provides comprehensive vector indexing capabilities for learning content,
 * including deduplication, metadata management, and optimized batch processing.
 */

import { CloudflareVectorizeStore } from '@langchain/cloudflare';
import { Document } from '@langchain/core/documents';
import type {
  VectorIndexingService,
  LearningContent,
  ContentChunk,
  IndexedContent,
  VectorMetadata,
  EmbeddingService,
  ChunkingService,
  SearchOptions,
  SearchResponse,
  SearchResult
} from '../types/index';
import { IndexingError } from '../types/index';
import { SERVICES_CONFIG } from '../config/services-config';
import { createEmbeddingService, createLangChainEmbeddingAdapter } from './embedding-service';
import { createChunkingService } from './chunking-service';

/**
 * Vector indexing service implementation
 */
export class LearningContentVectorIndexingService implements VectorIndexingService {
  private vectorStore: CloudflareVectorizeStore;
  private embeddingService: EmbeddingService;
  private chunkingService: ChunkingService;
  private config = SERVICES_CONFIG.vectorStore;

  constructor(
    vectorizeIndex: VectorizeIndex,
    embeddingService?: EmbeddingService,
    chunkingService?: ChunkingService,
    env?: Record<string, any>
  ) {
    this.embeddingService = embeddingService || createEmbeddingService(env);
    this.chunkingService = chunkingService || createChunkingService();

    // Create LangChain-compatible embedding adapter
    const langchainAdapter = createLangChainEmbeddingAdapter(env);

    // Create CloudflareVectorizeStore with LangChain-compatible adapter
    this.vectorStore = new CloudflareVectorizeStore(langchainAdapter, {
      index: vectorizeIndex as any, // Type compatibility fix for Cloudflare Vectorize
    });
  }

  /**
   * Index entire learning content
   */
  async indexContent(content: LearningContent): Promise<IndexedContent[]> {
    try {
      // Check if content is already indexed (with graceful error handling)
      const isIndexed = await this.isContentIndexed(content.id);
      if (isIndexed) {
        console.log(`Content ${content.id} is already indexed. Use updateContentIndex to refresh.`);
        return [];
      }

      console.log(`Proceeding to index content ${content.id}...`);

      // Chunk the content
      const chunks = await this.chunkingService.chunkContent(content);

      // Update chunk metadata with content ID
      chunks.forEach(chunk => {
        chunk.metadata.learningContentId = content.id;
      });

      // Index the chunks
      return await this.indexChunks(chunks);
    } catch (error) {
      throw new IndexingError(`Failed to index content ${content.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Index content chunks with deduplication
   */
  async indexChunks(chunks: ContentChunk[]): Promise<IndexedContent[]> {
    if (chunks.length === 0) {
      return [];
    }

    try {
      const indexedContent: IndexedContent[] = [];

      // Process chunks in batches
      for (let i = 0; i < chunks.length; i += this.config.batchSize) {
        const batch = chunks.slice(i, i + this.config.batchSize);
        const batchResults = await this.processBatch(batch);
        indexedContent.push(...batchResults);
      }

      return indexedContent;
    } catch (error) {
      throw new IndexingError(`Failed to index chunks: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Check if content is already indexed
   */
  async isContentIndexed(contentId: string): Promise<boolean> {
    try {
      // Search for any chunks from this content
      const searchResults = await this.vectorStore.similaritySearch(
        'test', // Dummy query
        1,
        { learningContentId: contentId }
      );

      return searchResults.length > 0;
    } catch (error) {
      // Handle authentication errors gracefully
      if (error instanceof Error) {
        if (error.message.includes('Authentication error') ||
            error.message.includes('VECTOR_QUERY_ERROR')) {
          console.warn(`Authentication error checking if content ${contentId} is indexed. Proceeding with indexing (this is normal in local development).`);
          return false; // Assume not indexed and proceed
        }
      }

      console.warn(`Error checking if content ${contentId} is indexed:`, error);
      return false; // Default to not indexed to allow indexing to proceed
    }
  }

  /**
   * Delete all indexed content for a learning content item
   */
  async deleteContentIndex(contentId: string): Promise<void> {
    try {
      // Find all vector IDs for this content
      const searchResults = await this.vectorStore.similaritySearch(
        'test', // Dummy query
        1000, // Large number to get all chunks
        { learningContentId: contentId }
      );

      if (searchResults.length === 0) {
        return; // Nothing to delete
      }

      // Extract vector IDs from metadata
      const vectorIds = searchResults
        .map((doc: any) => doc.metadata.vectorId)
        .filter((id: any) => id);

      if (vectorIds.length > 0) {
        await this.vectorStore.delete({ ids: vectorIds });
      }
    } catch (error) {
      // Handle authentication errors gracefully for delete operations
      if (error instanceof Error &&
          (error.message.includes('Authentication error') ||
           error.message.includes('VECTOR_QUERY_ERROR'))) {
        console.warn(`Authentication error during delete operation for content ${contentId}. This is normal in local development.`);
        return; // Skip deletion in case of auth errors
      }

      throw new IndexingError(`Failed to delete content index for ${contentId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update content index (delete old and create new)
   */
  async updateContentIndex(content: LearningContent): Promise<IndexedContent[]> {
    try {
      // Delete existing index
      await this.deleteContentIndex(content.id);

      // Create new index
      return await this.indexContent(content);
    } catch (error) {
      throw new IndexingError(`Failed to update content index for ${content.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Process a batch of chunks
   */
  private async processBatch(chunks: ContentChunk[]): Promise<IndexedContent[]> {
    const documents: Document[] = [];
    const indexedContent: IndexedContent[] = [];

    for (const chunk of chunks) {
      // Create LangChain document
      const document = new Document({
        pageContent: chunk.text,
        metadata: this.createVectorMetadata(chunk),
      });

      documents.push(document);
    }

    try {
      // Add documents to vector store with retry logic
      const vectorIds = await this.addDocumentsWithRetry(documents);

      // Create indexed content records
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        const vectorId = vectorIds[i];

        if (vectorId) {
          indexedContent.push({
            vectorId,
            contentId: chunk.metadata.learningContentId,
            chunkId: chunk.id,
            embedding: [], // Not stored locally
            metadata: this.createVectorMetadata(chunk),
            createdAt: new Date(),
          });
        }
      }

      return indexedContent;
    } catch (error) {
      throw new IndexingError(`Failed to process batch: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Add documents to vector store with retry logic
   */
  private async addDocumentsWithRetry(documents: Document[]): Promise<string[]> {
    const requestId = crypto.randomUUID().slice(0, 8);
    let lastError: Error | null = null;

    console.log(`[${requestId}] 📝 Adding documents to vector store`, {
      documentCount: documents.length,
      documentsPreview: documents.slice(0, 3).map((doc, index) => ({
        index: index + 1,
        contentLength: doc.pageContent.length,
        contentPreview: doc.pageContent.substring(0, 100),
        metadata: {
          learningContentId: doc.metadata.learningContentId,
          stepTitle: doc.metadata.stepTitle,
          chunkIndex: doc.metadata.chunkIndex,
        },
      })),
      totalContentLength: documents.reduce((sum, doc) => sum + doc.pageContent.length, 0),
    });

    for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
      try {
        // Generate unique IDs for documents
        const ids = documents.map(() => this.generateVectorId());

        console.log(`[${requestId}] 🎯 Attempt ${attempt}: Adding ${documents.length} documents with IDs`, {
          idsPreview: ids.slice(0, 3),
          vectorStoreType: this.vectorStore.constructor.name,
        });

        await this.vectorStore.addDocuments(documents, { ids });

        console.log(`[${requestId}] ✅ Successfully added documents to vector store`, {
          documentCount: documents.length,
          vectorIds: ids.slice(0, 3),
          attempt,
        });

        // Verify documents were added by attempting a quick search
        try {
          const testQuery = documents[0].pageContent.substring(0, 50);
          const verificationResults = await this.vectorStore.similaritySearch(testQuery, 1);
          console.log(`[${requestId}] 🔍 Verification search results`, {
            testQuery: testQuery.substring(0, 30) + '...',
            resultsFound: verificationResults.length,
            firstResultScore: verificationResults[0] ? 'Found match' : 'No match',
          });
        } catch (verifyError) {
          console.warn(`[${requestId}] ⚠️ Could not verify document addition:`, verifyError);
        }

        return ids;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        console.error(`[${requestId}] ❌ Attempt ${attempt} failed:`, {
          error: lastError.message,
          errorType: lastError.constructor.name,
          remainingAttempts: this.config.retryAttempts - attempt,
        });

        if (attempt < this.config.retryAttempts) {
          console.warn(`[${requestId}] 🔄 Retrying in ${this.config.retryDelay}ms...`);
          await this.delay(this.config.retryDelay);
        }
      }
    }

    console.error(`[${requestId}] 💥 Failed to add documents after all attempts`, {
      totalAttempts: this.config.retryAttempts,
      finalError: lastError?.message,
    });

    throw new IndexingError(`Failed to add documents after ${this.config.retryAttempts} attempts: ${lastError?.message}`);
  }

  /**
   * Create vector metadata from content chunk
   */
  private createVectorMetadata(chunk: ContentChunk): VectorMetadata {
    return {
      learningContentId: chunk.metadata.learningContentId,
      stepId: chunk.metadata.stepId,
      stepTitle: chunk.metadata.stepTitle,
      chunkIndex: chunk.metadata.chunkIndex,
      chunkText: chunk.text,
      embeddingModel: this.embeddingService.getConfig().model,
      embeddingDimension: this.embeddingService.getConfig().dimensions,
      processingMetadata: {
        chunkingStrategy: chunk.metadata.chunkingStrategy,
        chunkSize: chunk.metadata.chunkSize,
        chunkOverlap: chunk.metadata.chunkOverlap,
        tokenCount: chunk.tokens,
      },
    };
  }

  /**
   * Search content with vector similarity using direct Vectorize API
   */
  async searchContent(query: string, options: SearchOptions = {}): Promise<SearchResponse> {
    const startTime = Date.now();

    try {
      const {
        limit = 10,
        similarityThreshold = 0.7,
        includeMetadata = true
      } = options;

      // Generate embedding for the query
      const queryEmbedding = await this.embeddingService.generateEmbedding(query, 'query');

      // Use direct Vectorize API instead of LangChain wrapper
      const vectorizeIndex = (this.vectorStore as any).index;
      const directResults = await vectorizeIndex.query(queryEmbedding.embedding, {
        topK: limit,
        returnMetadata: true
      });

      // Transform direct Vectorize results to our format
      const results: SearchResult[] = directResults.matches.map((match: any) => ({
        id: match.id,
        content: match.metadata?.text || '',
        score: match.score,
        metadata: {
          learningContentId: match.metadata?.learningContentId || '',
          stepId: match.metadata?.stepId || '',
          stepTitle: match.metadata?.stepTitle || '',
          chunkIndex: match.metadata?.chunkIndex || 0,
          embeddingModel: match.metadata?.embeddingModel || '',
          embeddingDimension: match.metadata?.embeddingDimension || 0,
        },
      }));

      // Filter by similarity threshold if specified
      const filteredResults = similarityThreshold > 0
        ? results.filter(result => result.score >= similarityThreshold)
        : results;

      const processingTimeMs = Date.now() - startTime;

      return {
        results: filteredResults,
        query,
        totalResults: filteredResults.length,
        processingTimeMs,
      };
    } catch (error) {
      // Handle authentication errors gracefully for search operations
      if (error instanceof Error &&
          (error.message.includes('Authentication error') ||
           error.message.includes('VECTOR_QUERY_ERROR'))) {
        console.warn(`Authentication error during search operation. Returning empty results. This is normal in local development.`);
        return {
          results: [],
          query,
          totalResults: 0,
          processingTimeMs: Date.now() - startTime,
        };
      }

      throw new IndexingError(`Failed to search content: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Search content scoped to specific learning content IDs using direct Vectorize API
   *
   * WORKAROUND: Due to Cloudflare Vectorize metadata filtering bug, this method
   * uses unfiltered search + post-processing to achieve content scoping.
   */
  async searchContentScoped(query: string, contentIds: string[], options: SearchOptions = {}): Promise<SearchResponse> {
    const startTime = Date.now();
    const requestId = crypto.randomUUID().slice(0, 8);

    try {
      const {
        limit = 10,
        similarityThreshold = 0.7,
        includeMetadata = true
      } = options;

      console.log(`[${requestId}] 🔍 Vector Search Debug - Starting scoped search`, {
        query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
        contentIds,
        limit,
        similarityThreshold,
        queryLength: query.length,
      });

      // If no content IDs provided, fall back to regular search
      if (!contentIds || contentIds.length === 0) {
        console.log(`[${requestId}] ⚠️ No content IDs provided, falling back to regular search`);
        return this.searchContent(query, options);
      }

      // Generate embedding for the query
      const queryEmbedding = await this.embeddingService.generateEmbedding(query, 'query');
      console.log(`[${requestId}] 🧮 Query embedding generated`, {
        embeddingDimensions: queryEmbedding.embedding.length,
        embeddingModel: queryEmbedding.model,
        tokenCount: queryEmbedding.tokenCount,
        embeddingPreview: queryEmbedding.embedding.slice(0, 5).map(n => n.toFixed(4)),
      });

      // Use direct Vectorize API
      const vectorizeIndex = (this.vectorStore as any).index;
      const allResults: SearchResult[] = [];
      const debugInfo: any[] = [];

      // WORKAROUND: Try metadata filtering first, fallback to post-processing if it fails
      let useMetadataFiltering = true;
      const contentIdSet = new Set(contentIds);

      for (const contentId of contentIds) {
        try {
          console.log(`[${requestId}] 🎯 Searching content ID: ${contentId}`);

          let directResults: any;
          let searchMethod = 'unknown';

          if (useMetadataFiltering) {
            try {
              // Attempt metadata filtering first
              directResults = await vectorizeIndex.query(queryEmbedding.embedding, {
                topK: Math.ceil(limit / contentIds.length) + 2,
                returnMetadata: true,
                filter: { learningContentId: contentId }
              });
              searchMethod = 'metadata_filter';

              console.log(`[${requestId}] 📊 Metadata filter results for ${contentId}`, {
                totalMatches: directResults.matches?.length || 0,
                searchMethod,
              });

              // If metadata filtering returns 0 results, disable it for subsequent searches
              if (!directResults.matches || directResults.matches.length === 0) {
                console.log(`[${requestId}] ⚠️ Metadata filtering returned 0 results, switching to post-processing workaround`);
                useMetadataFiltering = false;
                throw new Error('Metadata filtering returned 0 results - switching to workaround');
              }

            } catch (filterError) {
              console.log(`[${requestId}] 🔄 Metadata filtering failed, using post-processing workaround`);
              useMetadataFiltering = false;

              // Fallback to unfiltered search + post-processing
              directResults = await vectorizeIndex.query(queryEmbedding.embedding, {
                topK: 50, // Get more results to filter from
                returnMetadata: true,
              });
              searchMethod = 'post_processing_workaround';

              // Filter results by learningContentId in post-processing
              if (directResults.matches) {
                directResults.matches = directResults.matches.filter((match: any) =>
                  match.metadata?.learningContentId === contentId
                );
              }

              console.log(`[${requestId}] 🔧 Post-processing filter results for ${contentId}`, {
                totalMatches: directResults.matches?.length || 0,
                searchMethod,
              });
            }
          } else {
            // Use post-processing workaround for all subsequent searches
            directResults = await vectorizeIndex.query(queryEmbedding.embedding, {
              topK: 50, // Get more results to filter from
              returnMetadata: true,
            });
            searchMethod = 'post_processing_workaround';

            // Filter results by learningContentId in post-processing
            if (directResults.matches) {
              directResults.matches = directResults.matches.filter((match: any) =>
                match.metadata?.learningContentId === contentId
              );
            }

            console.log(`[${requestId}] 🔧 Post-processing filter results for ${contentId}`, {
              totalMatches: directResults.matches?.length || 0,
              searchMethod,
            });
          }

          console.log(`[${requestId}] 📊 Final search results for ${contentId}`, {
            totalMatches: directResults.matches?.length || 0,
            searchMethod,
            matches: directResults.matches?.slice(0, 3).map((match: any, index: number) => ({
              index: index + 1,
              id: match.id,
              score: match.score,
              hasMetadata: !!match.metadata,
              learningContentId: match.metadata?.learningContentId,
              stepTitle: match.metadata?.stepTitle,
              chunkIndex: match.metadata?.chunkIndex,
              contentPreview: (match.metadata?.chunkText || match.metadata?.text || '').substring(0, 100),
            })) || [],
          });

          // Transform direct Vectorize results to our format
          const transformedResults: SearchResult[] = (directResults.matches || []).map((match: any) => ({
            id: match.id,
            content: match.metadata?.chunkText || match.metadata?.text || '',
            score: match.score,
            metadata: {
              learningContentId: match.metadata?.learningContentId || '',
              stepId: match.metadata?.stepId || '',
              stepTitle: match.metadata?.stepTitle || '',
              chunkIndex: match.metadata?.chunkIndex || 0,
              embeddingModel: match.metadata?.embeddingModel || '',
              embeddingDimension: match.metadata?.embeddingDimension || 0,
            },
          }));

          allResults.push(...transformedResults);
          debugInfo.push({
            contentId,
            rawResultsCount: directResults.matches?.length || 0,
            transformedResultsCount: transformedResults.length,
            searchMethod,
          });

        } catch (error) {
          console.error(`[${requestId}] ❌ Error searching content ${contentId}:`, {
            error: error instanceof Error ? error.message : String(error),
            errorType: error instanceof Error ? error.constructor.name : typeof error,
            stack: error instanceof Error ? error.stack : undefined,
          });

          // Handle authentication errors gracefully
          if (error instanceof Error &&
              (error.message.includes('Authentication error') ||
               error.message.includes('VECTOR_QUERY_ERROR'))) {
            console.warn(`[${requestId}] 🔐 Authentication error searching content ${contentId}. Skipping. This is normal in local development.`);
          } else {
            console.warn(`[${requestId}] ⚠️ Failed to search content ${contentId}:`, error);
          }
          // Continue with other content IDs
        }
      }

      // Sort by score and limit results
      const sortedResults = allResults
        .sort((a, b) => b.score - a.score)
        .slice(0, limit);

      console.log(`[${requestId}] 📈 Results before threshold filtering`, {
        totalResults: sortedResults.length,
        scores: sortedResults.map(r => r.score),
        scoreRange: sortedResults.length > 0 ? {
          highest: Math.max(...sortedResults.map(r => r.score)),
          lowest: Math.min(...sortedResults.map(r => r.score)),
        } : null,
      });

      // Filter by similarity threshold if specified
      const filteredResults = similarityThreshold > 0
        ? sortedResults.filter(result => result.score >= similarityThreshold)
        : sortedResults;

      console.log(`[${requestId}] ✅ Final search results`, {
        beforeFiltering: sortedResults.length,
        afterFiltering: filteredResults.length,
        similarityThreshold,
        filteredOutCount: sortedResults.length - filteredResults.length,
        filteredOutScores: sortedResults
          .filter(result => result.score < similarityThreshold)
          .map(r => ({ score: r.score, stepTitle: r.metadata.stepTitle })),
        finalResults: filteredResults.map((result, index) => ({
          index: index + 1,
          score: result.score,
          stepTitle: result.metadata.stepTitle,
          contentPreview: result.content.substring(0, 100),
        })),
      });

      const processingTimeMs = Date.now() - startTime;

      return {
        results: filteredResults,
        query,
        totalResults: filteredResults.length,
        processingTimeMs,
        contentScope: contentIds,
      };
    } catch (error) {
      console.error(`[${requestId}] 💥 Critical error in scoped search:`, {
        error: error instanceof Error ? error.message : String(error),
        errorType: error instanceof Error ? error.constructor.name : typeof error,
        stack: error instanceof Error ? error.stack : undefined,
      });

      // Handle authentication errors gracefully for scoped search operations
      if (error instanceof Error &&
          (error.message.includes('Authentication error') ||
           error.message.includes('VECTOR_QUERY_ERROR'))) {
        console.warn(`[${requestId}] 🔐 Authentication error during scoped search operation. Returning empty results. This is normal in local development.`);
        return {
          results: [],
          query,
          totalResults: 0,
          processingTimeMs: Date.now() - startTime,
          contentScope: contentIds,
        };
      }

      throw new IndexingError(`Failed to search scoped content: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Transform LangChain search results to our SearchResult format
   */
  private transformSearchResults(searchResults: any[], includeMetadata: boolean): SearchResult[] {
    return searchResults.map((doc, index) => {
      const metadata = doc.metadata || {};

      return {
        id: metadata.vectorId || `result_${index}`,
        content: doc.pageContent || doc.text || '',
        score: doc.score || 0,
        metadata: {
          learningContentId: metadata.learningContentId || '',
          stepId: metadata.stepId || '',
          stepTitle: metadata.stepTitle || '',
          chunkIndex: metadata.chunkIndex || 0,
          embeddingModel: metadata.embeddingModel || '',
          embeddingDimension: metadata.embeddingDimension || 0,
        },
        // sourceContent will be populated by higher-level services if needed
      };
    });
  }

  /**
   * Generate unique vector ID
   */
  private generateVectorId(): string {
    return `vec_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * Create vector indexing service with Cloudflare Workers environment
 */
export function createVectorIndexingService(
  vectorizeIndex: VectorizeIndex,
  env?: Record<string, any>
): VectorIndexingService {
  const embeddingService = createEmbeddingService(env);
  const chunkingService = createChunkingService();

  return new LearningContentVectorIndexingService(
    vectorizeIndex,
    embeddingService,
    chunkingService,
    env
  );
}

/**
 * Batch indexing utility for multiple content items
 */
export async function batchIndexContent(
  contentItems: LearningContent[],
  indexingService: VectorIndexingService,
  onProgress?: (completed: number, total: number) => void
): Promise<IndexedContent[]> {
  const allIndexedContent: IndexedContent[] = [];

  for (let i = 0; i < contentItems.length; i++) {
    const content = contentItems[i];

    try {
      const indexed = await indexingService.indexContent(content);
      allIndexedContent.push(...indexed);

      if (onProgress) {
        onProgress(i + 1, contentItems.length);
      }
    } catch (error) {
      console.error(`Failed to index content ${content.id}:`, error);
      // Continue with other content items
    }
  }

  return allIndexedContent;
}

/**
 * Cloudflare Workers types (should be available from @cloudflare/workers-types)
 */
declare global {
  interface VectorizeIndex {
    query(vector: number[], options?: any): Promise<any>;
    upsert(vectors: any[]): Promise<any>;
    deleteByIds(ids: string[]): Promise<any>;
  }
}