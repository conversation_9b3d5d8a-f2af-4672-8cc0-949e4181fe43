/**
 * Conversation Context Builder
 * Builds enhanced conversation context by combining memory and recent messages
 */

import type { Database } from '~/db/connection';
import type { ConversationWithMessages } from '~/db/services/chat';
import type { ConversationMemory } from '~/db/schema/memory';
import { getConversationMemory } from '~/db/services/memory';
import { ConversationMemoryService } from './conversation-memory-service';
import { log } from '~/lib/logger';

export interface EnhancedConversationContext {
  // Recent conversation messages
  recentMessages: ConversationWithMessages['messages'];
  
  // Memory-based context
  keyTopics: string[];
  userPreferences: Record<string, any>;
  proposedTopics: Array<{
    topic: string;
    status: 'proposed' | 'accepted' | 'rejected';
    timestamp: string;
  }>;
  contextSummary: string;
  
  // Context metadata
  hasMemory: boolean;
  memoryLastUpdated?: Date;
  totalMessageCount: number;
}

export interface ContextBuilderConfig {
  maxRecentMessages: number;
  enableMemoryExtraction: boolean;
  memoryUpdateThreshold: number; // Number of new messages before updating memory
}

export class ConversationContextBuilder {
  private memoryService: ConversationMemoryService;
  private config: ContextBuilderConfig;

  constructor(
    db: Database,
    config: Partial<ContextBuilderConfig> = {}
  ) {
    this.memoryService = new ConversationMemoryService(db);
    this.config = {
      maxRecentMessages: 6,
      enableMemoryExtraction: true,
      memoryUpdateThreshold: 3,
      ...config,
    };
  }

  /**
   * Build enhanced conversation context with memory integration
   */
  async buildEnhancedContext(
    conversation: ConversationWithMessages,
    db: Database
  ): Promise<EnhancedConversationContext> {
    try {
      // Get recent messages (limited by config)
      const recentMessages = conversation.messages
        .slice(-this.config.maxRecentMessages);

      // Get existing memory
      const existingMemory = await getConversationMemory(db, conversation.id);
      
      let memory: ConversationMemory | null = existingMemory;
      
      // Check if we should update memory
      const shouldUpdateMemory = this.shouldUpdateMemory(
        conversation.messages,
        existingMemory
      );

      if (shouldUpdateMemory && this.config.enableMemoryExtraction) {
        log.info('Updating conversation memory', {
          conversationId: conversation.id,
          messageCount: conversation.messages.length,
          hasExistingMemory: !!existingMemory,
        });

        // Extract and update memory
        memory = await this.memoryService.extractAndUpdateMemory(
          conversation.id,
          conversation.messages
        );
      }

      // Build enhanced context
      const enhancedContext: EnhancedConversationContext = {
        recentMessages,
        keyTopics: memory?.keyTopics || [],
        userPreferences: memory?.userPreferences || {},
        proposedTopics: memory?.proposedTopics || [],
        contextSummary: memory?.contextSummary || '',
        hasMemory: !!memory,
        memoryLastUpdated: memory?.updatedAt,
        totalMessageCount: conversation.messages.length,
      };

      log.debug('Built enhanced conversation context', {
        conversationId: conversation.id,
        recentMessageCount: recentMessages.length,
        keyTopicsCount: enhancedContext.keyTopics.length,
        proposedTopicsCount: enhancedContext.proposedTopics.length,
        hasMemory: enhancedContext.hasMemory,
      });

      return enhancedContext;
    } catch (error) {
      log.error('Failed to build enhanced conversation context', {
        conversationId: conversation.id,
        error: error instanceof Error ? error.message : String(error),
      });

      // Fallback to basic context
      return {
        recentMessages: conversation.messages.slice(-this.config.maxRecentMessages),
        keyTopics: [],
        userPreferences: {},
        proposedTopics: [],
        contextSummary: '',
        hasMemory: false,
        totalMessageCount: conversation.messages.length,
      };
    }
  }

  /**
   * Build system prompt with memory context
   */
  buildMemoryAwareSystemPrompt(
    context: EnhancedConversationContext,
    ragResults: any[],
    learningContentMetadata?: any
  ): string {
    let systemPrompt = '';

    // Core AI assistant identity with learning content focus
    systemPrompt += `You are an AI teaching assistant helping users understand learning content. You have access to specific learning materials and should provide helpful, accurate responses.\n\n`;

    // Add learning content information
    if (learningContentMetadata) {
      systemPrompt += `LEARNING CONTENT INFORMATION:\n`;
      systemPrompt += `Title: ${learningContentMetadata.title || 'Unknown'}\n`;
      systemPrompt += `Description: ${learningContentMetadata.description || 'No description available'}\n`;
      systemPrompt += `Level: ${learningContentMetadata.learningLevel || 'Unknown'}\n\n`;
    }

    // Add memory context if available
    if (context.hasMemory) {
      systemPrompt += 'CONVERSATION MEMORY:\n';
      
      if (context.contextSummary) {
        systemPrompt += `Previous Context: ${context.contextSummary}\n`;
      }
      
      if (context.keyTopics.length > 0) {
        systemPrompt += `Key Topics Discussed: ${context.keyTopics.join(', ')}\n`;
      }
      
      if (context.proposedTopics.length > 0) {
        const pendingTopics = context.proposedTopics
          .filter(t => t.status === 'proposed')
          .map(t => t.topic);
        const acceptedTopics = context.proposedTopics
          .filter(t => t.status === 'accepted')
          .map(t => t.topic);
        
        if (pendingTopics.length > 0) {
          systemPrompt += `Pending Topic Proposals: ${pendingTopics.join(', ')}\n`;
        }
        if (acceptedTopics.length > 0) {
          systemPrompt += `Accepted Topics: ${acceptedTopics.join(', ')}\n`;
        }
      }
      
      if (Object.keys(context.userPreferences).length > 0) {
        systemPrompt += `User Preferences: ${JSON.stringify(context.userPreferences)}\n`;
      }
      
      systemPrompt += '\n';
    }

    // Enhanced RAG context with detailed formatting
    const hasRagContext = ragResults && ragResults.length > 0;
    systemPrompt += `RETRIEVED CONTEXT FROM LEARNING MATERIALS:\n`;
    
    if (hasRagContext) {
      ragResults.forEach((result, index) => {
        const stepTitle = result.metadata?.stepTitle || 'Unknown Step';
        systemPrompt += `[Context ${index + 1}] (from: ${stepTitle})\n${result.content}\n\n`;
      });
    } else {
      systemPrompt += 'No specific context was retrieved from the learning materials for this question.\n\n';
    }

    // Critical RAG-specific source attribution instructions
    systemPrompt += `CRITICAL INSTRUCTIONS FOR SOURCE ATTRIBUTION:\n`;
    
    if (hasRagContext) {
      systemPrompt += `✅ CONTEXT AVAILABLE: You have relevant content from the learning materials above.\n`;
      systemPrompt += `- Base your answer primarily on the provided context\n`;
      systemPrompt += `- Start your response with "Based on the learning materials:" or similar\n`;
      systemPrompt += `- Quote or reference specific information from the context\n`;
      systemPrompt += `- If you need to add general knowledge, clearly distinguish it: "Additionally, from general knowledge..."\n\n`;
    } else {
      systemPrompt += `⚠️ NO CONTEXT RETRIEVED: No relevant content was found in the learning materials for this question.\n`;
      systemPrompt += `- You should still provide a helpful and informative response using your general knowledge\n`;
      systemPrompt += `- Start your response with: "While I don't have specific information about this in the current learning materials, I can help explain this topic from general knowledge:"\n`;
      systemPrompt += `- Provide a comprehensive explanation using your general knowledge\n`;
      systemPrompt += `- After your explanation, suggest: "For more specific details related to ${learningContentMetadata?.title || 'this learning content'}, you might want to check if there are related sections in the materials or rephrase your question."\n`;
      systemPrompt += `- Be educational and helpful while being transparent about your information source\n\n`;
    }

    // Comprehensive response guidelines integrating memory and RAG
    systemPrompt += `RESPONSE GUIDELINES:\n`;
    systemPrompt += `1. Always be transparent about your information source (learning materials vs. general knowledge)\n`;
    systemPrompt += `2. Maintain context continuity using the conversation memory\n`;
    systemPrompt += `3. Reference previous topics and user preferences when relevant\n`;
    systemPrompt += `4. Reference specific sections when using learning material context\n`;
    systemPrompt += `5. When proposing new topics, consider what has been discussed before\n`;
    systemPrompt += `6. If the user accepts a proposed topic, acknowledge the acceptance and build upon it\n`;
    systemPrompt += `7. If mixing sources, clearly separate them in your response\n`;
    systemPrompt += `8. Encourage active learning and ask follow-up questions when appropriate\n`;
    systemPrompt += `9. Be conversational and helpful while maintaining source clarity\n\n`;

    systemPrompt += `Please provide a helpful response that clearly indicates whether you're using information from the learning materials or general knowledge, while leveraging conversation memory for contextual awareness.`;

    return systemPrompt;
  }

  /**
   * Determine if memory should be updated based on conversation state
   */
  private shouldUpdateMemory(
    messages: ConversationWithMessages['messages'],
    existingMemory: ConversationMemory | null
  ): boolean {
    // Always update if no memory exists
    if (!existingMemory) {
      return messages.length >= 2; // At least one exchange
    }

    // Update if we have enough new messages since last extraction
    const lastExtractionCount = existingMemory.extractionMetadata?.lastMessageCount || 0;
    const newMessageCount = messages.length - lastExtractionCount;
    
    return newMessageCount >= this.config.memoryUpdateThreshold;
  }

  /**
   * Handle topic proposal acceptance
   */
  async handleTopicAcceptance(
    conversationId: string,
    acceptedTopic: string,
    db: Database
  ): Promise<void> {
    try {
      // Update the memory service to mark topic as accepted
      await this.memoryService.updateTopicStatus(
        conversationId,
        acceptedTopic,
        'accepted'
      );

      log.info('Topic acceptance handled', {
        conversationId,
        acceptedTopic,
      });
    } catch (error) {
      log.error('Failed to handle topic acceptance', {
        conversationId,
        acceptedTopic,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Handle topic proposal rejection
   */
  async handleTopicRejection(
    conversationId: string,
    rejectedTopic: string,
    db: Database
  ): Promise<void> {
    try {
      // Update the memory service to mark topic as rejected
      await this.memoryService.updateTopicStatus(
        conversationId,
        rejectedTopic,
        'rejected'
      );

      log.info('Topic rejection handled', {
        conversationId,
        rejectedTopic,
      });
    } catch (error) {
      log.error('Failed to handle topic rejection', {
        conversationId,
        rejectedTopic,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }
}