/**
 * Embedding service using Voyage AI
 *
 * This service provides text embedding capabilities using Voyage AI's voyage-3.5 model
 * with 1024 dimensions, optimized for semantic search and retrieval applications.
 */

import type {
  EmbeddingService,
  EmbeddingConfig,
  EmbeddingResult
} from '../types/index';
import { EmbeddingError } from '../types/index';
import { SERVICES_CONFIG, EMBEDDING_MODELS } from '../config/services-config';

/**
 * Voyage AI embedding service implementation
 */
export class VoyageEmbeddingService implements EmbeddingService {
  private config: EmbeddingConfig;
  private apiKey: string;
  private baseUrl = 'https://api.voyageai.com/v1';

  constructor(config?: Partial<EmbeddingConfig>, apiKey?: string) {
    this.config = {
      ...SERVICES_CONFIG.defaultEmbedding,
      ...config,
    };
    this.apiKey = apiKey !== undefined ? apiKey : SERVICES_CONFIG.voyageApiKey;

    if (!this.apiKey || this.apiKey.trim() === '') {
      throw new Error('Voyage AI API key is required. Set VOYAGE_API_KEY environment variable.');
    }
  }

  /**
   * Generate embedding for a single text
   */
  async generateEmbedding(text: string, inputType: 'document' | 'query' = 'document'): Promise<EmbeddingResult> {
    if (!text.trim()) {
      throw new Error('Text cannot be empty');
    }

    try {
      const response = await this.makeEmbeddingRequest([text], inputType);
      const embedding = response.data[0].embedding;

      return {
        embedding,
        model: this.config.model,
        dimensions: this.config.dimensions,
        tokenCount: response.usage?.total_tokens,
      };
    } catch (error) {
      throw this.handleEmbeddingError(error, 'Failed to generate embedding');
    }
  }

  /**
   * Generate embeddings for multiple texts
   */
  async generateEmbeddings(texts: string[], inputType: 'document' | 'query' = 'document'): Promise<EmbeddingResult[]> {
    if (texts.length === 0) {
      return [];
    }

    // Filter out empty texts
    const validTexts = texts.filter(text => text.trim());
    if (validTexts.length === 0) {
      throw new Error('At least one non-empty text is required');
    }

    try {
      // Process in batches to avoid API limits
      const batchSize = 128; // Voyage AI batch limit
      const results: EmbeddingResult[] = [];

      for (let i = 0; i < validTexts.length; i += batchSize) {
        const batch = validTexts.slice(i, i + batchSize);
        const response = await this.makeEmbeddingRequest(batch, inputType);

        const batchResults = response.data.map((item) => ({
          embedding: item.embedding,
          model: this.config.model,
          dimensions: this.config.dimensions,
          tokenCount: response.usage ? Math.floor(response.usage.total_tokens / batch.length) : undefined,
        }));

        results.push(...batchResults);
      }

      return results;
    } catch (error) {
      throw this.handleEmbeddingError(error, 'Failed to generate embeddings');
    }
  }

  /**
   * Get the current embedding configuration
   */
  getConfig(): EmbeddingConfig {
    return { ...this.config };
  }

  /**
   * Make API request to Voyage AI
   */
  private async makeEmbeddingRequest(texts: string[], inputType: 'document' | 'query' = 'document'): Promise<VoyageEmbeddingResponse> {
    const requestBody = {
      input: texts,
      model: this.config.model,
      input_type: inputType, // Use appropriate type for documents vs queries
    };

    const response = await fetch(`${this.baseUrl}/embeddings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({})) as any;
      throw new Error(`Voyage AI API error: ${response.status} ${response.statusText}. ${errorData.message || ''}`);
    }

    return response.json();
  }

  /**
   * Handle and transform embedding errors
   */
  private handleEmbeddingError(error: unknown, message: string): EmbeddingError {
    if (error instanceof Error) {
      if (error.message.includes('rate limit')) {
        return new EmbeddingError('Rate limit exceeded. Please try again later.', error);
      }
      if (error.message.includes('quota')) {
        return new EmbeddingError('API quota exceeded. Please check your Voyage AI account.', error);
      }
      if (error.message.includes('unauthorized')) {
        return new EmbeddingError('Invalid API key. Please check your Voyage AI credentials.', error);
      }
      return new EmbeddingError(`${message}: ${error.message}`, error);
    }
    return new EmbeddingError(message);
  }
}

/**
 * Voyage AI API response types
 */
interface VoyageEmbeddingResponse {
  object: 'list';
  data: Array<{
    object: 'embedding';
    embedding: number[];
    index: number;
  }>;
  model: string;
  usage?: {
    total_tokens: number;
  };
}

/**
 * LangChain-compatible embedding adapter
 *
 * This adapter wraps our VoyageEmbeddingService to make it compatible with
 * LangChain's Embeddings interface, which expects embedQuery and embedDocuments methods.
 */
export class LangChainEmbeddingAdapter {
  constructor(private embeddingService: EmbeddingService) {}

  /**
   * Embed a single query text (LangChain interface)
   */
  async embedQuery(text: string): Promise<number[]> {
    const result = await this.embeddingService.generateEmbedding(text, 'query');
    return result.embedding;
  }

  /**
   * Embed multiple documents (LangChain interface)
   */
  async embedDocuments(texts: string[]): Promise<number[][]> {
    const results = await this.embeddingService.generateEmbeddings(texts, 'document');
    return results.map(result => result.embedding);
  }

  /**
   * Get the underlying embedding service configuration
   */
  getConfig(): EmbeddingConfig {
    return this.embeddingService.getConfig();
  }
}

/**
 * Create embedding service with environment configuration
 */
export function createEmbeddingService(env?: Record<string, any>): EmbeddingService {
  const apiKey = env?.['VOYAGE_API_KEY'] || SERVICES_CONFIG.voyageApiKey;
  return new VoyageEmbeddingService(undefined, apiKey);
}

/**
 * Create LangChain-compatible embedding adapter
 */
export function createLangChainEmbeddingAdapter(env?: Record<string, any>): LangChainEmbeddingAdapter {
  const embeddingService = createEmbeddingService(env);
  return new LangChainEmbeddingAdapter(embeddingService);
}

/**
 * Create embedding service with custom configuration
 */
export function createCustomEmbeddingService(
  config: Partial<EmbeddingConfig>,
  env?: Record<string, any>
): EmbeddingService {
  const apiKey = env?.['VOYAGE_API_KEY'] || SERVICES_CONFIG.voyageApiKey;
  return new VoyageEmbeddingService(config, apiKey);
}

/**
 * Utility function to estimate token count for text
 * This is an approximation - actual token count may vary
 */
export function estimateTokenCount(text: string): number {
  // Rough estimation: ~4 characters per token for English text
  return Math.ceil(text.length / 4);
}

/**
 * Validate text for embedding
 */
export function validateEmbeddingText(text: string): void {
  if (!text || typeof text !== 'string') {
    throw new Error('Text must be a non-empty string');
  }

  if (text.trim().length === 0) {
    throw new Error('Text cannot be empty or only whitespace');
  }

  const estimatedTokens = estimateTokenCount(text);
  const maxTokens = EMBEDDING_MODELS[SERVICES_CONFIG.defaultEmbedding.model as keyof typeof EMBEDDING_MODELS]?.maxTokens || 32000;

  if (estimatedTokens > maxTokens) {
    throw new Error(`Text is too long. Estimated ${estimatedTokens} tokens, but maximum is ${maxTokens}`);
  }
}