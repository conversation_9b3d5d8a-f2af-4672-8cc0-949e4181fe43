/**
 * Conversation Memory Service
 * 
 * This service provides persistent memory functionality to retain conversation context
 * across sessions, specifically addressing the issue where the assistant loses context
 * when proposing topics and receiving user confirmation.
 */

import type { Database } from '~/db/connection';
import { 
  getConversationWithMessages,
  type ConversationWithMessages
} from '~/db/services/chat';
import type { Message } from '~/db/schema/chat';
// ConversationMemory type will be imported from schema below
import {
  upsertConversationMemory,
  getConversationMemory,
  updateProposedTopicStatus
} from '~/db/services/memory';
import type { ConversationMemory } from '~/db/schema/memory';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { generateText } from 'ai';
import { log } from '~/lib/logger';

// ConversationMemory type is imported from schema

export interface MemoryExtractionResult {
  keyTopics: string[];
  userPreferences: Record<string, any>;
  proposedTopics: Array<{
    topic: string;
    status: 'proposed' | 'accepted' | 'rejected';
    timestamp: Date;
  }>;
  contextSummary: string;
}

export interface ConversationMemoryServiceConfig {
  aiModel: {
    model: string;
    temperature: number;
    maxTokens: number;
  };
  memoryWindow: {
    maxMessages: number;
    summaryLength: number;
  };
}

const DEFAULT_CONFIG: ConversationMemoryServiceConfig = {
  aiModel: {
    model: 'anthropic/claude-3.5-sonnet',
    temperature: 0.3,
    maxTokens: 1000,
  },
  memoryWindow: {
    maxMessages: 20,
    summaryLength: 500,
  },
};

/**
 * Conversation Memory Service
 * 
 * Extracts and maintains key information from conversations to provide
 * persistent context across sessions.
 */
export class ConversationMemoryService {
  private config: ConversationMemoryServiceConfig;
  private env?: Record<string, any>;

  constructor(
    private db: Database,
    env?: Record<string, any>,
    config?: Partial<ConversationMemoryServiceConfig>
  ) {
    this.env = env;
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Extract memory from conversation messages
   */
  async extractMemoryFromConversation(
    conversationId: string,
    userId: string
  ): Promise<MemoryExtractionResult> {
    try {
      // Get conversation with messages
      const conversation = await getConversationWithMessages(
        this.db,
        conversationId,
        userId,
        {
          limit: this.config.memoryWindow.maxMessages,
          orderBy: 'asc',
        }
      );

      if (!conversation || conversation.messages.length === 0) {
        return this.createEmptyMemory();
      }

      // Build conversation text for analysis
      const conversationText = this.buildConversationText(conversation.messages);

      // Extract memory using AI
      const memoryResult = await this.extractMemoryWithAI(conversationText);

      log.info('Memory extracted from conversation', {
        conversationId,
        keyTopicsCount: memoryResult.keyTopics.length,
        proposedTopicsCount: memoryResult.proposedTopics.length,
        summaryLength: memoryResult.contextSummary.length,
      });

      return memoryResult;
    } catch (error) {
      log.error('Failed to extract memory from conversation', {
        conversationId,
        error: error instanceof Error ? error.message : String(error),
      });
      return this.createEmptyMemory();
    }
  }

  /**
   * Extract and update memory from conversation messages
   */
  async extractAndUpdateMemory(
    conversationId: string,
    messages: Message[]
  ): Promise<ConversationMemory | null> {
    try {
      log.info('Starting memory extraction', {
        conversationId,
        messageCount: messages.length,
        hasApiKey: !!this.env?.OPENROUTER_API_KEY
      });
      
      const conversationText = this.buildConversationText(messages);
      log.info('Built conversation text', {
        conversationId,
        textLength: conversationText.length
      });
      
      const extractedMemory = await this.extractMemoryWithAI(conversationText);
      log.info('Memory extraction completed', {
        conversationId,
        hasKeyTopics: extractedMemory.keyTopics.length > 0,
        hasProposedTopics: extractedMemory.proposedTopics.length > 0,
        hasContextSummary: !!extractedMemory.contextSummary
      });
      
      if (!extractedMemory || (extractedMemory.keyTopics.length === 0 && extractedMemory.proposedTopics.length === 0)) {
        log.warn('No meaningful memory extracted', { conversationId });
        return null;
      }
      
      // Update memory in database
      const memory = await upsertConversationMemory(this.db, {
        conversationId,
        keyTopics: extractedMemory.keyTopics,
        userPreferences: extractedMemory.userPreferences,
        proposedTopics: extractedMemory.proposedTopics.map(t => ({
          ...t,
          timestamp: typeof t.timestamp === 'string' ? t.timestamp : t.timestamp.toISOString()
        })),
        contextSummary: extractedMemory.contextSummary,
        extractionMetadata: {
          lastMessageCount: messages.length,
          extractionMethod: 'ai-extraction',
          confidence: 0.8,
        },
      });
      
      log.info('Memory saved to database', {
        conversationId,
        memoryId: memory.id
      });
      
      return memory;
    } catch (error) {
      log.error('Failed to extract and update memory', {
        conversationId,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      return null;
    }
  }

  /**
   * Update topic status (accepted/rejected)
   */
  async updateTopicStatus(
    conversationId: string,
    topic: string,
    status: 'accepted' | 'rejected'
  ): Promise<ConversationMemory | null> {
    try {
      const memory = await updateProposedTopicStatus(
        this.db,
        conversationId,
        topic,
        status
      );
      
      return memory;
    } catch (error) {
      log.error('Failed to update topic status', {
        conversationId,
        topic,
        status,
        error: error instanceof Error ? error.message : String(error),
      });
      return null;
    }
  }

  /**
   * Build enhanced context with memory integration
   */
  async buildEnhancedContext(
    conversationId: string,
    recentMessages: Message[],
    maxMessages: number = 6
  ): Promise<{
    messages: Message[];
    memoryContext: string;
    keyTopics: string[];
    userPreferences: Record<string, any>;
    proposedTopics: Array<{
      topic: string;
      status: 'proposed' | 'accepted' | 'rejected';
      timestamp: string;
    }>;
  }> {
    try {
      // Get existing memory
      const memory = await getConversationMemory(this.db, conversationId);
      
      // Limit recent messages
      const limitedMessages = recentMessages.slice(-maxMessages);
      
      // Build memory context string
      let memoryContext = '';
      
      if (memory) {
        if (memory.contextSummary) {
          memoryContext += `Previous context: ${memory.contextSummary}\n`;
        }
        
        if (memory.keyTopics.length > 0) {
          memoryContext += `Key topics: ${memory.keyTopics.join(', ')}\n`;
        }
        
        const pendingTopics = memory.proposedTopics
           .filter((t: any) => t.status === 'proposed')
           .map((t: any) => t.topic);
        
        if (pendingTopics.length > 0) {
          memoryContext += `Pending topic proposals: ${pendingTopics.join(', ')}\n`;
        }
      }
      
      return {
        messages: limitedMessages,
        memoryContext,
        keyTopics: memory?.keyTopics || [],
        userPreferences: memory?.userPreferences || {},
        proposedTopics: memory?.proposedTopics || [],
      };
    } catch (error) {
      log.error('Failed to build enhanced context', {
        conversationId,
        error: error instanceof Error ? error.message : String(error),
      });
      
      // Fallback to basic context
      return {
        messages: recentMessages.slice(-maxMessages),
        memoryContext: '',
        keyTopics: [],
        userPreferences: {},
        proposedTopics: [],
      };
    }
  }

  /**
   * Detect topic proposals and acceptances in messages
   */
  detectTopicProposalsAndAcceptances(
    messages: Message[]
  ): Array<{
    topic: string;
    status: 'proposed' | 'accepted' | 'rejected';
    timestamp: Date;
  }> {
    const topicEvents: Array<{
      topic: string;
      status: 'proposed' | 'accepted' | 'rejected';
      timestamp: Date;
    }> = [];

    for (let i = 0; i < messages.length; i++) {
      const message = messages[i];
      const nextMessage = messages[i + 1];

      // Detect proposals from assistant
      if (message.senderRole === 'assistant') {
        const proposals = this.extractTopicProposals(message.content);
        proposals.forEach(topic => {
          topicEvents.push({
            topic,
            status: 'proposed',
            timestamp: message.createdAt,
          });
        });
      }

      // Detect user responses to proposals
      if (message.senderRole === 'user' && nextMessage?.senderRole === 'assistant') {
        const acceptance = this.detectTopicAcceptance(message.content);
        if (acceptance) {
          // Find the most recent proposal to mark as accepted
          const recentProposal = topicEvents
            .filter(t => t.status === 'proposed')
            .pop();
          
          if (recentProposal) {
            topicEvents.push({
              topic: recentProposal.topic,
              status: acceptance.accepted ? 'accepted' : 'rejected',
              timestamp: message.createdAt,
            });
          }
        }
      }
    }

    return topicEvents;
  }

  /**
   * Extract topic proposals from assistant message
   */
  private extractTopicProposals(content: string): string[] {
    const proposals: string[] = [];
    
    // Common patterns for topic proposals
    const patterns = [
      /would you like to (explore|learn about|discuss) ([^?]+)\?/gi,
      /shall we (explore|discuss|look at) ([^?]+)\?/gi,
      /let's (explore|discuss|examine) ([^.!?]+)/gi,
      /you might want to (explore|learn about) ([^.!?]+)/gi,
    ];

    patterns.forEach(pattern => {
      const matches = content.matchAll(pattern);
      for (const match of matches) {
        if (match[2]) {
          proposals.push(match[2].trim());
        }
      }
    });

    return proposals;
  }

  /**
   * Detect topic acceptance/rejection in user message
   */
  private detectTopicAcceptance(content: string): { accepted: boolean } | null {
    const lowerContent = content.toLowerCase();
    
    // Acceptance patterns
    const acceptancePatterns = [
      /\b(yes|yeah|sure|okay|ok|absolutely|definitely|sounds good)\b/,
      /\b(let's do it|let's go|i'd like that|that sounds interesting)\b/,
      /\b(please|go ahead|continue)\b/,
    ];

    // Rejection patterns
    const rejectionPatterns = [
      /\b(no|nope|not really|not interested|maybe later)\b/,
      /\b(skip|pass|different topic|something else)\b/,
    ];

    const hasAcceptance = acceptancePatterns.some(pattern => pattern.test(lowerContent));
    const hasRejection = rejectionPatterns.some(pattern => pattern.test(lowerContent));

    if (hasAcceptance && !hasRejection) {
      return { accepted: true };
    } else if (hasRejection && !hasAcceptance) {
      return { accepted: false };
    }

    return null;
  }

  /**
   * Build conversation text from messages
   */
  private buildConversationText(messages: Message[]): string {
    return messages
      .map(msg => `${msg.senderRole === 'user' ? 'User' : 'Assistant'}: ${msg.content}`)
      .join('\n\n');
  }

  /**
   * Extract memory using AI analysis
   */
  private async extractMemoryWithAI(conversationText: string): Promise<MemoryExtractionResult> {
    const apiKey = this.env?.OPENROUTER_API_KEY || process.env.OPENROUTER_API_KEY;

    if (!apiKey) {
      throw new Error('OpenRouter API key is missing');
    }

    const openrouter = createOpenRouter({ apiKey });
    const model = openrouter(this.config.aiModel.model);

    const prompt = `Analyze the following conversation and extract key memory information. Return a JSON object with the following structure:

{
  "keyTopics": ["topic1", "topic2", ...],
  "userPreferences": {"preference1": "value1", ...},
  "proposedTopics": [{"topic": "topic", "status": "proposed|accepted|rejected", "timestamp": "ISO_DATE"}],
  "contextSummary": "Brief summary of the conversation context and key points"
}

Conversation:
${conversationText}

Focus on:
1. Main topics discussed
2. User learning preferences and interests
3. Topics proposed by the assistant and user responses
4. Key context that should be remembered for future conversations

Return only the JSON object, no additional text.`;

    try {
      const result = await generateText({
        model,
        messages: [{ role: 'user', content: prompt }],
        temperature: this.config.aiModel.temperature,
        maxTokens: this.config.aiModel.maxTokens,
      });

      const parsed = JSON.parse(result.text);
      return {
        keyTopics: parsed.keyTopics || [],
        userPreferences: parsed.userPreferences || {},
        proposedTopics: parsed.proposedTopics || [],
        contextSummary: parsed.contextSummary || '',
      };
    } catch (error) {
      log.error('Failed to extract memory with AI', {
        error: error instanceof Error ? error.message : String(error),
      });
      return this.createEmptyMemory();
    }
  }

  /**
   * Create empty memory structure
   */
  private createEmptyMemory(): MemoryExtractionResult {
    return {
      keyTopics: [],
      userPreferences: {},
      proposedTopics: [],
      contextSummary: '',
    };
  }
}

/**
 * Create conversation memory service instance
 */
export function createConversationMemoryService(
  db: Database,
  env?: Record<string, any>,
  config?: Partial<ConversationMemoryServiceConfig>
): ConversationMemoryService {
  return new ConversationMemoryService(db, env, config);
}