/**
 * RAG-powered chat service
 * 
 * This service combines vector search with AI response generation to create
 * contextual conversations about learning content.
 */

import type { Database } from '~/db/connection';
import { 
  createConversation,
  addMessage,
  getConversationWithMessages,
  getUserConversationsForContent,
  updateConversationTitle,
  type ConversationWithMessages,
  type MessageWithSources 
} from '~/db/services/chat';
import { getLearningContentById } from '~/db/services/learning-content';
import { createRagSearchService, type RagSearchService } from './rag-search-service';
import { createVectorIndexingService } from './vector-indexing-service';
import { ConversationContextBuilder } from './conversation-context-builder';
import { ConversationMemoryService } from './conversation-memory-service';
import { log } from '~/lib/logger';
import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import { generateText, type CoreMessage } from 'ai';

export interface ChatRequest {
  message: string;
  conversationId?: string;
  learningContentId: string;
  userId: string;
}

export interface ChatResponse {
  success: boolean;
  conversationId: string;
  message?: {
    id: string;
    content: string;
    role: 'user' | 'assistant';
    sources?: Array<{
      stepId: string;
      stepTitle: string;
      chunkIndex: number;
      score: number;
      contentId?: string;
      contentTitle?: string;
    }>;
    timestamp: Date;
  };
  error?: string;
  processingTime?: number;
}

export interface StreamingChatResponse {
  conversationId: string;
  messageId: string;
  stream: ReadableStream<Uint8Array>;
}

/**
 * RAG Chat Service Configuration
 */
export interface RagChatServiceConfig {
  searchOptions: {
    limit: number;
    similarityThreshold: number;
    includeSourceContent: boolean;
  };
  aiModel: {
    provider: 'openrouter';
    model: string;
    temperature: number;
    maxTokens: number;
  };
  contextWindow: {
    maxMessages: number;
    maxContextLength: number;
  };
}

const DEFAULT_CONFIG: RagChatServiceConfig = {
  searchOptions: {
    limit: 5,
    similarityThreshold: 0.3, // Match test script threshold for better results
    includeSourceContent: true,
  },
  aiModel: {
    provider: 'openrouter',
    model: 'anthropic/claude-3.5-sonnet',
    temperature: 0.7,
    maxTokens: 2000,
  },
  contextWindow: {
    maxMessages: 10,
    maxContextLength: 8000,
  },
};

/**
 * RAG-powered chat service
 */
export class RagChatService {
  private ragSearchService: RagSearchService;
  private config: RagChatServiceConfig;
  private env?: Record<string, any>;
  private memoryService: ConversationMemoryService;
  private contextBuilder: ConversationContextBuilder;

  constructor(
    private db: Database,
    vectorizeIndex?: VectorizeIndex,
    env?: Record<string, any>,
    config?: Partial<RagChatServiceConfig>
  ) {
    // Store environment variables for API key access
    this.env = env;

    // Debug logging for constructor
    const testApiKey = env?.OPENROUTER_API_KEY || process.env.OPENROUTER_API_KEY;

    console.log('RagChatService constructor - Environment debug:', {
      hasEnv: !!env,
      envKeys: env ? Object.keys(env) : [],
      hasOpenRouterKey: !!testApiKey,
      apiKeyLength: testApiKey ? testApiKey.length : 0,
    });
    // Initialize RAG search service
    const vectorIndexingService = vectorizeIndex 
      ? createVectorIndexingService(vectorizeIndex, env)
      : null;
    const contentProvider = async (contentIds: string[]) => {
      const contents = await Promise.all(
        contentIds.map(id => getLearningContentById(this.db, id))
      );
      // Filter out null values and cast to RAG LearningContent type
      return contents.filter((content): content is NonNullable<typeof content> => content !== null).map(content => ({
        id: content.id,
        title: content.title,
        description: content.description,
        steps: content.steps.map(step => ({
          id: step.id,
          title: step.title,
          icon: step.icon || '',
          blocks: step.blocks
        })),
        learningLevel: content.learningLevel,
        estimatedReadingTime: content.estimatedReadingTime,
        isPublic: content.isPublic,
        tags: content.tags,
        userId: content.userId,
        createdAt: content.createdAt,
        updatedAt: content.updatedAt,
        aiMetadata: content.aiMetadata || undefined
      }));
    };
    
    if (vectorIndexingService) {
      this.ragSearchService = createRagSearchService(vectorIndexingService, contentProvider);
    } else {
      // Create a mock RAG search service for development
      this.ragSearchService = {
        searchContentScoped: async () => ({ results: [] }),
        search: async () => ({ results: [] })
      } as any;
    }
    this.config = { ...DEFAULT_CONFIG, ...config };
    
    // Initialize memory service and context builder
    this.memoryService = new ConversationMemoryService(this.db);
    this.contextBuilder = new ConversationContextBuilder(this.db);
  }

  /**
   * Process a chat message with RAG context
   */
  async chat(request: ChatRequest): Promise<ChatResponse> {
    const startTime = Date.now();

    try {
      // Get or create conversation
      let conversationId = request.conversationId;
      if (!conversationId) {
        const conversation = await createConversation(this.db, {
          userId: request.userId,
          learningContentId: request.learningContentId,
          title: 'New Chat',
        });
        conversationId = conversation.id;
      }

      // Add user message
      const userMessage = await addMessage(this.db, {
        conversationId,
        content: request.message,
        senderRole: 'user',
        messageType: 'text',
      });

      // Perform RAG search with debugging
      log.info(`[RAG Chat] Performing search for: "${request.message}"`, {
        learningContentId: request.learningContentId,
        limit: this.config.searchOptions.limit,
        similarityThreshold: this.config.searchOptions.similarityThreshold,
      });

      const searchResults = await this.ragSearchService.searchContentScoped(
        request.message,
        [request.learningContentId],
        {
          limit: this.config.searchOptions.limit,
          similarityThreshold: this.config.searchOptions.similarityThreshold,
          enrichWithSourceContent: this.config.searchOptions.includeSourceContent,
        }
      );

      log.info(`[RAG Chat] 🔍 Vectorize Search Results`, {
        resultsFound: searchResults.results.length,
        scores: searchResults.results.map(r => r.score),
        searchQuery: request.message,
        learningContentId: request.learningContentId,
        searchConfig: {
          limit: this.config.searchOptions.limit,
          similarityThreshold: this.config.searchOptions.similarityThreshold,
        },
        results: searchResults.results.map((result, index) => ({
          index: index + 1,
          score: result.score,
          stepTitle: result.metadata.stepTitle || 'Unknown',
          stepId: result.metadata.stepId,
          contentPreview: result.content.substring(0, 150) + (result.content.length > 150 ? '...' : ''),
          chunkIndex: result.metadata.chunkIndex,
          learningContentId: result.metadata.learningContentId,
        })),
        totalContextLength: searchResults.results.reduce((sum, r) => sum + r.content.length, 0),
        hasRelevantContent: searchResults.results.length > 0,
        contentSource: searchResults.results.length > 0 ? 'learning_materials' : 'general_knowledge',
        workaroundStatus: searchResults.results.length > 0 ? 'SUCCESS - Content found' : 'ISSUE - No content found (metadata filtering bug)',
      });

      // Get conversation context
      const conversation = await getConversationWithMessages(this.db, conversationId, request.userId, {
        limit: this.config.contextWindow.maxMessages,
        orderBy: 'asc',
      });

      // Generate AI response
      const aiResponse = await this.generateResponse(
        request.message,
        searchResults.results,
        conversation?.messages || [],
        request.learningContentId
      );

      // Prepare sources for metadata with deduplication
      const sources = this.deduplicateSources(searchResults.results.map(result => ({
        stepId: result.metadata.stepId,
        stepTitle: result.metadata.stepTitle,
        chunkIndex: result.metadata.chunkIndex,
        score: result.score,
        contentId: result.metadata.learningContentId,
        contentTitle: result.sourceContent?.title,
      })));

      // Add assistant message
      const assistantMessage = await addMessage(this.db, {
        conversationId,
        content: aiResponse,
        senderRole: 'assistant',
        messageType: 'text',
        metadata: {
          sources,
          processingTime: Date.now() - startTime,
          model: this.config.aiModel.model,
        },
      });

      // Auto-generate conversation title for first exchange
      if (conversation?.messages && conversation.messages.length <= 1) {
        await this.generateConversationTitle(conversationId, request.userId, request.message);
      }

      // Extract and update conversation memory after a few exchanges
      if (conversation?.messages && conversation.messages.length >= 3) {
        try {
          await this.contextBuilder.buildEnhancedContext(
            { id: conversationId, messages: [...conversation.messages, assistantMessage] } as any,
            this.db
          );
        } catch (error) {
          log.warn('Failed to update conversation memory', {
            conversationId,
            error: error instanceof Error ? error.message : String(error),
          });
        }
      }

      return {
        success: true,
        conversationId,
        message: {
          id: assistantMessage.id,
          content: aiResponse,
          role: 'assistant',
          sources,
          timestamp: assistantMessage.createdAt,
        },
        processingTime: Date.now() - startTime,
      };

    } catch (error) {
      log.error('RAG chat failed', {
        error: error instanceof Error ? error.message : String(error),
        userId: request.userId,
        learningContentId: request.learningContentId,
        conversationId: request.conversationId,
      });

      return {
        success: false,
        conversationId: request.conversationId || '',
        error: error instanceof Error ? error.message : 'Chat processing failed',
        processingTime: Date.now() - startTime,
      };
    }
  }

  /**
   * Generate streaming chat response
   */
  async chatStream(request: ChatRequest): Promise<StreamingChatResponse> {
    // Get or create conversation
    let conversationId = request.conversationId;
    if (!conversationId) {
      const conversation = await createConversation(this.db, {
        userId: request.userId,
        learningContentId: request.learningContentId,
        title: 'New Chat',
      });
      conversationId = conversation.id;
    }

    // Add user message
    await addMessage(this.db, {
      conversationId,
      content: request.message,
      senderRole: 'user',
      messageType: 'text',
    });

    // Create message ID for the assistant response
    const messageId = crypto.randomUUID();

    // Perform RAG search with debugging
    log.info(`[RAG Chat Stream] Performing search for: "${request.message}"`, {
      learningContentId: request.learningContentId,
      limit: this.config.searchOptions.limit,
      similarityThreshold: this.config.searchOptions.similarityThreshold,
    });

    const searchResults = await this.ragSearchService.searchContentScoped(
      request.message,
      [request.learningContentId],
      {
        limit: this.config.searchOptions.limit,
        similarityThreshold: this.config.searchOptions.similarityThreshold,
        enrichWithSourceContent: this.config.searchOptions.includeSourceContent,
      }
    );

    log.info(`[RAG Chat Stream] 🔍 Vectorize Search Results`, {
      resultsFound: searchResults.results.length,
      scores: searchResults.results.map(r => r.score),
      searchQuery: request.message,
      learningContentId: request.learningContentId,
      searchConfig: {
        limit: this.config.searchOptions.limit,
        similarityThreshold: this.config.searchOptions.similarityThreshold,
      },
      results: searchResults.results.map((result, index) => ({
        index: index + 1,
        score: result.score,
        stepTitle: result.metadata.stepTitle || 'Unknown',
        stepId: result.metadata.stepId,
        contentPreview: result.content.substring(0, 150) + (result.content.length > 150 ? '...' : ''),
        chunkIndex: result.metadata.chunkIndex,
        learningContentId: result.metadata.learningContentId,
      })),
      totalContextLength: searchResults.results.reduce((sum, r) => sum + r.content.length, 0),
      hasRelevantContent: searchResults.results.length > 0,
      contentSource: searchResults.results.length > 0 ? 'learning_materials' : 'general_knowledge',
      workaroundStatus: searchResults.results.length > 0 ? 'SUCCESS - Content found' : 'ISSUE - No content found (metadata filtering bug)',
    });

    // Get conversation context
    const conversation = await getConversationWithMessages(this.db, conversationId, request.userId, {
      limit: this.config.contextWindow.maxMessages,
      orderBy: 'asc',
    });

    // Create streaming response
    const stream = this.generateStreamingResponse(
      request.message,
      searchResults.results,
      conversation?.messages || [],
      request.learningContentId,
      conversationId,
      messageId
    );

    return {
      conversationId,
      messageId,
      stream,
    };
  }

  /**
   * Generate AI response using retrieved context
   */
  private async generateResponse(
    userMessage: string,
    searchResults: any[],
    conversationHistory: any[],
    learningContentId: string
  ): Promise<string> {
    // Get learning content metadata
    const learningContent = await getLearningContentById(this.db, learningContentId);
    
    // Build context from search results
    const contextChunks = searchResults.map((result, index) => 
      `[Context ${index + 1}] (from: ${result.metadata.stepTitle})\n${result.content}`
    ).join('\n\n');

    // Get conversation ID from the most recent message
    const conversationId = conversationHistory.length > 0 ? conversationHistory[0].conversationId : null;
    
    // Build enhanced context if we have a conversation
    let systemPrompt: string;
    if (conversationId && conversationHistory.length > 0) {
      // Create a conversation object for the context builder
      const conversation = {
        id: conversationId,
        messages: conversationHistory
      } as any;
      
      const enhancedContext = await this.contextBuilder.buildEnhancedContext(conversation, this.db);
      systemPrompt = this.contextBuilder.buildMemoryAwareSystemPrompt(
        enhancedContext,
        searchResults,
        learningContent
      );
    } else {
      // Fallback to basic system prompt for new conversations
      systemPrompt = this.buildSystemPrompt(learningContent, contextChunks);
    }

    // Build conversation history
    const messages: CoreMessage[] = [
      {
        role: 'system',
        content: systemPrompt,
      },
    ];

    // Add recent conversation history
    const recentMessages = conversationHistory.slice(-6); // Last 6 messages for context
    for (const msg of recentMessages) {
      if (msg.senderRole === 'user' || msg.senderRole === 'assistant') {
        messages.push({
          role: msg.senderRole === 'user' ? 'user' : 'assistant',
          content: msg.content,
        });
      }
    }

    // Add current user message if not already included
    if (recentMessages.length === 0 || recentMessages[recentMessages.length - 1]?.content !== userMessage) {
      messages.push({
        role: 'user',
        content: userMessage,
      });
    }

    console.log("KOKONUT-this.env", this.env)

    console.log("KOKONUT-this.env", this.env)

    // Generate response using OpenRouter with API key
    // Access the API key directly from the environment object
    const apiKey = this.env?.OPENROUTER_API_KEY || process.env.OPENROUTER_API_KEY;

    if (!apiKey) {
      throw new Error('OpenRouter API key is missing. Pass it using the \'apiKey\' parameter or the OPENROUTER_API_KEY environment variable.');
    }

    const openrouter = createOpenRouter({ apiKey });
    const model = openrouter(this.config.aiModel.model);

    const result = await generateText({
      model,
      messages,
      temperature: this.config.aiModel.temperature,
      maxTokens: this.config.aiModel.maxTokens,
    });

    return result.text;
  }

  /**
   * Generate streaming AI response
   */
  private generateStreamingResponse(
    userMessage: string,
    searchResults: any[],
    conversationHistory: any[],
    learningContentId: string,
    conversationId: string,
    messageId: string
  ): ReadableStream<Uint8Array> {
    const encoder = new TextEncoder();
    
    return new ReadableStream({
      start: async (controller) => {
        try {
          // This is a simplified streaming implementation
          // In a real implementation, you'd use the AI provider's streaming capabilities
          const response = await this.generateResponse(
            userMessage,
            searchResults,
            conversationHistory,
            learningContentId
          );

          // Extract and update conversation memory for streaming responses too
          if (conversationHistory.length >= 3) {
            try {
              await this.contextBuilder.buildEnhancedContext(
                { id: conversationId, messages: conversationHistory } as any,
                this.db
              );
            } catch (error) {
              log.warn('Failed to update conversation memory in streaming', {
                conversationId,
                error: error instanceof Error ? error.message : String(error),
              });
            }
          }

          // Simulate streaming by sending chunks as Server-Sent Events
          const chunks = response.split(' ');
          for (let i = 0; i < chunks.length; i++) {
            const chunk = chunks[i] + (i < chunks.length - 1 ? ' ' : '');
            // Format as SSE event
            const sseData = `data: ${JSON.stringify({ content: chunk })}\n\n`;
            controller.enqueue(encoder.encode(sseData));
            
            // Small delay to simulate streaming
            await new Promise(resolve => setTimeout(resolve, 50));
          }

          // Prepare metadata with sources
          const metadata = {
            sources: this.deduplicateSources(searchResults.map(result => ({
              stepId: result.metadata.stepId,
              stepTitle: result.metadata.stepTitle,
              chunkIndex: result.metadata.chunkIndex,
              score: result.score,
              contentId: result.metadata.learningContentId,
              contentTitle: result.sourceContent?.title,
            }))),
            model: this.config.aiModel.model,
          };

          // Send metadata event before done
          const metadataEvent = `data: ${JSON.stringify({ metadata })}\n\n`;
          controller.enqueue(encoder.encode(metadataEvent));

          // Send end event
          const endEvent = `data: ${JSON.stringify({ done: true })}\n\n`;
          controller.enqueue(encoder.encode(endEvent));

          // Save the complete message to database
          await addMessage(this.db, {
            conversationId,
            content: response,
            senderRole: 'assistant',
            messageType: 'text',
            metadata,
          });

          controller.close();
        } catch (error) {
          controller.error(error);
        }
      },
    });
  }

  /**
   * Build system prompt with retrieved context
   */
  private buildSystemPrompt(learningContent: any, contextChunks: string): string {
    const hasContext = contextChunks && contextChunks.trim().length > 0;

    return `You are an AI teaching assistant helping users understand learning content. You have access to specific learning materials and should provide helpful, accurate responses.

LEARNING CONTENT INFORMATION:
Title: ${learningContent?.title || 'Unknown'}
Description: ${learningContent?.description || 'No description available'}
Level: ${learningContent?.learningLevel || 'Unknown'}

RETRIEVED CONTEXT FROM LEARNING MATERIALS:
${hasContext ? contextChunks : 'No specific context was retrieved from the learning materials for this question.'}

CRITICAL INSTRUCTIONS FOR SOURCE ATTRIBUTION:
${hasContext ? `
✅ CONTEXT AVAILABLE: You have relevant content from the learning materials above.
- Base your answer primarily on the provided context
- Start your response with "Based on the learning materials:" or similar
- Quote or reference specific information from the context
- If you need to add general knowledge, clearly distinguish it: "Additionally, from general knowledge..."
` : `
⚠️ NO CONTEXT RETRIEVED: No relevant content was found in the learning materials for this question.
- You should still provide a helpful and informative response using your general knowledge
- Start your response with: "While I don't have specific information about this in the current learning materials, I can help explain this topic from general knowledge:"
- Provide a comprehensive explanation using your general knowledge
- After your explanation, suggest: "For more specific details related to ${learningContent?.title || 'this learning content'}, you might want to check if there are related sections in the materials or rephrase your question."
- Be educational and helpful while being transparent about your information source
`}

RESPONSE GUIDELINES:
1. Always be transparent about your information source (learning materials vs. general knowledge)
2. Be conversational and helpful while maintaining source clarity
3. Reference specific sections when using learning material context
4. If mixing sources, clearly separate them in your response
5. Encourage active learning and ask follow-up questions when appropriate

Please provide a helpful response that clearly indicates whether you're using information from the learning materials or general knowledge.`;
  }

  /**
   * Generate conversation title based on first user message
   */
  private async generateConversationTitle(
    conversationId: string,
    userId: string,
    firstMessage: string
  ): Promise<void> {
    try {
      // Simple title generation - take first few words
      const words = firstMessage.split(' ').slice(0, 8);
      let title = words.join(' ');
      
      if (firstMessage.split(' ').length > 8) {
        title += '...';
      }

      // Capitalize first letter
      title = title.charAt(0).toUpperCase() + title.slice(1);

      await updateConversationTitle(this.db, conversationId, userId, title);
    } catch (error) {
      log.warn('Failed to generate conversation title', {
        conversationId,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  /**
   * Get conversation with messages
   */
  async getConversation(
    conversationId: string,
    userId: string
  ): Promise<ConversationWithMessages | null> {
    return getConversationWithMessages(this.db, conversationId, userId);
  }

  /**
   * Get user's conversations for specific content
   */
  async getUserConversations(
    userId: string,
    learningContentId: string
  ): Promise<ConversationWithMessages[]> {
    return getUserConversationsForContent(this.db, userId, learningContentId);
  }

  /**
   * Deduplicate sources to avoid showing multiple entries for the same step
   *
   * When multiple chunks from the same step are returned, we consolidate them
   * into a single source entry with the highest score.
   */
  private deduplicateSources(sources: Array<{
    stepId: string;
    stepTitle: string;
    chunkIndex: number;
    score: number;
    contentId?: string;
    contentTitle?: string;
  }>): Array<{
    stepId: string;
    stepTitle: string;
    chunkIndex: number;
    score: number;
    contentId?: string;
    contentTitle?: string;
  }> {
    const sourceMap = new Map<string, typeof sources[0]>();

    for (const source of sources) {
      const key = `${source.stepId}-${source.stepTitle}`;
      const existing = sourceMap.get(key);

      if (!existing || source.score > existing.score) {
        // Keep the source with the highest score for each step
        sourceMap.set(key, source);
      }
    }

    // Return deduplicated sources sorted by score (highest first)
    return Array.from(sourceMap.values()).sort((a, b) => b.score - a.score);
  }

  /**
   * Handle topic acceptance from user
   */
  async acceptTopic(conversationId: string, topic: string): Promise<void> {
    try {
      await this.contextBuilder.handleTopicAcceptance(conversationId, topic, this.db);
      log.info('Topic accepted', { conversationId, topic });
    } catch (error) {
      log.error('Failed to accept topic', {
        conversationId,
        topic,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Handle topic rejection from user
   */
  async rejectTopic(conversationId: string, topic: string): Promise<void> {
    try {
      await this.contextBuilder.handleTopicRejection(conversationId, topic, this.db);
      log.info('Topic rejected', { conversationId, topic });
    } catch (error) {
      log.error('Failed to reject topic', {
        conversationId,
        topic,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }
}

/**
 * Create RAG chat service
 */
export function createRagChatService(
  db: Database,
  vectorizeIndex?: VectorizeIndex,
  env?: Record<string, any>,
  config?: Partial<RagChatServiceConfig>
): RagChatService {
  return new RagChatService(db, vectorizeIndex, env, config);
}