/**
 * Type definitions for the services library
 */

// Learning content types
export interface LearningContentStep {
  id: string;
  title: string;
  icon: string;
  blocks: Array<{
    id: string;
    type: string;
    data: any;
    isEditing?: boolean;
  }>;
}

export interface LearningContent {
  id: string;
  title: string;
  description: string;
  steps: LearningContentStep[];
  learningLevel: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadingTime: number;
  isPublic: boolean;
  tags: string[];
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  aiMetadata?: {
    aiModel?: string;
    generatedAt?: string;
    contentTypes?: string[];
    originalPrompt?: string;
    contentStructure?: 'kwaci-primer' | 'standard';
    sectionCount?: number;
    focusAreas?: string;
  };
}

// Chunking types
export interface ContentChunk {
  id: string;
  text: string;
  metadata: ChunkMetadata;
  tokens?: number;
}

export interface ChunkMetadata {
  learningContentId: string;
  stepId: string;
  stepTitle: string;
  chunkIndex: number;
  blockId?: string;
  blockType?: string;
  chunkingStrategy: ChunkingStrategy;
  chunkSize: number;
  chunkOverlap?: number;
}

export type ChunkingStrategy =
  | 'fixed-size'
  | 'sentence-based'
  | 'paragraph-based'
  | 'semantic'
  | 'content-aware';

// Embedding types
export interface EmbeddingConfig {
  model: string;
  dimensions: number;
  provider: 'voyage' | 'openai' | 'cloudflare';
}

export interface EmbeddingResult {
  embedding: number[];
  model: string;
  dimensions: number;
  tokenCount?: number;
}

// Vector store types
export interface VectorStoreConfig {
  indexName: string;
  embeddingConfig: EmbeddingConfig;
  namespace?: string;
}

export interface IndexedContent {
  vectorId: string;
  contentId: string;
  chunkId: string;
  embedding: number[];
  metadata: VectorMetadata;
  createdAt: Date;
}

export interface VectorMetadata {
  learningContentId: string;
  stepId: string;
  stepTitle: string;
  chunkIndex: number;
  chunkText: string;
  embeddingModel: string;
  embeddingDimension: number;
  processingMetadata: {
    chunkingStrategy: ChunkingStrategy;
    chunkSize: number;
    chunkOverlap?: number;
    tokenCount?: number;
  };
}

// Service interfaces
export interface EmbeddingService {
  generateEmbedding(text: string, inputType?: 'document' | 'query'): Promise<EmbeddingResult>;
  generateEmbeddings(texts: string[], inputType?: 'document' | 'query'): Promise<EmbeddingResult[]>;
  getConfig(): EmbeddingConfig;
}

export interface ChunkingService {
  chunkContent(content: LearningContent, strategy?: ChunkingStrategy): Promise<ContentChunk[]>;
  chunkStep(step: LearningContentStep, strategy?: ChunkingStrategy): Promise<ContentChunk[]>;
  chunkText(text: string, strategy?: ChunkingStrategy, metadata?: Partial<ChunkMetadata>): Promise<ContentChunk[]>;
}

// Search types
export interface SearchOptions {
  limit?: number;
  similarityThreshold?: number;
  includeMetadata?: boolean;
}

export interface SearchResult {
  id: string;
  content: string;
  score: number;
  metadata: {
    learningContentId: string;
    stepId: string;
    stepTitle: string;
    chunkIndex: number;
    embeddingModel: string;
    embeddingDimension: number;
  };
  sourceContent?: {
    id: string;
    title: string;
    description: string;
  };
}

export interface SearchResponse {
  results: SearchResult[];
  query: string;
  totalResults: number;
  processingTimeMs: number;
  contentScope?: string[]; // Content IDs that were searched
}

export interface VectorSearchService {
  searchContent(query: string, options?: SearchOptions): Promise<SearchResponse>;
  searchContentScoped(query: string, contentIds: string[], options?: SearchOptions): Promise<SearchResponse>;
}

export interface VectorIndexingService {
  indexContent(content: LearningContent): Promise<IndexedContent[]>;
  indexChunks(chunks: ContentChunk[]): Promise<IndexedContent[]>;
  isContentIndexed(contentId: string): Promise<boolean>;
  deleteContentIndex(contentId: string): Promise<void>;
  updateContentIndex(content: LearningContent): Promise<IndexedContent[]>;
  // Add search capabilities to the indexing service
  searchContent(query: string, options?: SearchOptions): Promise<SearchResponse>;
  searchContentScoped(query: string, contentIds: string[], options?: SearchOptions): Promise<SearchResponse>;
}

// Indexing result types
export interface IndexingResult {
  success: boolean;
  contentId: string;
  chunksIndexed: number;
  indexedContent: IndexedContent[];
  processingTimeMs: number;
  error?: string;
}

export interface BatchIndexingOptions {
  concurrency?: number;
  retryFailures?: boolean;
  skipExisting?: boolean;
  onProgress?: (completed: number, total: number, current?: string) => void;
  onError?: (contentId: string, error: Error) => void;
}

export interface BatchIndexingResult {
  success: boolean;
  totalItems: number;
  successfulItems: number;
  failedItems: number;
  results: IndexingResult[];
  processingTimeMs: number;
  errors?: string[];
}

// Content indexing statistics
export interface ContentIndexingStats {
  stepCount: number;
  blockCount: number;
  blockTypes: string[];
  estimatedTextLength: number;
  estimatedTokens: number;
  estimatedChunks: number;
  estimatedProcessingTime: {
    chunkingTimeMs: number;
    embeddingTimeMs: number;
    indexingTimeMs: number;
    totalTimeMs: number;
  };
}

// Content validation result
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// Error types
export class ServicesError extends Error {
  public code: string;

  constructor(message: string, code: string, cause?: Error) {
    super(message);
    this.name = 'ServicesError';
    this.code = code;
    if (cause) {
      this.stack = `${this.stack}\nCaused by: ${cause.stack}`;
    }
  }
}

export class EmbeddingError extends ServicesError {
  constructor(message: string, cause?: Error) {
    super(message, 'EMBEDDING_ERROR', cause);
    this.name = 'EmbeddingError';
  }
}

export class ChunkingError extends ServicesError {
  constructor(message: string, cause?: Error) {
    super(message, 'CHUNKING_ERROR', cause);
    this.name = 'ChunkingError';
  }
}

export class IndexingError extends ServicesError {
  constructor(message: string, cause?: Error) {
    super(message, 'INDEXING_ERROR', cause);
    this.name = 'IndexingError';
  }
}