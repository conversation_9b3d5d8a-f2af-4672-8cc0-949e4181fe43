/**
 * RAG (Retrieval-Augmented Generation) Library
 *
 * Centralized services library for AI-powered learning content indexing and retrieval.
 *
 * This library provides:
 * - Vector indexing services using Cloudflare Vectorize
 * - Content chunking strategies for learning materials
 * - Embedding services with Voyage AI
 * - Deduplication and metadata management
 * - Future-ready interfaces for AI retrieval features
 */

// Configuration
export * from './config/services-config';

// Core services
export * from './services/embedding-service';
export * from './services/chunking-service';
export * from './services/vector-indexing-service';
export * from './services/rag-search-service';
export * from './services/rag-chat-service';

// Types and interfaces
export * from './types/index';

// Utilities
export * from './utils/index';

// Main service factory functions for easy integration
export { createLearningContentIndexingService } from './services/learning-content-indexing-service';
export { createRagSearchService } from './services/rag-search-service';
export { createRagChatService } from './services/rag-chat-service';