import { useState, useCallback } from 'react';

export interface StreamingMessage {
  id: string;
  content: string;
  senderRole: 'user' | 'assistant';
  createdAt: Date;
  status: 'streaming' | 'complete' | 'error';
  tempId?: string;
  metadata?: {
    sources?: Array<{
      stepId: string;
      stepTitle: string;
      contentTitle?: string;
      chunkIndex: number;
      score: number;
    }>;
    systemContext?: string;
    processingTime?: number;
    model?: string;
  };
}

export interface UseStreamingChatOptions {
  learningContentId: string;
  onMessageComplete?: (message: StreamingMessage, userMessage?: any) => void;
  onError?: (error: Error) => void;
}

export function useStreamingChat({
  learningContentId,
  onMessageComplete,
  onError,
}: UseStreamingChatOptions) {
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentStreamingMessage, setCurrentStreamingMessage] = useState<StreamingMessage | null>(null);

  const sendStreamingMessage = useCallback(async (
    conversationId: string,
    content: string,
    userMessage?: any
  ) => {
    setIsStreaming(true);
    setError(null);
    
    const aiTempId = crypto.randomUUID();
    const streamingMessage: StreamingMessage = {
      id: aiTempId,
      content: '',
      senderRole: 'assistant',
      createdAt: new Date(),
      status: 'streaming',
      tempId: aiTempId,
    };
    
    setCurrentStreamingMessage(streamingMessage);

    try {
      const response = await fetch('/api/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
        },
        body: JSON.stringify({
          message: content,
          conversationId,
          learningContentId,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body reader available');
      }

      const decoder = new TextDecoder();
      let buffer = '';
      let accumulatedContent = '';
      let finalMetadata: StreamingMessage['metadata'] | undefined;

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          break;
        }

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || ''; // Keep incomplete line in buffer

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6).trim();
            
            if (!data) continue;
            
            try {
              const parsed = JSON.parse(data);
              
              if (parsed.done) {
                // Stream complete
                const finalMessage: StreamingMessage = {
                  ...streamingMessage,
                  content: accumulatedContent,
                  status: 'complete',
                  metadata: finalMetadata,
                };
                setCurrentStreamingMessage(finalMessage);
                onMessageComplete?.(finalMessage, userMessage);
                return finalMessage;
              }
              
              if (parsed.content) {
                accumulatedContent += parsed.content;
                setCurrentStreamingMessage(prev => prev ? {
                  ...prev,
                  content: accumulatedContent,
                } : null);
              }
              
              if (parsed.metadata) {
                finalMetadata = parsed.metadata;
              }
              
              if (parsed.error) {
                throw new Error(parsed.error || 'Streaming error occurred');
              }
            } catch (parseError) {
              console.warn('Failed to parse SSE data:', data, parseError);
            }
          }
        }
      }
      
      // If we reach here without [DONE], consider it complete
      const finalMessage: StreamingMessage = {
        ...streamingMessage,
        content: accumulatedContent,
        status: 'complete',
        metadata: finalMetadata,
      };
      setCurrentStreamingMessage(finalMessage);
      onMessageComplete?.(finalMessage, userMessage);
      return finalMessage;
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send message';
      setError(errorMessage);
      
      const errorStreamingMessage: StreamingMessage = {
        ...streamingMessage,
        content: 'Failed to send message. Please try again.',
        status: 'error',
      };
      setCurrentStreamingMessage(errorStreamingMessage);
      
      onError?.(err instanceof Error ? err : new Error(errorMessage));
      throw err;
    } finally {
      setIsStreaming(false);
    }
  }, [learningContentId, onMessageComplete, onError]);

  const clearCurrentMessage = useCallback(() => {
    setCurrentStreamingMessage(null);
  }, []);

  return {
    sendStreamingMessage,
    isStreaming,
    error,
    currentStreamingMessage,
    clearCurrentMessage,
  };
}