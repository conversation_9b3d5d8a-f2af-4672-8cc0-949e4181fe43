import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from '@tanstack/react-query';
import { apiClient, createQueryKey } from '../data-fetching';
import type {
  LearningContent,
  NewLearningContent,
  LearningContentListResponse
} from '~/db/schema/learning-content';
import type { LearningContentWithProgress, MyLearningFilters } from '~/db/services/learning-content';

export interface GetLearningContentParams {
  query?: string;
  contentType?: 'standard' | 'kwaci-primer';
  learningLevel?: 'beginner' | 'intermediate' | 'advanced';
  isPublic?: boolean;
  limit?: number;
  offset?: number;
}

export interface CreateLearningContentParams {
  title: string;
  description?: string;
  content: {
    steps: Array<{
      id: string;
      title: string;
      icon?: string;
      blocks: Array<{
        id: string;
        type: string;
        data: any;
        isEditing?: boolean;
      }>;
    }>;
    estimatedReadingTime?: number;
    metadata?: any;
  };
  contentType?: 'standard' | 'kwaci-primer';
  learningLevel?: 'beginner' | 'intermediate' | 'advanced';
  isPublic?: boolean;
  aiMetadata?: {
    aiModel: string;
    generatedAt: string;
    contentTypes: string[];
    learningLevel: string;
    topic: string;
    preferredContentTypes?: string[];
  };
}

// Query hooks
export function useGetAllLearningContent(params: GetLearningContentParams = {}) {
  const {
    query,
    contentType,
    learningLevel,
    isPublic,
    limit = 100,
    offset = 0,
  } = params;

  return useQuery<LearningContent[], Error>({
    queryKey: createQueryKey('learningContent.getAll', {
      query,
      contentType,
      learningLevel,
      isPublic,
      limit,
      offset,
    }),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
      });

      if (query) searchParams.set('query', query);
      if (contentType) searchParams.set('contentType', contentType);
      if (learningLevel) searchParams.set('learningLevel', learningLevel);
      if (isPublic !== undefined) searchParams.set('isPublic', isPublic.toString());

      return apiClient.get<LearningContent[]>(`/learning?${searchParams}`);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useGetLearningContent(id: string) {
  return useQuery<LearningContent, Error>({
    queryKey: createQueryKey('learningContent.get', { id }),
    queryFn: async () => {
      return apiClient.get<LearningContent>(`/learning/${id}`);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Mutation hooks
export function useCreateLearningContent() {
  const queryClient = useQueryClient();

  return useMutation<LearningContent, Error, CreateLearningContentParams>({
    mutationFn: async (params) => {
      return apiClient.post('/learning', params);
    },
    onSuccess: () => {
      // Invalidate learning content queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['learningContent'],
      });
    },
  });
}

export function useUpdateLearningContent(id: string) {
  const queryClient = useQueryClient();

  return useMutation<LearningContent, Error, Partial<CreateLearningContentParams>>({
    mutationFn: async (params) => {
      return apiClient.put(`/learning/${id}`, params);
    },
    onSuccess: () => {
      // Invalidate learning content queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['learningContent'],
      });
    },
  });
}

export function useDeleteLearningContent() {
  const queryClient = useQueryClient();

  return useMutation<void, Error, string>({
    mutationFn: async (id) => {
      return apiClient.delete(`/learning/${id}`);
    },
    onSuccess: () => {
      // Invalidate learning content queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['learningContent'],
      });
    },
  });
}

// Generate content similar to an existing learning content item
export function useGenerateSimilarContent() {
  const queryClient = useQueryClient();

  return useMutation<
    { id: string; content: LearningContent },
    Error,
    { learningContentId: string; titleHint?: string }
  >({
    mutationFn: async (params) => {
      return apiClient.post('/content/generate-similar', params);
    },
    onSuccess: () => {
      // Invalidate lists so the new content appears
      queryClient.invalidateQueries({ queryKey: ['learningContent'] });
      queryClient.invalidateQueries({ queryKey: ['myLearning'] });
    },
  });
}

// My Learning Content Hooks with Infinite Query
export function useGetMyLearningContent(filters: Omit<MyLearningFilters, 'offset'> = {}) {
  const limit = filters.limit || 12;
  
  return useInfiniteQuery<
    { success: boolean; content: LearningContentWithProgress[]; pagination: { limit: number; offset: number; hasMore: boolean } },
    Error,
    { success: boolean; content: LearningContentWithProgress[]; pagination: { limit: number; offset: number; hasMore: boolean } },
    any[],
    number
  >({
    queryKey: createQueryKey('myLearning.infinite', { ...filters, limit }),
    queryFn: async ({ pageParam = 0 }) => {
      const searchParams = new URLSearchParams();

      // Add pagination
      searchParams.set('limit', limit.toString());
      searchParams.set('offset', pageParam.toString());

      // Add filters to search params
      if (filters.search) searchParams.set('search', filters.search);
      if (filters.learningLevel) searchParams.set('learningLevel', filters.learningLevel);
      if (filters.contentType) searchParams.set('contentType', filters.contentType);
      if (filters.completionStatus) searchParams.set('completionStatus', filters.completionStatus);
      if (filters.isPublic !== undefined) searchParams.set('isPublic', filters.isPublic.toString());
      if (filters.sortBy) searchParams.set('sortBy', filters.sortBy);
      if (filters.sortOrder) searchParams.set('sortOrder', filters.sortOrder);

      if (filters.tags && filters.tags.length > 0) {
        searchParams.set('tags', filters.tags.join(','));
      }

      if (filters.dateRange) {
        searchParams.set('dateFrom', filters.dateRange.from.toISOString());
        searchParams.set('dateTo', filters.dateRange.to.toISOString());
      }

      if (filters.readingTimeRange) {
        searchParams.set('readingTimeMin', filters.readingTimeRange.min.toString());
        searchParams.set('readingTimeMax', filters.readingTimeRange.max.toString());
      }

      return apiClient.get<{ success: boolean; content: LearningContentWithProgress[]; pagination: { limit: number; offset: number; hasMore: boolean } }>(`/my-learning?${searchParams}`);
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage.pagination.hasMore) return undefined;
      return lastPage.pagination.offset + lastPage.pagination.limit;
    },
    initialPageParam: 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useDeleteMyLearningContent() {
  const queryClient = useQueryClient();

  return useMutation<{ success: boolean; message: string }, Error, string>({
    mutationFn: async (contentId) => {
      return apiClient.post('/my-learning', {
        action: 'delete',
        contentId,
      });
    },
    onSuccess: () => {
      // Invalidate my learning queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['myLearning'],
      });
      // Also invalidate general learning content queries
      queryClient.invalidateQueries({
        queryKey: ['learningContent'],
      });
    },
  });
}

export function useToggleContentPublic() {
  const queryClient = useQueryClient();

  return useMutation<{ success: boolean; data: LearningContent; message: string }, Error, string>({
    mutationFn: async (contentId) => {
      return apiClient.post('/my-learning', {
        action: 'togglePublic',
        contentId,
      });
    },
    onSuccess: () => {
      // Invalidate my learning queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['myLearning'],
      });
      // Also invalidate general learning content queries
      queryClient.invalidateQueries({
        queryKey: ['learningContent'],
      });
    },
  });
}

// Utility hook to get query client utils (similar to tRPC utils)
export function useLearningContentUtils() {
  const queryClient = useQueryClient();

  return {
    learningContent: {
      getAll: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['learningContent.getAll'],
          });
        },
        refetch: (params?: GetLearningContentParams) => {
          const queryKey = createQueryKey('learningContent.getAll', params || {});
          return queryClient.refetchQueries({ queryKey });
        },
      },
      get: {
        invalidate: (id?: string) => {
          if (id) {
            queryClient.invalidateQueries({
              queryKey: createQueryKey('learningContent.get', { id }),
            });
          } else {
            queryClient.invalidateQueries({
              queryKey: ['learningContent.get'],
            });
          }
        },
      },
    },
    myLearning: {
      getAll: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['myLearning'],
          });
        },
        refetch: (filters?: MyLearningFilters) => {
          const queryKey = createQueryKey('myLearning.getAll', filters || {});
          return queryClient.refetchQueries({ queryKey });
        },
      },
    },
  };
}
