import { useCallback, useEffect } from 'react';
import { useLocation } from 'react-router';
import { useUIStore } from '~/lib/stores/ui-store';

export interface BreadcrumbItem {
  label: string;
  href: string;
  isEllipsis?: boolean;
}

export interface DynamicBreadcrumbData {
  title?: string;
  href?: string;
  parentTitle?: string;
  parentHref?: string;
  customParents?: BreadcrumbItem[];
}

// Route configuration for automatic breadcrumb generation
const ROUTE_BREADCRUMB_CONFIG: Record<string, BreadcrumbItem[]> = {
  '/dashboard': [
    { label: 'Dashboard', href: '/dashboard' }
  ],
  '/dashboard/my-learning': [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'My Learning', href: '/dashboard/my-learning' }
  ],
  '/dashboard/learn': [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Learn', href: '/dashboard/learn' }
  ],
  '/dashboard/quizzes': [
    { label: 'Dashboard', href: '/dashboard' },
    { label: 'Quizzes', href: '/dashboard/quizzes' }
  ],
};

// Dynamic route patterns with parameter matching
// NOTE: Order matters! More specific patterns should come first
const DYNAMIC_ROUTE_PATTERNS = [
  {
    // More specific pattern for creating new kwaci content
    pattern: /^\/dashboard\/learn\/kwaci\/new$/,
    generator: () => [
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'Learn', href: '/dashboard/learn' },
      { label: 'Create Kwaci Content', href: '/dashboard/learn/kwaci/new' }
    ]
  },
  {
    // General pattern for learn content details (UUID-based)
    pattern: /^\/dashboard\/learn\/([a-f0-9-]+)$/,
    generator: (matches: RegExpMatchArray, data?: DynamicBreadcrumbData) => {
      // Use custom parents if provided, otherwise default to My Learning for learn detail pages
      if (data?.customParents) {
        return [
          ...data.customParents,
          { 
            label: data?.title || 'Content', 
            href: data?.href || `/dashboard/learn/${matches[1]}` 
          }
        ];
      }
      
      // Default: show My Learning as parent for learn detail pages
      return [
        { label: 'Dashboard', href: '/dashboard' },
        { label: 'My Learning', href: '/dashboard/my-learning' },
        { 
          label: data?.title || 'Content', 
          href: data?.href || `/dashboard/learn/${matches[1]}` 
        }
      ];
    }
  },
  {
    pattern: /^\/dashboard\/quiz\/(.+)$/,
    generator: (matches: RegExpMatchArray, data?: DynamicBreadcrumbData) => [
      { label: 'Dashboard', href: '/dashboard' },
      { label: 'Quizzes', href: '/dashboard/quizzes' },
      { 
        label: data?.title || 'Quiz', 
        href: data?.href || `/dashboard/quiz/${matches[1]}` 
      }
    ]
  }
];

/**
 * Intelligently collapse breadcrumbs when there are too many items
 * Shows: Home > ... > Parent > Current (when > 4 items)
 * Shows: Home > Item1 > Item2 > Current (when <= 4 items)
 */
function collapseBreadcrumbs(breadcrumbs: BreadcrumbItem[], maxVisible = 4): BreadcrumbItem[] {
  if (breadcrumbs.length <= maxVisible) {
    return breadcrumbs;
  }

  // Always show first, last, and second-to-last items
  const first = breadcrumbs[0];
  const last = breadcrumbs[breadcrumbs.length - 1];
  const secondToLast = breadcrumbs[breadcrumbs.length - 2];
  
  // If we have more than 4 items, collapse the middle
  if (breadcrumbs.length > 4) {
    return [
      first,
      { label: '...', href: '', isEllipsis: true },
      secondToLast,
      last
    ];
  }

  return breadcrumbs;
}

/**
 * Generate breadcrumbs from current pathname
 */
function generateBreadcrumbs(pathname: string, dynamicData?: DynamicBreadcrumbData): BreadcrumbItem[] {
  // Check static route configurations first
  if (ROUTE_BREADCRUMB_CONFIG[pathname]) {
    return ROUTE_BREADCRUMB_CONFIG[pathname];
  }

  // Check dynamic route patterns
  for (const { pattern, generator } of DYNAMIC_ROUTE_PATTERNS) {
    const matches = pathname.match(pattern);
    if (matches) {
      return generator(matches, dynamicData);
    }
  }

  // Fallback: generate breadcrumbs from path segments
  const segments = pathname.split('/').filter(Boolean);
  const breadcrumbs: BreadcrumbItem[] = [];
  
  let currentPath = '';
  segments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    
    // Convert segment to readable label
    const label = segment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
    
    breadcrumbs.push({
      label: index === segments.length - 1 && dynamicData?.title ? dynamicData.title : label,
      href: currentPath
    });
  });

  return breadcrumbs;
}

/**
 * Custom hook for managing breadcrumbs with intelligent collapsing
 */
export function useBreadcrumbs() {
  const location = useLocation();
  const { setBreadcrumbs, navigation } = useUIStore();

  const updateBreadcrumbs = useCallback((dynamicData?: DynamicBreadcrumbData) => {
    const pathname = location.pathname;
    
    // Generate base breadcrumbs
    let breadcrumbs = generateBreadcrumbs(pathname, dynamicData);
    
    // Add "Kwaci Learning" as root if not already present and not on root dashboard
    if (pathname !== '/dashboard' && !breadcrumbs.some(b => b.label === 'Kwaci Learning')) {
      breadcrumbs = [
        { label: 'Kwaci Learning', href: '/dashboard' },
        ...breadcrumbs.filter(b => b.label !== 'Dashboard')
      ];
    }
    
    // Apply intelligent collapsing
    const collapsedBreadcrumbs = collapseBreadcrumbs(breadcrumbs);
    
    // Update the UI store
    setBreadcrumbs(collapsedBreadcrumbs);
  }, [location.pathname, setBreadcrumbs]);

  // Auto-update breadcrumbs when route changes
  useEffect(() => {
    updateBreadcrumbs();
  }, [updateBreadcrumbs]);

  return {
    updateBreadcrumbs,
    currentBreadcrumbs: navigation.breadcrumbs
  };
}

/**
 * Hook for pages to easily set their title in breadcrumbs
 */
export function useBreadcrumbTitle(title: string, href?: string) {
  const { updateBreadcrumbs } = useBreadcrumbs();
  
  useEffect(() => {
    if (title) {
      updateBreadcrumbs({ title, href });
    }
  }, [title, href, updateBreadcrumbs]);
}

/**
 * Hook for pages to set custom breadcrumb hierarchy
 */
export function useCustomBreadcrumbs(title: string, customParents?: BreadcrumbItem[], href?: string) {
  const { updateBreadcrumbs } = useBreadcrumbs();
  
  useEffect(() => {
    if (title) {
      updateBreadcrumbs({ title, href, customParents });
    }
  }, [title, href, customParents, updateBreadcrumbs]);
}