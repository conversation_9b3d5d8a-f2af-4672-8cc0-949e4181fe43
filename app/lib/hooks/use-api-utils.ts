import { useQueryClient } from '@tanstack/react-query';
import { createQuery<PERSON>ey } from '../data-fetching';

// Comprehensive API utils hook that mimics tRPC utils interface
export function useApiUtils() {
  const queryClient = useQueryClient();

  return {
    learningProgress: {
      getProgress: {
        invalidate: (params?: { contentId?: string }) => {
          if (params?.contentId) {
            queryClient.invalidateQueries({
              queryKey: createQueryKey('learningProgress.getProgress', { contentId: params.contentId }),
            });
          } else {
            queryClient.invalidateQueries({
              queryKey: ['learningProgress.getProgress'],
            });
          }
        },
        refetch: (params?: { contentId?: string }) => {
          if (params?.contentId) {
            const queryKey = createQueryKey('learningProgress.getProgress', { contentId: params.contentId });
            return queryClient.refetchQueries({ queryKey });
          } else {
            return queryClient.refetchQueries({
              queryKey: ['learningProgress.getProgress'],
            });
          }
        },
      },
      getLatestInProgress: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['learningProgress.getLatestInProgress'],
          });
        },
        refetch: () => {
          return queryClient.refetchQueries({
            queryKey: ['learningProgress.getLatestInProgress'],
          });
        },
      },
      getStats: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['learningProgress.stats'],
          });
        },
        refetch: () => {
          return queryClient.refetchQueries({
            queryKey: ['learningProgress.stats'],
          });
        },
      },
    },
    learningContent: {
      getAll: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['learningContent.getAll'],
          });
        },
        refetch: () => {
          return queryClient.refetchQueries({
            queryKey: ['learningContent.getAll'],
          });
        },
      },
      getMy: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['learningContent.getMy'],
          });
        },
        refetch: () => {
          return queryClient.refetchQueries({
            queryKey: ['learningContent.getMy'],
          });
        },
      },
      get: {
        invalidate: (params?: { id?: string }) => {
          if (params?.id) {
            queryClient.invalidateQueries({
              queryKey: createQueryKey('learningContent.get', { id: params.id }),
            });
          } else {
            queryClient.invalidateQueries({
              queryKey: ['learningContent.get'],
            });
          }
        },
        refetch: (params?: { id?: string }) => {
          if (params?.id) {
            const queryKey = createQueryKey('learningContent.get', { id: params.id });
            return queryClient.refetchQueries({ queryKey });
          } else {
            return queryClient.refetchQueries({
              queryKey: ['learningContent.get'],
            });
          }
        },
      },
    },
    quiz: {
      getAll: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['quiz.getAll'],
          });
        },
        refetch: () => {
          return queryClient.refetchQueries({
            queryKey: ['quiz.getAll'],
          });
        },
      },
      getHistoryByLearningContent: {
        invalidate: (params?: { learningContentId?: string }) => {
          if (params?.learningContentId) {
            queryClient.invalidateQueries({
              queryKey: createQueryKey('quiz.getHistoryByLearningContent', { learningContentId: params.learningContentId }),
            });
          } else {
            queryClient.invalidateQueries({
              queryKey: ['quiz.getHistoryByLearningContent'],
            });
          }
        },
        refetch: (params?: { learningContentId?: string }) => {
          if (params?.learningContentId) {
            const queryKey = createQueryKey('quiz.getHistoryByLearningContent', { learningContentId: params.learningContentId });
            return queryClient.refetchQueries({ queryKey });
          } else {
            return queryClient.refetchQueries({
              queryKey: ['quiz.getHistoryByLearningContent'],
            });
          }
        },
      },
      getLearningInsights: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['quiz.getLearningInsights'],
          });
        },
        refetch: () => {
          return queryClient.refetchQueries({
            queryKey: ['quiz.getLearningInsights'],
          });
        },
      },
    },
    chat: {
      getOrCreateConversation: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['chat.getOrCreateConversation'],
          });
        },
        refetch: () => {
          return queryClient.refetchQueries({
            queryKey: ['chat.getOrCreateConversation'],
          });
        },
      },
      getConversationHistory: {
        invalidate: (params?: { conversationId?: string }) => {
          if (params?.conversationId) {
            queryClient.invalidateQueries({
              queryKey: createQueryKey('chat.getConversationHistory', { conversationId: params.conversationId }),
            });
          } else {
            queryClient.invalidateQueries({
              queryKey: ['chat.getConversationHistory'],
            });
          }
        },
        refetch: (params?: { conversationId?: string }) => {
          if (params?.conversationId) {
            const queryKey = createQueryKey('chat.getConversationHistory', { conversationId: params.conversationId });
            return queryClient.refetchQueries({ queryKey });
          } else {
            return queryClient.refetchQueries({
              queryKey: ['chat.getConversationHistory'],
            });
          }
        },
      },
    },
    topics: {
      acceptTopic: {
        invalidate: () => {
          // Invalidate conversation history to refresh memory context
          queryClient.invalidateQueries({
            queryKey: ['chat.getConversationHistory'],
          });
        },
      },
      rejectTopic: {
        invalidate: () => {
          // Invalidate conversation history to refresh memory context
          queryClient.invalidateQueries({
            queryKey: ['chat.getConversationHistory'],
          });
        },
      },
    },
    interests: {
      getRecommendations: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['interests.recommendations'],
          });
        },
        refetch: () => {
          return queryClient.refetchQueries({
            queryKey: ['interests.recommendations'],
          });
        },
      },
    },
    conversations: {
      getMemory: {
        invalidate: (params?: { conversationId?: string }) => {
          if (params?.conversationId) {
            queryClient.invalidateQueries({
              queryKey: createQueryKey('conversations.getMemory', { conversationId: params.conversationId }),
            });
          } else {
            queryClient.invalidateQueries({
              queryKey: ['conversations.getMemory'],
            });
          }
        },
        refetch: (params?: { conversationId?: string }) => {
          if (params?.conversationId) {
            const queryKey = createQueryKey('conversations.getMemory', { conversationId: params.conversationId });
            return queryClient.refetchQueries({ queryKey });
          } else {
            return queryClient.refetchQueries({
              queryKey: ['conversations.getMemory'],
            });
          }
        },
      },
    },
  };
}
