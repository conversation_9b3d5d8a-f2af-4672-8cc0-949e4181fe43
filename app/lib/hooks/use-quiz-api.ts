import { useQuery, useInfiniteQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient, createQueryKey } from '../data-fetching';
import type {
  QuizGenerationConfig,
  QuizGenerationResponse,
  QuizGenerationData,
  QuizListResponse
} from '~/components/quiz/types';
import type {
  QuizzesFilters,
  QuizWithProgress,
  QuizzesResponse
} from '~/routes/api.quizzes';
import type {
  QuizAttemptsFilters,
  QuizAttemptsResponse
} from '~/routes/api.quiz-attempts';

// Types for quiz API
export interface Quiz {
  id: string;
  title: string;
  description?: string;
  questions: any[];
  difficulty: 'easy' | 'medium' | 'hard';
  isPublic: boolean;
  authorId: string;
  metadata?: any;
  createdAt: string;
  updatedAt: string;
}

export interface GetQuizzesParams {
  learningContentId?: string;
  limit?: number;
  offset?: number;
  includePublic?: boolean;
  difficulty?: 'easy' | 'medium' | 'hard';
}

export interface GetQuizHistoryParams {
  learningContentId?: string;
  limit?: number;
  offset?: number;
}

export interface CreateQuizParams {
  title: string;
  description?: string;
  questions: any[];
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  isPublic?: boolean;
  metadata?: any;
}

// Query hooks
export function useGetAllQuizzes(params: GetQuizzesParams = {}) {
  const {
    limit = 50,
    offset = 0,
    includePublic = true,
    difficulty,
  } = params;

  return useQuery<Quiz[], Error>({
    queryKey: createQueryKey('quiz.getAll', {
      limit,
      offset,
      includePublic,
      difficulty,
    }),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        includePublic: includePublic.toString(),
      });

      if (difficulty) searchParams.set('difficulty', difficulty);

      return apiClient.get<Quiz[]>(`/quiz?${searchParams}`);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useGetQuiz(id: string) {
  return useQuery<Quiz, Error>({
    queryKey: createQueryKey('quiz.get', { id }),
    queryFn: async () => {
      return apiClient.get<Quiz>(`/quiz/${id}`);
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useGetQuizHistoryByLearningContent(params: GetQuizHistoryParams) {
  const { learningContentId, limit = 10, offset = 0 } = params;

  return useQuery<any[], Error>({
    queryKey: createQueryKey('quiz.getHistoryByLearningContent', {
      learningContentId,
      limit,
      offset,
    }),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
      });

      if (learningContentId) {
        searchParams.set('learningContentId', learningContentId);
      }

      return apiClient.get<any[]>(`/quiz/history?${searchParams}`);
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    enabled: !!learningContentId,
  });
}

// Mutation hooks
export function useGenerateQuiz() {
  const queryClient = useQueryClient();

  return useMutation<QuizGenerationData, Error, QuizGenerationConfig>({
    mutationFn: async (config) => {
      return apiClient.post('/quiz/generate', config);
    },
    onSuccess: (response, variables) => {
      // Invalidate quiz queries for the learning content
      queryClient.invalidateQueries({
        queryKey: ['quiz'],
      });
      
      // If we have a specific learning content ID, invalidate that specific query
      if (variables.learningContentId) {
        queryClient.invalidateQueries({
          queryKey: createQueryKey('quiz.getAll', { learningContentId: variables.learningContentId }),
        });
      }
    },
  });
}

export function useCreateQuiz() {
  const queryClient = useQueryClient();

  return useMutation<Quiz, Error, CreateQuizParams>({
    mutationFn: async (params) => {
      return apiClient.post('/quiz', params);
    },
    onSuccess: () => {
      // Invalidate quiz queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['quiz'],
      });
    },
  });
}

export function useUpdateQuiz(id: string) {
  const queryClient = useQueryClient();

  return useMutation<Quiz, Error, Partial<CreateQuizParams>>({
    mutationFn: async (params) => {
      return apiClient.put(`/quiz/${id}`, params);
    },
    onSuccess: () => {
      // Invalidate quiz queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['quiz'],
      });
    },
  });
}

export function useDeleteQuiz() {
  const queryClient = useQueryClient();

  return useMutation<void, Error, string>({
    mutationFn: async (id) => {
      return apiClient.delete(`/quiz/${id}`);
    },
    onSuccess: () => {
      // Invalidate quiz queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ['quiz'],
      });
    },
  });
}

// New hooks for comprehensive quiz management
export function useGetQuizzes(filters: Partial<QuizzesFilters> = {}) {
  const {
    search,
    difficulty,
    completionStatus = "all",
    learningContentId,
    limit = 20,
    offset = 0,
    includePublic = true,
    grouped = false,
  } = filters;

  return useQuery<QuizzesResponse, Error>({
    queryKey: createQueryKey('quizzes.getAll', {
      search,
      difficulty,
      completionStatus,
      learningContentId,
      limit,
      offset,
      includePublic,
      grouped,
    }),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        includePublic: includePublic.toString(),
        grouped: grouped.toString(),
        completionStatus,
      });

      if (search) searchParams.set('search', search);
      if (difficulty) searchParams.set('difficulty', difficulty);
      if (learningContentId) searchParams.set('learningContentId', learningContentId);

      return apiClient.get<QuizzesResponse>(`/quizzes?${searchParams}`);
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Infinite scroll version for quizzes
export function useGetQuizzesInfinite(filters: Partial<Omit<QuizzesFilters, 'offset'>> = {}) {
  const {
    search,
    difficulty,
    completionStatus = "all",
    learningContentId,
    limit = 12,
    includePublic = true,
    grouped = false,
  } = filters;

  return useInfiniteQuery<QuizzesResponse, Error>({
    queryKey: createQueryKey('quizzes.getAllInfinite', {
      search,
      difficulty,
      completionStatus,
      learningContentId,
      limit,
      includePublic,
      grouped,
    }),
    queryFn: async ({ pageParam = 0 }) => {
      const searchParams = new URLSearchParams({
        limit: limit.toString(),
        offset: (pageParam as number).toString(),
        includePublic: includePublic.toString(),
        grouped: grouped.toString(),
        completionStatus,
      });

      if (search) searchParams.set('search', search);
      if (difficulty) searchParams.set('difficulty', difficulty);
      if (learningContentId) searchParams.set('learningContentId', learningContentId);

      return apiClient.get<QuizzesResponse>(`/quizzes?${searchParams}`);
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage.pagination.hasMore) return undefined;
      return lastPage.pagination.offset + lastPage.pagination.limit;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    initialPageParam: 0,
  });
}

export function useGetQuizAttempts(filters: Partial<QuizAttemptsFilters> = {}) {
  const {
    quizId,
    limit = 20,
    offset = 0,
  } = filters;

  return useQuery<QuizAttemptsResponse, Error>({
    queryKey: createQueryKey('quiz-attempts.getAll', {
      quizId,
      limit,
      offset,
    }),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
      });

      if (quizId) searchParams.set('quizId', quizId);

      return apiClient.get<QuizAttemptsResponse>(`/quiz-attempts?${searchParams}`);
    },
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

// Utility hook to get query client utils (similar to tRPC utils)
export function useQuizUtils() {
  const queryClient = useQueryClient();

  return {
    quiz: {
      getAll: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['quiz.getAll'],
          });
        },
        refetch: (params?: GetQuizzesParams) => {
          const queryKey = createQueryKey('quiz.getAll', params || {});
          return queryClient.refetchQueries({ queryKey });
        },
      },
      get: {
        invalidate: (id?: string) => {
          if (id) {
            queryClient.invalidateQueries({
              queryKey: createQueryKey('quiz.get', { id }),
            });
          } else {
            queryClient.invalidateQueries({
              queryKey: ['quiz.get'],
            });
          }
        },
      },
    },
    quizzes: {
      getAll: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['quizzes.getAll'],
          });
        },
        refetch: (filters?: Partial<QuizzesFilters>) => {
          const queryKey = createQueryKey('quizzes.getAll', filters || {});
          return queryClient.refetchQueries({ queryKey });
        },
      },
    },
    quizAttempts: {
      getAll: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['quiz-attempts.getAll'],
          });
        },
        refetch: (filters?: Partial<QuizAttemptsFilters>) => {
          const queryKey = createQueryKey('quiz-attempts.getAll', filters || {});
          return queryClient.refetchQueries({ queryKey });
        },
      },
    },
  };
}

// Quiz-taking hooks for dashboard quiz route
export function useGetQuizWithAttempts(quizId?: string) {
  return useQuery<{ quiz: any; attempts: any[] }, Error>({
    queryKey: createQueryKey('quiz.getWithAttempts', { quizId }),
    queryFn: async () => apiClient.get(`/quiz/${quizId}`),
    enabled: !!quizId,
    staleTime: 60 * 1000,
  });
}

export function useGetCurrentAttempt(quizId?: string) {
  return useQuery<{ attempt: any | null; progress: any | null }, Error>({
    queryKey: createQueryKey('quizAttempts.current', { quizId }),
    queryFn: async () => apiClient.get(`/quiz-attempts/current/${quizId}`),
    enabled: !!quizId,
    staleTime: 15 * 1000,
  });
}

export function useGetAttemptHistory(quizId?: string, params?: { limit?: number; offset?: number }) {
  const { limit = 10, offset = 0 } = params || {};
  return useQuery<{ attempts: any[]; pagination: any }, Error>({
    queryKey: createQueryKey('quizAttempts.history', { quizId, limit, offset }),
    queryFn: async () => apiClient.get(`/quiz-attempts/history/${quizId}`, { limit, offset }),
    enabled: !!quizId,
    staleTime: 60 * 1000,
  });
}

export function useStartAttempt() {
  const qc = useQueryClient();
  return useMutation<any, Error, { quizId: string }>({
    mutationFn: (vars) => apiClient.post('/quiz-attempts/start', vars),
    onSuccess: (_data, vars) => {
      qc.invalidateQueries({ queryKey: createQueryKey('quizAttempts.current', { quizId: vars.quizId }) });
      qc.invalidateQueries({ queryKey: createQueryKey('quiz.getWithAttempts', { quizId: vars.quizId }) });
      qc.invalidateQueries({ queryKey: ['quizzes.getAll'] });
      qc.invalidateQueries({ queryKey: ['quizzes.getAllInfinite'] });
    },
  });
}

export function useSubmitAnswer() {
  const qc = useQueryClient();
  return useMutation<any, Error, { attemptId: string; questionId: string; answer: any; timeSpent: number; isTemporary?: boolean; quizId: string }>({
    mutationFn: ({ quizId, ...payload }) => apiClient.post('/quiz-attempts/submit-answer', payload),
    onSuccess: (_data, vars) => {
      qc.invalidateQueries({ queryKey: createQueryKey('quizAttempts.current', { quizId: vars.quizId }) });
      qc.invalidateQueries({ queryKey: ['quizzes.getAll'] });
      qc.invalidateQueries({ queryKey: ['quizzes.getAllInfinite'] });
    },
  });
}

export function useSaveProgress() {
  const qc = useQueryClient();
  return useMutation<any, Error, { attemptId: string; currentQuestionIndex: number; timeSpentOnCurrentQuestion: number; totalSessionTime: number; quizId: string; bookmarkedQuestions?: string[] }>({
    mutationFn: ({ quizId, ...payload }) => apiClient.post('/quiz-attempts/save-progress', payload),
    onSuccess: (_data, vars) => {
      qc.invalidateQueries({ queryKey: createQueryKey('quizAttempts.current', { quizId: vars.quizId }) });
      // Invalidate quiz list cache to update progress status
      qc.invalidateQueries({ queryKey: ['quizzes.getAll'] });
      qc.invalidateQueries({ queryKey: ['quizzes.getAllInfinite'] });
    },
  });
}

export function useCompleteAttempt() {
  const qc = useQueryClient();
  return useMutation<any, Error, { attemptId: string; quizId: string }>({
    mutationFn: ({ quizId, attemptId }) => apiClient.post('/quiz-attempts/complete', { attemptId }),
    onSuccess: (_data, vars) => {
      qc.invalidateQueries({ queryKey: createQueryKey('quizAttempts.current', { quizId: vars.quizId }) });
      qc.invalidateQueries({ queryKey: createQueryKey('quizAttempts.history', { quizId: vars.quizId }) });
      qc.invalidateQueries({ queryKey: createQueryKey('quiz.getWithAttempts', { quizId: vars.quizId }) });
      // Invalidate quiz list cache to update completion status
      qc.invalidateQueries({ queryKey: ['quizzes.getAll'] });
      qc.invalidateQueries({ queryKey: ['quizzes.getAllInfinite'] });
    },
  });
}
