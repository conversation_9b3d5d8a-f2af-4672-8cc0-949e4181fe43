import { useInfiniteQuery } from '@tanstack/react-query';
import { useApiQuery } from './use-query';
import { apiClient } from '~/lib/data-fetching';
import { useAuth } from '~/lib/auth/auth-provider';

interface LeaderboardUser {
  userId: string;
  userName: string;
  userEmail: string;
  userAvatar?: string | null;
  totalPoints: number;
  quizPoints?: number;
  progressPoints?: number;
  analyticsPoints?: number;
  completedContent: number;
  completedQuizzes: number;
  totalSessions: number;
  rank: number;
  lastUpdated: string;
  isCurrentUser: boolean;
}

interface LeaderboardResponse {
  success: boolean;
  data: {
    leaderboard: LeaderboardUser[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      hasMore: boolean;
    };
    currentUser: {
      rank: number;
      id: string;
      data?: LeaderboardUser | null; // Full user data if not in top results
    };
  };
}

interface ContributionDay {
  date: string;
  sessionCount: number;
  totalEvents: number;
  level: number;
}

interface ContributionsResponse {
  success: boolean;
  data: {
    contributions: ContributionDay[];
    stats: {
      year: number;
      totalSessions: number;
      activeDays: number;
      longestStreak: number;
      currentStreak: number;
      maxSessions: number;
    };
  };
}

// Fetch leaderboard data with pagination
async function fetchLeaderboard(page: number = 1, limit: number = 10): Promise<LeaderboardResponse> {
  return apiClient.get<LeaderboardResponse>(`/leaderboard`, { page, limit });
}

// Fetch contributions data
async function fetchContributions(year?: number): Promise<ContributionsResponse> {
  const currentYear = year || new Date().getFullYear();
  const timezoneOffset = -new Date().getTimezoneOffset();
  console.log('[CONTRIBUTIONS API] Fetching contributions:', { year: currentYear, timezoneOffset });
  const result = await apiClient.get<ContributionsResponse>(`/contributions`, { year: currentYear, timezoneOffset });
  console.log('[CONTRIBUTIONS API] Response received:', result);
  return result;
}

// Hook for infinite scrolling leaderboard
export function useLeaderboardInfinite(limit: number = 10) {
  // Delay query until auth is ready to avoid early 401s before token is attached
  const { isAuthenticated, isLoading } = useAuth();
  return useInfiniteQuery({
    queryKey: ['leaderboard', limit],
    queryFn: ({ pageParam = 1 }) => fetchLeaderboard(pageParam, limit),
    getNextPageParam: (lastPage) => {
      if (lastPage.success && lastPage.data.pagination.hasMore) {
        return lastPage.data.pagination.page + 1;
      }
      return undefined;
    },
    initialPageParam: 1,
    staleTime: 2 * 60 * 1000,
    gcTime: 5 * 60 * 1000,
    enabled: isAuthenticated && !isLoading,
  });
}

// Hook for regular leaderboard query (if you need just first page)
export function useLeaderboard(page: number = 1, limit: number = 10) {
  return useApiQuery(
    ['leaderboard', page, limit],
    () => fetchLeaderboard(page, limit),
    {
      staleTime: 2 * 60 * 1000, // 2 minutes
      gcTime: 5 * 60 * 1000, // 5 minutes
    }
  );
}

// Hook for contributions (git-style heatmap data)
export function useContributions(year?: number) {
  const currentYear = year || new Date().getFullYear();
  // Delay query until auth is ready to avoid early 401s before token is attached
  const { isAuthenticated, isLoading } = useAuth();

  return useApiQuery(
    ['contributions', currentYear],
    () => fetchContributions(currentYear),
    {
      staleTime: 10 * 60 * 1000, // 10 minutes - contributions don't change as frequently
      gcTime: 30 * 60 * 1000, // 30 minutes
      enabled: isAuthenticated && !isLoading,
    }
  );
}

// Export types for use in components
export type { LeaderboardUser, LeaderboardResponse, ContributionDay, ContributionsResponse };