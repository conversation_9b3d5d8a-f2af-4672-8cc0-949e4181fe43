import { useQuery } from '@tanstack/react-query';

export interface QuizScoreItem {
  title: string;
  score: number;
  points: number;
}

export interface QuizScoreRange {
  count: number;
  points: number;
  items: QuizScoreItem[];
}

export interface QuizBreakdown {
  scoreRanges: {
    '90+': QuizScoreRange;
    '60-69': QuizScoreRange;
    '<60': QuizScoreRange;
  };
  total: {
    count: number;
    points: number;
    average: number;
  };
}

export interface LearningContentItem {
  title: string;
  duration: number;
  level: string;
  basePoints: number;
  bonusPoints: number;
  totalPoints: number;
}

export interface DurationBonusRange {
  count: number;
  points: number;
  items: LearningContentItem[];
}

export interface LevelBreakdownData {
  count: number;
  points: number;
  average: number;
  basePoints: number;
  bonusPoints: number;
}

export interface LearningBreakdown {
  durationBonus: {
    '15+': DurationBonusRange;
    '10-14': DurationBonusRange;
    '5-9': DurationBonusRange;
    '<5': DurationBonusRange;
  };
  levelBreakdown: {
    [level: string]: LevelBreakdownData;
  };
}

export interface PointsBreakdownData {
  quiz: QuizBreakdown;
  learning: LearningBreakdown;
}

export interface PointsBreakdownResponse {
  success: boolean;
  data?: PointsBreakdownData;
  error?: string;
}

export function usePointsBreakdown() {
  return useQuery({
    queryKey: ['points-breakdown'],
    queryFn: async (): Promise<PointsBreakdownResponse> => {
      const response = await fetch('/api/points-breakdown');
      if (!response.ok) {
        throw new Error('Failed to fetch points breakdown');
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime)
  });
}