import { useMutation } from '@tanstack/react-query';
import { apiClient } from '../data-fetching';
import { useApiUtils } from './use-api-utils';
import { log } from '../logger';

export interface TopicActionParams {
  conversationId: string;
  topic: string;
}

export interface TopicActionResponse {
  success: boolean;
  message?: string;
  error?: string;
}

/**
 * Hook for handling topic acceptance and rejection
 */
export function useTopicActions() {
  const utils = useApiUtils();

  const acceptTopicMutation = useMutation({
    mutationFn: async ({ conversationId, topic }: TopicActionParams): Promise<TopicActionResponse> => {
      return apiClient.post<TopicActionResponse>('/topics', {
        action: 'accept',
        conversationId,
        topic,
      });
    },
    onSuccess: (data, variables) => {
      log.info('Topic accepted successfully', {
        conversationId: variables.conversationId,
        topic: variables.topic,
      });
      
      // Invalidate conversation memory to refresh proposed topics
      utils.conversations.getMemory.invalidate({ conversationId: variables.conversationId });
      // Also invalidate conversation history to refresh memory context
      utils.topics.acceptTopic.invalidate();
    },
    onError: (error, variables) => {
      log.error('Failed to accept topic', {
        conversationId: variables.conversationId,
        topic: variables.topic,
        error: error instanceof Error ? error.message : String(error),
      });
    },
  });

  const rejectTopicMutation = useMutation({
    mutationFn: async ({ conversationId, topic }: TopicActionParams): Promise<TopicActionResponse> => {
      return apiClient.post<TopicActionResponse>('/topics', {
        action: 'reject',
        conversationId,
        topic,
      });
    },
    onSuccess: (data, variables) => {
      log.info('Topic rejected successfully', {
        conversationId: variables.conversationId,
        topic: variables.topic,
      });
      
      // Invalidate conversation memory to refresh proposed topics
      utils.conversations.getMemory.invalidate({ conversationId: variables.conversationId });
      // Also invalidate conversation history to refresh memory context
      utils.topics.rejectTopic.invalidate();
    },
    onError: (error, variables) => {
      log.error('Failed to reject topic', {
        conversationId: variables.conversationId,
        topic: variables.topic,
        error: error instanceof Error ? error.message : String(error),
      });
    },
  });

  return {
    acceptTopic: acceptTopicMutation.mutate,
    rejectTopic: rejectTopicMutation.mutate,
    isAccepting: acceptTopicMutation.isPending,
    isRejecting: rejectTopicMutation.isPending,
    acceptError: acceptTopicMutation.error,
    rejectError: rejectTopicMutation.error,
  };
}