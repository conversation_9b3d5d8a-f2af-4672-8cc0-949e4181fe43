import { useQuery } from '@tanstack/react-query';
import { createQuery<PERSON><PERSON> } from '../data-fetching';
import { log } from '../logger';

export interface ConversationMemoryData {
  proposedTopics?: Array<{
    topic: string;
    status: 'proposed' | 'accepted' | 'rejected';
  }>;
}

export interface ConversationMemoryResponse {
  success: boolean;
  data?: ConversationMemoryData;
  error?: string;
}

export interface UseConversationMemoryParams {
  conversationId: string;
  enabled?: boolean;
}

/**
 * Hook for fetching conversation memory using React Query
 */
export function useConversationMemory({ conversationId, enabled = true }: UseConversationMemoryParams) {
  return useQuery({
    queryKey: createQueryKey('conversations.getMemory', { conversationId }),
    queryFn: async (): Promise<ConversationMemoryResponse> => {
      try {
        const response = await fetch('/api/conversations', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'get_memory',
            conversationId
          })
        });
        
        const data = await response.json();
        return data;
      } catch (error) {
        log.error('Failed to fetch conversation memory', { conversationId, error });
        throw error;
      }
    },
    enabled: enabled && !!conversationId,
    staleTime: 1000 * 60 * 5, // 5 minutes
    gcTime: 1000 * 60 * 10, // 10 minutes
  });
}