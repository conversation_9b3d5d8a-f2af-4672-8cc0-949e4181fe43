import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiClient, createQuery<PERSON>ey } from '../data-fetching';

// Types for progress API
export interface ProgressStats {
  totalTimeSpent: number;
  contentCompleted: number;
  contentInProgress: number;
  averageProgress: number;
  recentActivity: any[];
}

export interface ProgressData {
  progress: any[];
  analytics: ProgressStats | null;
}

export interface GetProgressParams {
  contentId?: string;
  timeframe?: 'week' | 'month' | 'all';
  includeAnalytics?: boolean;
}

export interface TrackEventParams {
  action: 'track_event';
  eventType: 'content_viewed' | 'content_completed' | 'quiz_started' | 'quiz_completed' | 'step_completed' | 'time_spent' | 'interaction';
  contentId?: string;
  quizId?: string;
  stepId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
  duration?: number;
}

export interface UpdateProgressParams {
  action: 'update_progress';
  contentId: string;
  stepId?: string;
  progress: number;
  timeSpent: number;
  completed?: boolean;
  completedSteps?: number[];
  currentStep?: number;
  sessionId?: string;
}

export interface AddBookmarkParams {
  action: 'add_bookmark';
  contentId: string;
  stepId?: string;
  note?: string;
}

export interface RemoveBookmarkParams {
  action: 'remove_bookmark';
  contentId: string;
  stepId?: string;
}

export interface AddNoteParams {
  action: 'add_note';
  contentId: string;
  stepId?: string;
  content: string;
  noteType?: 'general' | 'step_specific';
}

export interface UpdateNoteParams {
  action: 'update_note';
  noteId: string;
  content: string;
}

export interface DeleteNoteParams {
  action: 'delete_note';
  noteId: string;
}

export interface SubmitFeedbackParams {
  action: 'submit_feedback';
  contentId: string;
  rating: number;
  difficulty: 'too_easy' | 'just_right' | 'too_hard';
  feedback?: string;
  improvements?: string;
}

// Query hooks
export function useGetStats(params: GetProgressParams = {}) {
  const {
    contentId,
    timeframe = 'month',
    includeAnalytics = true,
  } = params;

  return useQuery<ProgressData, Error>({
    queryKey: createQueryKey('learningProgress.stats', {
      contentId,
      timeframe,
      includeAnalytics,
    }),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        timeframe,
        includeAnalytics: includeAnalytics.toString(),
      });

      if (contentId) {
        searchParams.set('contentId', contentId);
      }

      return apiClient.get<ProgressData>(`/progress?${searchParams}`);
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useGetProgress(contentId: string) {
  return useQuery<any, Error>({
    queryKey: createQueryKey('learningProgress.getProgress', { contentId }),
    queryFn: async () => {
      const searchParams = new URLSearchParams({
        contentId,
        includeAnalytics: 'false',
      });

      return apiClient.get<any>(`/progress?${searchParams}`);
    },
    staleTime: 0, // Always refetch when invalidated
    refetchOnWindowFocus: false,
  });
}

// Mutation hooks
export function useTrackEvent() {
  const queryClient = useQueryClient();

  return useMutation<any, Error, TrackEventParams>({
    mutationFn: async (params) => {
      return apiClient.post('/progress', params);
    },
    onSuccess: () => {
      // Invalidate progress stats to refresh data
      queryClient.invalidateQueries({
        queryKey: ['learningProgress.stats'],
      });
    },
  });
}

export function useUpdateProgress() {
  const queryClient = useQueryClient();

  return useMutation<any, Error, UpdateProgressParams>({
    mutationFn: async (params) => {
      return apiClient.post('/progress', params);
    },
    onSuccess: (data, variables) => {
      // Invalidate progress stats to refresh data
      queryClient.invalidateQueries({
        queryKey: ['learningProgress.stats'],
      });
      
      // Invalidate the specific progress query for this content
      queryClient.invalidateQueries({
        queryKey: createQueryKey('learningProgress.getProgress', { contentId: variables.contentId }),
      });
      
      // Also invalidate all progress queries to ensure consistency
      queryClient.invalidateQueries({
        queryKey: ['learningProgress'],
      });
    },
  });
}

export function useSubmitFeedback() {
  const queryClient = useQueryClient();

  return useMutation<any, Error, SubmitFeedbackParams>({
    mutationFn: async (params) => {
      return apiClient.post('/progress', params);
    },
    onSuccess: () => {
      // Invalidate progress stats to refresh data
      queryClient.invalidateQueries({
        queryKey: ['learningProgress.stats'],
      });
    },
  });
}

export function useAddBookmark() {
  const queryClient = useQueryClient();

  return useMutation<any, Error, AddBookmarkParams>({
    mutationFn: async (params) => {
      return apiClient.post('/progress', params);
    },
    onSuccess: (data, variables) => {
      // Invalidate the specific progress query for this content
      queryClient.invalidateQueries({
        queryKey: createQueryKey('learningProgress.getProgress', { contentId: variables.contentId }),
      });
      // Invalidate progress data to refresh bookmarks
      queryClient.invalidateQueries({
        queryKey: ['learningProgress'],
      });
    },
  });
}

export function useRemoveBookmark() {
  const queryClient = useQueryClient();

  return useMutation<any, Error, RemoveBookmarkParams>({
    mutationFn: async (params) => {
      return apiClient.post('/progress', params);
    },
    onSuccess: (data, variables) => {
      // Invalidate the specific progress query for this content
      queryClient.invalidateQueries({
        queryKey: createQueryKey('learningProgress.getProgress', { contentId: variables.contentId }),
      });
      // Invalidate progress data to refresh bookmarks
      queryClient.invalidateQueries({
        queryKey: ['learningProgress'],
      });
    },
  });
}

export function useAddNote() {
  const queryClient = useQueryClient();

  return useMutation<any, Error, AddNoteParams>({
    mutationFn: async (params) => {
      return apiClient.post('/progress', params);
    },
    onSuccess: (data, variables) => {
      // Invalidate the specific progress query for this content
      queryClient.invalidateQueries({
        queryKey: createQueryKey('learningProgress.getProgress', { contentId: variables.contentId }),
      });
      // Invalidate progress data to refresh notes
      queryClient.invalidateQueries({
        queryKey: ['learningProgress'],
      });
    },
  });
}

export function useUpdateNote() {
  const queryClient = useQueryClient();

  return useMutation<any, Error, UpdateNoteParams>({
    mutationFn: async (params) => {
      return apiClient.post('/progress', params);
    },
    onSuccess: (data, variables) => {
      // Invalidate the specific progress query for this content based on noteId format `${contentId}:${index}`
      const contentId = variables.noteId?.split(':')[0];
      if (contentId) {
        queryClient.invalidateQueries({
          queryKey: createQueryKey('learningProgress.getProgress', { contentId }),
        });
      }
      // Invalidate progress data to refresh notes
      queryClient.invalidateQueries({
        queryKey: ['learningProgress'],
      });
    },
  });
}

export function useDeleteNote() {
  const queryClient = useQueryClient();

  return useMutation<any, Error, DeleteNoteParams>({
    mutationFn: async (params) => {
      return apiClient.post('/progress', params);
    },
    onSuccess: (data, variables) => {
      // Invalidate the specific progress query for this content based on noteId format `${contentId}:${index}`
      const contentId = variables.noteId?.split(':')[0];
      if (contentId) {
        queryClient.invalidateQueries({
          queryKey: createQueryKey('learningProgress.getProgress', { contentId }),
        });
      }
      // Invalidate progress data to refresh notes
      queryClient.invalidateQueries({
        queryKey: ['learningProgress'],
      });
    },
  });
}

// Utility hook to get query client utils (similar to tRPC utils)
export function useProgressUtils() {
  const queryClient = useQueryClient();

  return {
    learningProgress: {
      getStats: {
        invalidate: () => {
          queryClient.invalidateQueries({
            queryKey: ['learningProgress.stats'],
          });
        },
        refetch: (params?: GetProgressParams) => {
          const queryKey = createQueryKey('learningProgress.stats', params || {});
          return queryClient.refetchQueries({ queryKey });
        },
      },
    },
  };
}
