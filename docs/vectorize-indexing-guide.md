# Vectorize Indexing Guide

This guide explains how to index your learning materials into Cloudflare Vectorize for RAG-powered chat functionality.

## Quick Start

### 1. Start the Batch Index Worker
```bash
bun run batch-index-worker # deprecated
bun run test-rag-worker
```

### 2. Run the Indexing Script
```bash
bun run call-batch-worker # deprecated
bun run scripts/reindex-content.ts
```

That's it! Your learning materials are now indexed in Vectorize.

## What This Does

The indexing process:
1. **Reads learning content** from your PostgreSQL database
2. **Chunks the content** into searchable segments 
3. **Generates embeddings** using Voyage AI
4. **Stores vectors** in Cloudflare Vectorize index
5. **Enables RAG chat** with contextual responses

## Commands Reference

### Start Worker
```bash
# Start the batch indexing worker (runs on port 8787)
bun run batch-index-worker
```

### Index All Content
```bash
# Index all learning materials (dry run first)
bun run call-batch-worker --dry-run --limit 10
bun run call-batch-worker --limit 50

# Index everything
bun run call-batch-worker --limit 1000
```

### Index Specific User's Content
```bash
# Index content from specific user
bun run call-batch-worker --user-id abc123 --limit 20
```

### Health Check
```bash
# Check if worker is running and configured properly
curl http://localhost:8787/health
```

## Configuration

### Environment Variables
```bash
VOYAGE_API_KEY=your_voyage_api_key_here
```

### Vectorize Indexes
- **Development**: `learning-content-embeddings-dev`
- **Production**: `learning-content-embeddings-prod`

Configured in `wrangler.json`:
```json
{
  "env": {
    "develop": {
      "vectorize": [{
        "binding": "VECTORIZE_INDEX", 
        "index_name": "learning-content-embeddings-dev"
      }]
    }
  }
}
```

## What Gets Indexed

### Content Structure
Each learning material is broken down into:
- **Steps**: Main sections of the content
- **Blocks**: Individual content elements (text, lists, etc.)
- **Chunks**: Optimally sized segments for vector search

### Metadata Stored
- Content ID and step information
- Chunk text and position
- Embedding model details (Voyage AI)
- Processing metadata (chunking strategy, token counts)

### Example
```
Learning Material: "Understanding Dark Matter"
├── Step 1: "Introduction" → 3 chunks
├── Step 2: "Physics Concepts" → 5 chunks  
└── Step 3: "Current Research" → 4 chunks
Total: 12 chunks indexed in Vectorize
```

## Verification

### Check Indexing Status
```bash
# Dry run to see what would be indexed
bun run call-batch-worker --dry-run --limit 5

# Check worker health
curl http://localhost:8787/health
```

### Expected Output
```json
{
  "success": true,
  "vectorizeIndex": "learning-content-embeddings-dev",
  "stats": {
    "totalProcessed": 1,
    "successful": 1, 
    "failed": 0,
    "processingTimeMs": 2300
  }
}
```

## Troubleshooting

### Worker Won't Start
- Ensure you have `VOYAGE_API_KEY` in your environment
- Check wrangler configuration in `wrangler.json`
- Verify Vectorize index exists: `wrangler vectorize list`

### Connection Errors
```bash
# If you get connection errors, the worker might not be running
❌ Health check failed: Connection refused

# Solution: Start the worker first
bun run batch-index-worker
```

### No Content Found
```bash
# Check if you have learning content in database
bun run call-batch-worker --dry-run --limit 1
```

## Production Deployment

### For Production Indexing
```bash
# Deploy worker temporarily for batch indexing
wrangler deploy scripts/batch-index-worker.ts --name batch-indexer

# Call deployed worker
bun run call-batch-worker --url https://batch-indexer.your-subdomain.workers.dev
```

### Automatic Indexing
Once set up, new learning content is automatically indexed when created through the web application.

## RAG Chat Integration

After indexing, your learning materials are available for:
- **Contextual chat** via `/api/chat` endpoint
- **Content-specific conversations** scoped to learning materials
- **Source attribution** showing which content chunks informed responses

Test the chat functionality:
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "message": "What are the main concepts?",
    "learningContentId": "your-content-id"
  }'
```

## Summary

1. **Start worker**: `bun run batch-index-worker` 
2. **Index content**: `bun run call-batch-worker`
3. **Verify**: Check output for successful indexing
4. **Use RAG chat**: Content is now searchable and available for AI conversations

Your learning materials are now powered by RAG for intelligent, contextual conversations! 🚀