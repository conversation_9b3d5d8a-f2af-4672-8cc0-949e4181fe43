import { generateQuizFromContent } from './app/lib/ai/services/quiz-generator.ts';
import { getLearningContentById } from './app/db/services/learning-content.ts';
import { db } from './app/db/index.ts';

const contentId = '84696698-8274-4745-83f6-7c261e050a06';
const userId = 'user_2pqKhMZLgjNpzBrWKKvOWKvOWKv'; // Existing user

async function testAllQuestionTypes() {
  try {
    console.log('Fetching learning content...');
    const learningContent = await getLearningContentById(db, contentId, userId);
    
    if (!learningContent) {
      console.error('Learning content not found');
      return;
    }
    
    console.log('Learning content found:', {
      title: learningContent.title,
      stepsCount: learningContent.steps?.length || 0,
    });
    
    console.log('\nGenerating quiz with all question types and hints...');
    
    const config = {
      quizTypes: ['multipleChoice', 'trueFalse', 'fillInBlank', 'flashcard', 'matching', 'freeText', 'ordering'],
      difficulty: 'medium',
      questionsPerType: 1,
      includeHints: true,
      includeExplanations: true,
      shuffleQuestions: true,
      timeLimit: 32
    };
    
    const quiz = await generateQuizFromContent(learningContent, config, { userId });
    
    console.log('\nQuiz generation successful!');
    console.log('Generated quiz details:', {
      title: quiz.title,
      questionsCount: quiz.questions.length,
      difficulty: quiz.difficulty,
    });
    
    console.log('\nDetailed hint analysis:');
    let questionsWithHints = 0;
    
    quiz.questions.forEach((question, index) => {
      const hasHint = question.hint && question.hint.trim() !== '';
      if (hasHint) questionsWithHints++;
      
      console.log(`Question ${index + 1} (${question.type}): {`);
      console.log(`  hasHint: ${hasHint},`);
      console.log(`  hint: "${hasHint ? question.hint : 'No hint'}",`);
      console.log(`  hasCorrectFeedback: ${!!question.correctFeedback},`);
      console.log(`  hasIncorrectFeedback: ${!!question.incorrectFeedback},`);
      
      // Show question-specific fields for verification
      if (question.type === 'multipleChoice') {
        console.log(`  optionsCount: ${question.options?.length || 0},`);
      } else if (question.type === 'fillInBlank') {
        console.log(`  blanksCount: ${question.blanks?.length || 0},`);
      } else if (question.type === 'matching') {
        console.log(`  pairsCount: ${question.pairs?.length || 0},`);
      } else if (question.type === 'flashcard') {
        console.log(`  hasFront: ${!!question.front},`);
        console.log(`  hasBack: ${!!question.back},`);
      } else if (question.type === 'freeText') {
        console.log(`  answerType: "${question.answerType || 'N/A'}",`);
        console.log(`  maxLength: ${question.maxLength || 'N/A'},`);
      } else if (question.type === 'ordering') {
        console.log(`  itemsCount: ${question.items?.length || 0},`);
      }
      
      console.log('},');
    });
    
    console.log(`\nSummary: ${questionsWithHints}/${quiz.questions.length} questions have hints`);
    
    // Type distribution analysis
    const typeDistribution = {};
    quiz.questions.forEach(q => {
      typeDistribution[q.type] = (typeDistribution[q.type] || 0) + 1;
    });
    
    console.log('\nType distribution:', typeDistribution);
    
    // Hint distribution by type
    const hintsByType = {};
    quiz.questions.forEach(q => {
      const hasHint = q.hint && q.hint.trim() !== '';
      if (!hintsByType[q.type]) {
        hintsByType[q.type] = { total: 0, withHints: 0 };
      }
      hintsByType[q.type].total++;
      if (hasHint) hintsByType[q.type].withHints++;
    });
    
    console.log('\nHints by question type:');
    Object.entries(hintsByType).forEach(([type, stats]) => {
      console.log(`  ${type}: ${stats.withHints}/${stats.total} questions have hints`);
    });
    
  } catch (error) {
    console.error('Quiz generation failed:', error.message);
    console.error('Full error:', error);
  }
}

testAllQuestionTypes();