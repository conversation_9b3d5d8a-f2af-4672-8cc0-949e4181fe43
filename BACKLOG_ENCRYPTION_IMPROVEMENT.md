### Next steps and small follow-ups
Optional: Persist a short-lived “key session token” per user+quiz to rate-limit key requests; currently keys are cached client-side with a 5-minute default TTL returned by server.
Optional: Add replay protection using nonces or bind requests to current session ID.
Optional: Rotate server master key by versioning the derivation salt/label and handling dual-key decryption for a period.
Optional: Consider WebCrypto subtle.wrapKey instead of encrypt for structured wrapping, but current AES-GCM wrapping is adequate.
Optional: Reduce client logs in production build and ensure no sensitive debugging reaches console.

Switch to subtle.unwrapKey to avoid transient raw key bytes in client.
Add replay protection (nonce) or per-request key tokens.
Rotate/Version per-quiz key derivation if you ever need to rotate QUIZ_ENCRYPTION_KEY without downtime.