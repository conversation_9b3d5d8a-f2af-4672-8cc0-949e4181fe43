/**
 * Simple test script to validate streaming chat functionality
 * This tests the complete RAG workflow with streaming responses
 */

const BASE_URL = 'http://localhost:5174';

// Test data
const testData = {
  learningContentId: 'test-content-123',
  conversationId: 'test-conversation-456',
  message: 'What is quantum physics and how does it relate to classical physics?'
};

async function testStreamingChat() {
  console.log('🧪 Testing Streaming Chat Functionality');
  console.log('=====================================\n');
  
  try {
    console.log('📡 Sending streaming chat request...');
    console.log(`Message: "${testData.message}"`);
    console.log(`Learning Content ID: ${testData.learningContentId}`);
    console.log(`Conversation ID: ${testData.conversationId}\n`);
    
    const response = await fetch(`${BASE_URL}/api/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        message: testData.message,
        conversationId: testData.conversationId,
        learningContentId: testData.learningContentId,
      }),
    });
    
    console.log(`📊 Response Status: ${response.status} ${response.statusText}`);
    console.log(`📋 Response Headers:`);
    for (const [key, value] of response.headers.entries()) {
      console.log(`   ${key}: ${value}`);
    }
    console.log();
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Request failed:', errorText);
      return;
    }
    
    if (!response.body) {
      console.error('❌ No response body received');
      return;
    }
    
    console.log('📥 Streaming response:');
    console.log('----------------------');
    
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let fullContent = '';
    let chunkCount = 0;
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) {
          console.log('\n✅ Stream completed');
          break;
        }
        
        const chunk = decoder.decode(value, { stream: true });
        chunkCount++;
        
        // Parse SSE data
        const lines = chunk.split('\n');
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              if (data.content) {
                process.stdout.write(data.content);
                fullContent += data.content;
              }
              if (data.metadata) {
                console.log(`\n\n📊 Metadata received:`, JSON.stringify(data.metadata, null, 2));
              }
            } catch (e) {
              // Ignore parsing errors for non-JSON lines
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
    
    console.log('\n\n📈 Test Results:');
    console.log('================');
    console.log(`✅ Chunks received: ${chunkCount}`);
    console.log(`✅ Total content length: ${fullContent.length} characters`);
    console.log(`✅ Content preview: "${fullContent.substring(0, 100)}${fullContent.length > 100 ? '...' : ''}"`);    
    console.log('\n🎉 Streaming chat test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
if (typeof window === 'undefined') {
  // Node.js environment
  testStreamingChat();
} else {
  // Browser environment
  console.log('Run this script in Node.js to test the streaming API');
}