-- Fix for materialized view concurrent refresh issue
-- The error occurs because REFRESH MATERIALIZED VIEW CONCURRENTLY requires a unique index
-- but our materialized view doesn't have one.

-- Solution 1: Add a unique index to enable concurrent refreshes
-- This is the preferred solution as it allows non-blocking updates

-- First, refresh the view normally to ensure it has data
REFRESH MATERIALIZED VIEW user_points_leaderboard_mv;

-- Create a unique index on user_id (which should be unique in the leaderboard)
CREATE UNIQUE INDEX IF NOT EXISTS idx_user_points_leaderboard_mv_user_id_unique 
ON user_points_leaderboard_mv (user_id);

-- Now the materialized view can be refreshed concurrently
-- Test the concurrent refresh
REFRESH MATERIALIZED VIEW CONCURRENTLY user_points_leaderboard_mv;

-- Alternative Solution 2: Modify the trigger to use non-concurrent refresh
-- (Only use this if the unique index approach doesn't work)
/*
CREATE OR REPLACE FUNCTION refresh_leaderboard_mv_trigger()
R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Use regular REFRESH MATERIALIZED VIEW (blocking but reliable)
  REFRESH MATERIALIZED VIEW user_points_leaderboard_mv;
  
  -- Return the appropriate record based on trigger type
  IF TG_OP = 'DELETE' THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql;
*/