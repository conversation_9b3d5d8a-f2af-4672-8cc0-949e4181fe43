# Supabase Auth Migration Guide

## Overview

This guide documents the migration from Better Auth + Supabase PostgreSQL to Supabase Auth entirely in our React Router 7 web application.

## Migration Summary

### What Changed

**Before (Better Auth)**:
- Custom authentication server with Better Auth
- Drizzle ORM with custom auth tables
- Server-side session management
- Complex auth configuration

**After (Supabase Auth)**:
- Native Supabase Auth integration
- Built-in user management
- Client-side session management
- Simplified configuration

## Architecture Changes

### Authentication Flow

**Before**:
```
User → React Router 7 App → Better Auth API → Supabase DB
```

**After**:
```
User → React Router 7 App → Supabase Auth → Supabase DB
```

### Key Benefits

✅ **Simplified Architecture**: Direct integration with Supabase Auth  
✅ **Built-in Features**: Email verification, password reset, social auth  
✅ **Better Security**: Official Supabase security practices  
✅ **Reduced Maintenance**: No custom auth server to maintain  
✅ **Same User Experience**: Identical login/register flows  

## Files Modified

### Core Authentication Files

1. **`app/lib/auth-client.ts`** - Replaced Better Auth client with Supabase client
2. **`app/lib/auth/auth-provider.tsx`** - Updated to use Supabase Auth context with session monitoring
3. **`app/lib/auth/session-monitor.tsx`** - Updated session monitoring for Supabase Auth compatibility
4. **`app/lib/auth/user-sync.ts`** - New user synchronization utilities for hybrid approach
5. **`app/lib/auth.types.ts`** - Updated TypeScript types for Supabase Auth
6. **`app/routes/login.tsx`** - Migrated to client-side Supabase Auth
7. **`app/routes/register.tsx`** - Migrated to client-side Supabase Auth
8. **`app/db/schema/auth.ts`** - Updated with documentation for hybrid approach

### Environment Configuration

9. **`.env.example`** - Updated environment variables

## Environment Variables

### New Required Variables

```bash
# Supabase Configuration
SUPABASE_URL="https://[project-ref].supabase.co"
SUPABASE_ANON_KEY="your-supabase-anon-key"

# Vite Environment Variables (for client-side access)
VITE_SUPABASE_URL="https://[project-ref].supabase.co"
VITE_SUPABASE_ANON_KEY="your-supabase-anon-key"
```

### Removed Variables

```bash
# These are no longer needed
BETTER_AUTH_SECRET
BETTER_AUTH_URL
BETTER_AUTH_TRUSTED_ORIGINS
```

## Setup Instructions

### 1. Configure Supabase Project

1. **Enable Email/Password Authentication**:
   - Go to Supabase Dashboard → Authentication → Settings
   - Enable "Email" provider
   - Configure email templates if needed

2. **Set Site URL**:
   - Set site URL to your application URL
   - Add redirect URLs for development and production

3. **Configure RLS (if needed)**:
   - Set up Row Level Security policies for your tables
   - Ensure proper access controls

### 2. Update Environment Variables

1. Copy `.env.example` to `.env`
2. Fill in your Supabase project details:
   ```bash
   SUPABASE_URL="https://your-project-ref.supabase.co"
   SUPABASE_ANON_KEY="your-anon-key"
   VITE_SUPABASE_URL="https://your-project-ref.supabase.co"
   VITE_SUPABASE_ANON_KEY="your-anon-key"
   ```

### 3. Test Authentication

1. Start the development server:
   ```bash
   bun run dev
   ```

2. Test the authentication flows:
   - Register a new account at `/register`
   - Login with existing credentials at `/login`
   - Verify logout functionality
   - Test protected routes

## Breaking Changes

### User Data Migration

**Hybrid Approach Implemented**: We maintain both Supabase Auth and application user tables for data consistency.

**How It Works**:
1. **Supabase Auth**: Handles authentication (login, registration, sessions)
2. **Application User Table**: Maintains user profiles and data relationships
3. **Automatic Sync**: Users are automatically synced between systems when they sign in

**Migration Strategy**:
- **Better Auth Tables**: Kept for application data relationships (learning progress, analytics, etc.)
- **Session/Account/Verification Tables**: Deprecated (Supabase Auth handles these)
- **User Table**: Repurposed as application user profiles, synced with Supabase Auth
- **Existing Data**: Preserved - no data loss for existing users

### Session Management

- Sessions are now managed client-side by Supabase
- No server-side session checking in loaders
- Authentication state is handled by React context
- **Automatic session monitoring preserved**: Session expiration detection, 401 response handling, and automatic logout functionality migrated to work with Supabase Auth

### API Changes

- Removed Better Auth API routes (`/api/auth/*`)
- Authentication is now handled directly by Supabase client

## Testing Checklist

- [ ] User registration works
- [ ] User login works
- [ ] User logout works
- [ ] Protected routes redirect to login when not authenticated
- [ ] Authenticated users are redirected from login/register pages
- [ ] Session persists across browser refreshes
- [ ] Error handling works correctly

## Troubleshooting

### Common Issues

1. **"Missing Supabase configuration" Error**:
   - Ensure `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY` are set
   - Check that environment variables are properly loaded

2. **Authentication not working**:
   - Verify Supabase project configuration
   - Check that email provider is enabled
   - Ensure site URL is configured correctly

3. **Redirect issues**:
   - Check that redirect URLs are configured in Supabase dashboard
   - Verify that authentication state is properly managed

### Debug Mode

Enable debug logging by checking browser console for Supabase Auth events.

## Next Steps

### Recommended Enhancements

1. **Email Verification**: Enable email verification in Supabase settings
2. **Password Reset**: Implement password reset functionality
3. **Social Authentication**: Add OAuth providers (Google, GitHub, etc.)
4. **Profile Management**: Add user profile editing capabilities
5. **Multi-Factor Authentication**: Enable 2FA for enhanced security

### Monitoring

Consider adding monitoring for:
- Authentication success/failure rates
- User registration patterns
- Session duration analytics

---

**Migration Completed**: 2025-01-16  
**Architecture Version**: Supabase Auth v2.56.0  
**Maintained By**: Development Team
