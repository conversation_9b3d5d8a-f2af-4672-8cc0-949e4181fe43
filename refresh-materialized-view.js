import { db } from './app/db/index.js';
import { sql } from 'drizzle-orm';

async function refreshMaterializedView() {
  try {
    console.log('🔄 Refreshing materialized view...');
    
    // First, drop and recreate the materialized view with the new dynamic calculation
    await db.execute(sql`DROP MATERIALIZED VIEW IF EXISTS user_points_leaderboard_mv`);
    
    console.log('✅ Dropped existing materialized view');
    
    // Recreate with dynamic point calculation
    await db.execute(sql`
      CREATE MATERIALIZED VIEW user_points_leaderboard_mv AS
      WITH user_quiz_points AS (
        SELECT 
          qa.user_id,
          SUM(
            CASE 
              WHEN qa.score->>'percentage' IS NULL THEN 0
              WHEN (qa.score->>'percentage')::numeric >= 90 THEN 100
              WHEN (qa.score->>'percentage')::numeric >= 80 THEN 80
              WHEN (qa.score->>'percentage')::numeric >= 70 THEN 60
              WHEN (qa.score->>'percentage')::numeric >= 60 THEN 40
              ELSE 20
            END
          ) as quiz_points
        FROM quiz_attempt qa
        WHERE qa.is_completed = true
        GROUP BY qa.user_id
      ),
      user_progress_points AS (
        SELECT 
          lp.user_id,
          SUM(
            CASE 
              WHEN lc.learning_level = 'beginner' THEN 30
              WHEN lc.learning_level = 'intermediate' THEN 50
              WHEN lc.learning_level = 'advanced' THEN 70
              ELSE 50
            END +
            CASE 
              WHEN lc.estimated_reading_time >= 60 THEN 30
              WHEN lc.estimated_reading_time >= 30 THEN 20
              WHEN lc.estimated_reading_time >= 15 THEN 10
              WHEN lc.estimated_reading_time >= 5 THEN 5
              ELSE 0
            END
          ) as progress_points
        FROM learning_progress lp
        INNER JOIN learning_content lc ON lp.content_id = lc.id
        WHERE lp.is_completed = true
        GROUP BY lp.user_id
      ),
      user_analytics_points AS (
        SELECT 
          lca.user_id,
          COUNT(DISTINCT lca.session_id) * 10 as analytics_points
        FROM learning_content_analytics lca
        WHERE lca.session_id IS NOT NULL
        GROUP BY lca.user_id
      ),
      user_total_points AS (
        SELECT 
          u.id as user_id,
          u.name as user_name,
          u.email as user_email,
          COALESCE(uqp.quiz_points, 0) + 
          COALESCE(upp.progress_points, 0) + 
          COALESCE(uap.analytics_points, 0) as total_points,
          COALESCE(uqp.quiz_points, 0) as quiz_points,
          COALESCE(upp.progress_points, 0) as progress_points,
          COALESCE(uap.analytics_points, 0) as analytics_points
        FROM "user" u
        LEFT JOIN user_quiz_points uqp ON u.id = uqp.user_id
        LEFT JOIN user_progress_points upp ON u.id = upp.user_id
        LEFT JOIN user_analytics_points uap ON u.id = uap.user_id
      )
      SELECT 
        user_id,
        user_name,
        user_email,
        total_points,
        quiz_points,
        progress_points,
        analytics_points,
        RANK() OVER (ORDER BY total_points DESC) as rank,
        NOW() as last_updated
      FROM user_total_points
      ORDER BY total_points DESC
    `);
    
    console.log('✅ Created materialized view with dynamic point calculation');
    
    // Create indexes
    await db.execute(sql`
      CREATE INDEX idx_user_points_leaderboard_mv_total_points 
      ON user_points_leaderboard_mv (total_points DESC)
    `);
    
    await db.execute(sql`
      CREATE INDEX idx_user_points_leaderboard_mv_user_id 
      ON user_points_leaderboard_mv (user_id)
    `);
    
    console.log('✅ Created indexes');
    
    // Check the results
    const result = await db.execute(sql`
      SELECT COUNT(*) as total_users, 
             MAX(total_points) as max_points,
             MIN(total_points) as min_points,
             AVG(total_points) as avg_points
      FROM user_points_leaderboard_mv
    `);
    
    console.log('📊 Materialized view statistics:');
    const stats = result[0] || result.rows?.[0] || {};
    console.log(`   Total users: ${stats.total_users || 'N/A'}`);
    console.log(`   Max points: ${stats.max_points || 'N/A'}`);
    console.log(`   Min points: ${stats.min_points || 'N/A'}`);
    console.log(`   Avg points: ${stats.avg_points ? Math.round(stats.avg_points) : 'N/A'}`);
    
    // Show top 5 users
    const topUsers = await db.execute(sql`
      SELECT user_name, total_points, quiz_points, progress_points, analytics_points, rank
      FROM user_points_leaderboard_mv
      ORDER BY rank
      LIMIT 5
    `);
    
    console.log('\n🏆 Top 5 users:');
    const users = topUsers.rows || topUsers || [];
    users.forEach((user, index) => {
      console.log(`   ${index + 1}. ${user.user_name}: ${user.total_points} pts (Q:${user.quiz_points}, L:${user.progress_points}, S:${user.analytics_points})`);
    });
    
    console.log('\n🎉 Materialized view refreshed successfully with dynamic points!');
    
  } catch (error) {
    console.error('❌ Error refreshing materialized view:', error);
    process.exit(1);
  }
}

refreshMaterializedView().then(() => {
  console.log('✅ Script completed successfully');
  process.exit(0);
}).catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});