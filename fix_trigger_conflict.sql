-- Fix for trigger conflict - Run this first to clean up existing triggers
-- This script removes any existing triggers that might conflict

-- Drop all possible existing triggers (both old and new names)
DROP TRIGGER IF EXISTS trigger_refresh_leaderboard_on_quiz_attempt ON quiz_attempt;
DROP TRIGGER IF EXISTS trigger_queue_leaderboard_refresh_on_quiz_attempt ON quiz_attempt;

DROP TRIGGER IF EXISTS trigger_refresh_leaderboard_on_progress ON learning_progress;
DROP TRIGGER IF EXISTS trigger_queue_leaderboard_refresh_on_progress ON learning_progress;

DROP TRIGGER IF EXISTS trigger_refresh_leaderboard_on_analytics ON learning_content_analytics;
DROP TRIGGER IF EXISTS trigger_queue_leaderboard_refresh_on_analytics ON learning_content_analytics;

DROP TRIGGER IF EXISTS trigger_refresh_leaderboard_on_user ON "user";
DROP TRIGGER IF EXISTS trigger_queue_leaderboard_refresh_on_user ON "user";

-- Now you can safely run the create_optimized_database_triggers.sql script