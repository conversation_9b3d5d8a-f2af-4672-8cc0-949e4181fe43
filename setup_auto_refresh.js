#!/usr/bin/env node

// <PERSON><PERSON>t to setup automatic leaderboard refresh using database triggers
// This script will create the triggers and test the auto-refresh functionality

import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import postgres from 'postgres';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });
dotenv.config({ path: '.env' });

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Database connection
const sql = postgres(process.env.DATABASE_URL, {
  ssl: process.env.NODE_ENV === 'production' ? 'require' : false,
});

async function setupAutoRefresh() {
  try {
    console.log('🚀 Setting up automatic leaderboard refresh...');
    
    // Step 1: Check if materialized view exists
    console.log('\n1. Checking if materialized view exists...');
    const mvExists = await sql`
      SELECT EXISTS (
        SELECT 1 FROM pg_matviews 
        WHERE matviewname = 'user_points_leaderboard_mv'
      ) as exists
    `;
    
    if (!mvExists[0].exists) {
      console.log('❌ Materialized view does not exist!');
      console.log('Please run the materialized view setup first:');
      console.log('1. Open Supabase SQL Editor');
      console.log('2. Run the script in create_leaderboard_materialized_view.sql');
      process.exit(1);
    }
    
    console.log('✅ Materialized view exists');
    
    // Step 2: Create database triggers
    console.log('\n2. Creating database triggers...');
    const triggerSQL = readFileSync(join(__dirname, 'create_database_triggers.sql'), 'utf8');
    await sql.unsafe(triggerSQL);
    console.log('✅ Database triggers created successfully');
    
    // Step 3: Check trigger status
    console.log('\n3. Checking trigger status...');
    const triggers = await sql`
      SELECT 
        event_object_table as table_name,
        trigger_name,
        'ENABLED' as trigger_enabled
      FROM information_schema.triggers 
      WHERE trigger_name LIKE 'trigger_refresh_leaderboard_%'
      ORDER BY event_object_table, trigger_name
    `;
    
    console.log('📋 Trigger Status:');
    triggers.forEach(trigger => {
      console.log(`   ${trigger.table_name}: ${trigger.trigger_name} (${trigger.trigger_enabled})`);
    });
    
    // Step 4: Test automatic refresh
    console.log('\n4. Testing automatic refresh functionality...');
    
    // Get current leaderboard state
    const beforeRefresh = await sql`
      SELECT last_updated FROM user_points_leaderboard_mv 
      ORDER BY last_updated DESC LIMIT 1
    `;
    
    console.log(`📊 Current leaderboard last updated: ${beforeRefresh[0]?.last_updated || 'Never'}`);
    
    // Simulate a data change that should trigger refresh
    console.log('\n5. Simulating data change to test auto-refresh...');
    
    // Find a user with analytics data to update
    const testUser = await sql`
      SELECT user_id FROM learning_content_analytics 
      WHERE event_type = 'view' 
      LIMIT 1
    `;
    
    if (testUser.length > 0) {
      const userId = testUser[0].user_id;
      
      // Get a content_id for the test
      const testContent = await sql`
        SELECT id FROM learning_content LIMIT 1
      `;
      
      if (testContent.length > 0) {
        const contentId = testContent[0].id;
        
        // Insert a test analytics event (this should trigger the refresh)
        await sql`
          INSERT INTO learning_content_analytics (id, content_id, user_id, event_type, session_id, created_at)
          VALUES (
            gen_random_uuid(), 
            ${contentId},
            ${userId}, 
            'test_auto_refresh', 
            'test-session-' || extract(epoch from now()), 
            now()
          )
        `;
      } else {
        console.log('⚠️  No learning content found for testing');
        return;
      }
      
      console.log('✅ Test analytics event inserted');
      
      // Wait a moment for the trigger to execute
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Check if leaderboard was refreshed
      const afterRefresh = await sql`
        SELECT last_updated FROM user_points_leaderboard_mv 
        ORDER BY last_updated DESC LIMIT 1
      `;
      
      const beforeTime = new Date(beforeRefresh[0]?.last_updated || 0);
      const afterTime = new Date(afterRefresh[0]?.last_updated || 0);
      
      if (afterTime > beforeTime) {
        console.log('🎉 SUCCESS: Automatic refresh is working!');
        console.log(`📊 Leaderboard updated: ${afterRefresh[0].last_updated}`);
      } else {
        console.log('⚠️  WARNING: Automatic refresh may not be working');
        console.log('The leaderboard timestamp did not change after data modification');
      }
      
      // Clean up test data
      await sql`
        DELETE FROM analytics 
        WHERE event_type = 'test_auto_refresh' AND user_id = ${userId}
      `;
      
    } else {
      console.log('⚠️  No test user found, skipping auto-refresh test');
    }
    
    // Step 6: Provide usage instructions
    console.log('\n6. Setup Complete! 🎉');
    console.log('\n📋 What was set up:');
    console.log('   ✅ Database triggers for automatic refresh');
    console.log('   ✅ Triggers on: quiz_attempt, user_progress, analytics, user tables');
    console.log('   ✅ Non-blocking concurrent refresh');
    console.log('   ✅ Recursive trigger prevention');
    
    console.log('\n🔧 Management functions available:');
    console.log('   • Check trigger status: SELECT * FROM check_leaderboard_triggers();');
    console.log('   • Disable triggers: SELECT manage_leaderboard_triggers(false);');
    console.log('   • Enable triggers: SELECT manage_leaderboard_triggers(true);');
    console.log('   • Manual refresh: REFRESH MATERIALIZED VIEW CONCURRENTLY user_points_leaderboard_mv;');
    
    console.log('\n🚀 The leaderboard will now automatically refresh when:');
    console.log('   • Users complete quizzes');
    console.log('   • Learning content is completed');
    console.log('   • New learning sessions are recorded');
    console.log('   • User profile data changes');
    
    console.log('\n✨ No more manual refresh needed!');
    
  } catch (error) {
    console.error('❌ Error setting up automatic refresh:', error);
    process.exit(1);
  } finally {
    await sql.end();
  }
}

// Run the setup
setupAutoRefresh();