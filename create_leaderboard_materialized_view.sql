-- Create materialized view for user leaderboard with aggregated points
-- This script should be run manually in your Supabase SQL editor
-- to create the materialized view before using it in the application

-- Handle existing object: could be a table (from Drizzle migration) or materialized view
-- First, check if it exists as a table and drop it
DO $$
BEGIN
    -- Check if the object exists as a table
    IF EXISTS (
        SELECT 1 FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'user_points_leaderboard_mv'
    ) THEN
        DROP TABLE user_points_leaderboard_mv CASCADE;
        RAISE NOTICE 'Dropped existing table user_points_leaderboard_mv';
    END IF;

    -- Check if it exists as a materialized view and drop it
    IF EXISTS (
        SELECT 1 FROM pg_matviews
        WHERE schemaname = 'public'
        AND matviewname = 'user_points_leaderboard_mv'
    ) THEN
        DROP MATERIALIZED VIEW user_points_leaderboard_mv CASCADE;
        RAISE NOTICE 'Dropped existing materialized view user_points_leaderboard_mv';
    END IF;
END $$;

CREATE MATERIALIZED VIEW user_points_leaderboard_mv AS
WITH user_quiz_points AS (
  SELECT 
    qa."user_id" as user_id,
    COALESCE(SUM(
      CASE 
        WHEN (qa.score->>'percentage')::numeric >= 90 THEN 100
        WHEN (qa.score->>'percentage')::numeric >= 80 THEN 80
        WHEN (qa.score->>'percentage')::numeric >= 70 THEN 60
        WHEN (qa.score->>'percentage')::numeric >= 60 THEN 40
        ELSE 20
      END
    ), 0) as quiz_points
  FROM "quiz_attempt" qa
  WHERE qa."completed_at" IS NOT NULL
    AND qa.score IS NOT NULL
    AND qa.score->>'percentage' IS NOT NULL
  GROUP BY qa."user_id"
),
user_progress_points AS (
  SELECT 
    lp."user_id" as user_id,
    COALESCE(SUM(
      -- Base points (30-70 based on difficulty)
      CASE 
        WHEN lc."learning_level" = 'beginner' THEN 30
        WHEN lc."learning_level" = 'intermediate' THEN 50
        WHEN lc."learning_level" = 'advanced' THEN 70
        ELSE 50 -- fallback
      END +
      -- Duration bonus (0-30 points based on estimated reading time)
      CASE 
        WHEN lc."estimated_reading_time" >= 15 THEN 15  -- 15+ min content (capped)
        WHEN lc."estimated_reading_time" >= 10 THEN 10  -- 10-14 min content
        WHEN lc."estimated_reading_time" >= 5 THEN 5    -- 5-9 min content
        ELSE 0 -- <5 min content gets no bonus
      END
    ), 0) as progress_points
  FROM "learning_progress" lp
  JOIN "learning_content" lc ON lp."content_id" = lc."id"
  WHERE lp."is_completed" = true
  GROUP BY lp."user_id"
),
user_analytics_points AS (
  SELECT 
    lca."user_id" as user_id,
    COALESCE(COUNT(DISTINCT lca."session_id") * 10, 0) as analytics_points
  FROM "learning_content_analytics" lca
  WHERE lca."session_id" IS NOT NULL
  GROUP BY lca."user_id"
),
user_total_points AS (
  SELECT 
    u.id as user_id,
    u.name as user_name,
    u.email as user_email,
    COALESCE(uqp.quiz_points, 0) + 
    COALESCE(upp.progress_points, 0) + 
    COALESCE(uap.analytics_points, 0) as total_points,
    COALESCE(uqp.quiz_points, 0) as quiz_points,
    COALESCE(upp.progress_points, 0) as progress_points,
    COALESCE(uap.analytics_points, 0) as analytics_points
  FROM "user" u
  LEFT JOIN user_quiz_points uqp ON u.id = uqp.user_id
  LEFT JOIN user_progress_points upp ON u.id = upp.user_id
  LEFT JOIN user_analytics_points uap ON u.id = uap.user_id
)
SELECT
  user_id::text,
  user_name,
  user_email,
  total_points,
  quiz_points,
  progress_points,
  analytics_points,
  RANK() OVER (ORDER BY total_points DESC) as rank,
  NOW() as last_updated
FROM user_total_points
ORDER BY total_points DESC;

-- Create indexes on the materialized view for better query performance
CREATE INDEX IF NOT EXISTS idx_user_points_leaderboard_mv_total_points 
ON user_points_leaderboard_mv (total_points DESC);

CREATE INDEX IF NOT EXISTS idx_user_points_leaderboard_mv_user_id 
ON user_points_leaderboard_mv (user_id);

-- Refresh the materialized view to populate it with data
REFRESH MATERIALIZED VIEW user_points_leaderboard_mv;

-- Optional: Create a function to refresh the materialized view
-- This can be called periodically or triggered by events
CREATE OR REPLACE FUNCTION refresh_leaderboard_mv()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW user_points_leaderboard_mv;
END;
$$ LANGUAGE plpgsql;

-- Optional: Create a scheduled job to refresh the view every hour
-- Note: This requires the pg_cron extension to be enabled in Supabase
-- You can enable it in the Supabase dashboard under Database > Extensions
/*
SELECT cron.schedule(
  'refresh-leaderboard',
  '0 * * * *', -- Every hour
  'SELECT refresh_leaderboard_mv();'
);
*/

-- Manual refresh command (run this whenever you want to update the leaderboard)
-- REFRESH MATERIALIZED VIEW user_points_leaderboard_mv;