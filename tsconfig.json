{"include": ["**/*.ts", "**/*.tsx", "**/.server/**/*.ts", "**/.server/**/*.tsx", "**/.client/**/*.ts", "**/.client/**/*.tsx", ".react-router/types/**/*"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2022"], "isolatedModules": true, "esModuleInterop": true, "jsx": "react-jsx", "moduleResolution": "bundler", "resolveJsonModule": true, "target": "ES2022", "module": "es2022", "strict": true, "allowJs": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"~/*": ["./app/*"]}, "types": ["@react-router/node", "vite/client"], "rootDirs": [".", "./.react-router/types"], "noEmit": true}}