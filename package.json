{"name": "kwaci-learning", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "react-router build", "dev": "vite dev", "lint": "eslint --ignore-path .gitignore --cache --cache-location ./node_modules/.cache/eslint .", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc", "deploy:dev": "bun run build && wrangler deploy --env develop", "preview": "vite dev", "cf:login": "wrangler login", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "batch-index": "bun run scripts/batch-index-content.ts", "batch-index-worker": "bunx wrangler dev scripts/batch-index-worker.ts --port 8787 --env=develop --experimental-vectorize-bind-to-prod", "test-rag-worker": "bunx wrangler dev scripts/test-rag-worker.ts --port 8787 --env=develop --experimental-vectorize-bind-to-prod", "call-batch-worker": "bun run scripts/call-batch-worker.ts", "call-test-rag-worker": "bun run scripts/call-test-rag-worker.ts", "test-rag-chat": "bun run scripts/test-rag-chat.ts"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^5.1.1", "@langchain/cloudflare": "^0.1.0", "@langchain/community": "^0.3.47", "@langchain/core": "^0.3.61", "@openrouter/ai-sdk-provider": "^0.7.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-router/fs-routes": "^7.7.1", "@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "@supabase/ssr": "^0.7.0", "@supabase/supabase-js": "^2.56.0", "@tanstack/react-query": "^5.84.1", "ai": "^4.3.16", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "drizzle-orm": "^0.44.2", "framer-motion": "^12.18.1", "isbot": "^4.1.0", "lucide-react": "^0.536.0", "nanoid": "^5.1.5", "postgres": "^3.4.7", "react": "19", "react-day-picker": "^9.8.1", "react-dom": "19", "react-hook-form": "^7.58.1", "react-router": "^7.7.1", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.23.8", "zustand": "^5.0.7"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.11.0", "@cloudflare/workers-types": "^4.20250805.0", "@react-router/dev": "^7.7.1", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query-devtools": "^5.84.1", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "16.1.0", "@testing-library/user-event": "^14.6.1", "@types/canvas-confetti": "^1.9.0", "@types/jest": "^29.5.12", "@types/react": "19", "@types/react-dom": "19", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^6.7.4", "@typescript-eslint/parser": "^6.7.4", "autoprefixer": "^10.4.21", "drizzle-kit": "^0.31.1", "eslint": "^8.38.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.28.1", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.1.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "tsx": "^4.20.5", "typescript": "^5.1.6", "vite": "^6.0.0", "vite-tsconfig-paths": "^4.2.1", "wrangler": "^4.27.0"}, "engines": {"node": ">=20.0.0"}}