/**
 * Test script for RAG services initialization
 * Run with: node test-rag-setup.js
 */

import { config } from 'dotenv';

// Load environment variables
config();

async function testRagSetup() {
  console.log('🧪 Testing RAG services setup...\n');

  try {
    // Import the health check function
    const { checkRagHealth } = await import('./app/lib/startup/rag-setup.ts');
    
    // Run health check
    const healthStatus = await checkRagHealth();
    
    console.log('📊 RAG Health Check Results:');
    console.log('============================');
    console.log(`Overall Status: ${healthStatus.isHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);
    console.log('\n🔧 Configuration:');
    console.log(`  Voyage API Key: ${healthStatus.configuration.voyageApiKey ? '✅ Set' : '❌ Missing'}`);
    console.log(`  Vectorize Index: ${healthStatus.configuration.vectorizeIndex ? '✅ Set' : '❌ Missing'}`);
    console.log('\n⚙️ Services:');
    console.log(`  Embedding Service: ${healthStatus.services.embedding ? '✅ Initialized' : '❌ Not initialized'}`);
    console.log(`  Vector Indexing Service: ${healthStatus.services.vectorIndexing ? '✅ Initialized' : '❌ Not initialized'}`);
    console.log(`  RAG Search Service: ${healthStatus.services.ragSearch ? '✅ Initialized' : '❌ Not initialized'}`);
    
    if (healthStatus.errors.length > 0) {
      console.log('\n❌ Errors:');
      healthStatus.errors.forEach(error => {
        console.log(`  - ${error}`);
      });
    }

    console.log('\n🔍 Environment Variables Check:');
    console.log(`  VOYAGE_API_KEY: ${process.env.VOYAGE_API_KEY ? '✅ Set' : '❌ Missing'}`);
    console.log(`  VECTORIZE_INDEX_NAME: ${process.env.VECTORIZE_INDEX_NAME ? '✅ Set' : '❌ Missing'}`);
    console.log(`  OPENROUTER_API_KEY: ${process.env.OPENROUTER_API_KEY ? '✅ Set' : '❌ Missing'}`);

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.stack) {
      console.error('\n📝 Stack trace:');
      console.error(error.stack);
    }
  }
}

// Run the test
testRagSetup().then(() => {
  console.log('\n✅ Test completed');
}).catch(error => {
  console.error('\n❌ Test failed with error:', error);
  process.exit(1);
});