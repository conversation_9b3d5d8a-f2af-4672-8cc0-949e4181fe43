{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "strict": true, "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "types": ["@cloudflare/workers-types"], "lib": ["ES2022", "WebWorker"]}, "include": ["src/**/*", "migrations/**/*"], "exclude": ["node_modules", "dist"]}