# Auth Worker

A standalone Better Auth service running on Cloudflare Workers with D1 database. This worker provides authentication endpoints that can be consumed by other applications via HTTP API calls.

## Features

- 🔐 **Better Auth Integration** - Modern, type-safe authentication
- 🗄️ **Cloudflare D1 Database** - SQLite database for authentication data
- 🚀 **Cloudflare Workers** - Edge deployment for global performance
- 🌐 **CORS Support** - Cross-origin requests from client applications
- 📝 **TypeScript** - Full type safety
- 🔄 **Shared Configuration** - Reuses auth config from main application

## Setup

### 1. Install Dependencies

The worker uses the root package.json dependencies via workspace references:

```bash
# From the root directory
bun install
```

### 2. Create D1 Databases

Create D1 databases for development and production:

```bash
cd auth-worker

# Create development database
bun run db:create:dev

# Create production database  
bun run db:create:prod
```

Update the database IDs in `wrangler.toml` with the IDs returned from the commands above.

### 3. Run Database Migrations

Apply the authentication schema to your D1 databases:

```bash
# Migrate development database
bun run db:migrate:dev

# Migrate production database
bun run db:migrate:prod
```

### 4. Set Environment Variables

Set the required secrets for both environments:

```bash
# Development environment
bun run secrets:put:dev
# Enter your BETTER_AUTH_SECRET when prompted

# Production environment
bun run secrets:put:prod
# Enter your BETTER_AUTH_SECRET when prompted
```

### 5. Update Configuration

Edit `wrangler.toml` to update:
- Database IDs (from step 2)
- Worker URLs for your account
- Trusted origins for your applications

## Development

### Local Development

Run the worker locally:

```bash
# Connect to deployed D1 database
bun run dev

# Or run completely local (with local D1)
bun run dev:local
```

The worker will be available at `http://localhost:8787`

### Testing

Test the authentication endpoints:

```bash
# Health check
curl http://localhost:8787/health

# API info
curl http://localhost:8787/api

# Sign up (example)
curl -X POST http://localhost:8787/api/auth/sign-up \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","name":"Test User"}'
```

## Deployment

### Development Environment

```bash
bun run deploy:dev
```

### Production Environment

```bash
bun run deploy:prod
```

## API Endpoints

The worker exposes the following endpoints:

- `GET /` - Service information
- `GET /health` - Health check
- `GET /api` - API documentation
- `POST /api/auth/sign-up` - User registration
- `POST /api/auth/sign-in` - User login
- `POST /api/auth/sign-out` - User logout
- `GET /api/auth/session` - Get current session
- `GET /api/auth/user` - Get current user

## Integration

### From Client Applications

```typescript
// Configure your client to use the auth worker
const authClient = createAuthClient({
  baseURL: 'https://auth-worker.your-account.workers.dev',
  // Add any additional configuration
});

// Use the client for authentication
await authClient.signIn.email({
  email: '<EMAIL>',
  password: 'password123'
});
```

### Environment Variables

The worker expects these environment variables:

- `BETTER_AUTH_SECRET` - Secret key for auth (set via Wrangler secrets)
- `BETTER_AUTH_URL` - Base URL for the auth service
- `AUTH_WORKER_URL` - URL of the deployed worker
- `BETTER_AUTH_TRUSTED_ORIGINS` - Comma-separated list of allowed origins

## Database Management

### View Database Contents

```bash
# Development database
bun run db:console:dev

# Production database
bun run db:console:prod
```

### Backup and Restore

Use Wrangler commands to backup and restore your D1 databases:

```bash
# Export database
wrangler d1 export auth-database-dev --env develop

# Import database
wrangler d1 import auth-database-dev --env develop --file backup.sql
```

## Troubleshooting

### Common Issues

1. **Database ID not found**: Update the database IDs in `wrangler.toml`
2. **CORS errors**: Check the trusted origins configuration
3. **Authentication failures**: Verify the `BETTER_AUTH_SECRET` is set correctly
4. **Migration errors**: Ensure the database exists before running migrations

### Debugging

Enable debug logging by setting environment variables in `wrangler.toml`:

```toml
[env.develop.vars]
DEBUG = "true"
LOG_LEVEL = "debug"
```

## Security Considerations

- Always use HTTPS in production
- Set strong `BETTER_AUTH_SECRET` values
- Regularly rotate authentication secrets
- Monitor authentication logs for suspicious activity
- Keep trusted origins list minimal and specific
