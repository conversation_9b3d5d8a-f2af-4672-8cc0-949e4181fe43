# Cloudflare Worker configuration for Better Auth service
# This worker provides standalone authentication using Better Auth with D1

name = "auth-worker"
main = "src/index.ts"
compatibility_date = "2024-12-30"
compatibility_flags = ["nodejs_compat"]

# D1 Database binding for authentication data
[[d1_databases]]
binding = "AUTH_DB"
database_name = "auth-database"
database_id = "your-d1-database-id" # Replace with actual D1 database ID

# Development environment configuration
[env.develop]
name = "auth-worker-dev"
vars = { 
  BETTER_AUTH_URL = "https://auth-worker-dev.your-account.workers.dev",
  AUTH_WORKER_URL = "https://auth-worker-dev.your-account.workers.dev",
  BETTER_AUTH_TRUSTED_ORIGINS = "http://localhost:5173,http://localhost:3000,http://localhost:3001,http://localhost:8787,https://kwaci-learning.bmbn.dev"
}

[[env.develop.d1_databases]]
binding = "AUTH_DB"
database_name = "auth-database-dev"
database_id = "your-dev-d1-database-id" # Replace with actual dev D1 database ID

# Production environment configuration
[env.production]
name = "auth-worker-prod"
vars = { 
  BETTER_AUTH_URL = "https://auth-worker.your-account.workers.dev",
  AUTH_WORKER_URL = "https://auth-worker.your-account.workers.dev",
  BETTER_AUTH_TRUSTED_ORIGINS = "https://kwaci-learning.bmbn.dev,https://your-production-domain.com"
}

[[env.production.d1_databases]]
binding = "AUTH_DB"
database_name = "auth-database-prod"
database_id = "your-prod-d1-database-id" # Replace with actual prod D1 database ID

# Optional: Custom domain configuration for production
# [env.production.routes]
# pattern = "auth.your-domain.com/*"

# Note: Sensitive variables should be set via Wrangler secrets:
# wrangler secret put BETTER_AUTH_SECRET --env develop
# wrangler secret put BETTER_AUTH_SECRET --env production
