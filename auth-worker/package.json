{"name": "auth-worker", "version": "1.0.0", "description": "Standalone Better Auth service for Cloudflare Workers with D1 database", "type": "module", "main": "src/index.ts", "scripts": {"dev": "wrangler dev --env develop", "dev:local": "wrangler dev --local --env develop", "build": "tsc --noEmit", "deploy:dev": "wrangler deploy --env develop", "deploy:prod": "wrangler deploy --env production", "db:create:dev": "wrangler d1 create auth-database-dev", "db:create:prod": "wrangler d1 create auth-database-prod", "db:migrate:dev": "wrangler d1 migrations apply auth-database-dev --env develop", "db:migrate:prod": "wrangler d1 migrations apply auth-database-prod --env production", "db:console:dev": "wrangler d1 execute auth-database-dev --env develop", "db:console:prod": "wrangler d1 execute auth-database-prod --env production", "secrets:put:dev": "wrangler secret put BETTER_AUTH_SECRET --env develop", "secrets:put:prod": "wrangler secret put BETTER_AUTH_SECRET --env production", "secrets:list:dev": "wrangler secret list --env develop", "secrets:list:prod": "wrangler secret list --env production", "typecheck": "tsc --noEmit", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"better-auth": "workspace:*", "drizzle-orm": "workspace:*", "hono": "^4.6.0"}, "devDependencies": {"@cloudflare/workers-types": "workspace:*", "typescript": "workspace:*", "wrangler": "workspace:*"}, "keywords": ["cloudflare-workers", "better-auth", "authentication", "d1", "sqlite", "hono"], "author": "Your Name", "license": "MIT"}