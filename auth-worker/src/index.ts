/**
 * Cloudflare Worker for Better Auth
 * 
 * This worker provides a standalone authentication service using Better Auth
 * with Cloudflare D1 as the database backend. It can be consumed by other
 * applications via HTTP API calls.
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { createAuthForD1, type CloudflareEnv } from './auth-config';

// Create Hono app instance
const app = new Hono<{ Bindings: CloudflareEnv }>();

// Global CORS configuration for all routes
app.use('*', cors({
  origin: (origin) => {
    // Allow all localhost origins for development
    if (origin?.includes('localhost') || origin?.includes('127.0.0.1')) {
      return origin;
    }
    
    // Allow specific production origins
    const allowedOrigins = [
      'https://kwaci-learning.bmbn.dev',
      'https://kwaci-learning-dev.bm.workers.dev',
      'https://learn-platform-web-preview.bm.workers.dev',
      'https://learn-platform-admin-preview.bm.workers.dev',
    ];
    
    if (origin && allowedOrigins.includes(origin)) {
      return origin;
    }
    
    // For development, allow any origin
    return origin || '*';
  },
  allowHeaders: [
    'Content-Type',
    'Authorization',
    'Cookie',
    'Set-Cookie',
    'X-Requested-With',
  ],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  credentials: true,
  maxAge: 86400, // 24 hours
}));

// Health check endpoint
app.get('/', (c) => {
  return c.json({
    service: 'Auth Worker',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
  });
});

// Health check endpoint for monitoring
app.get('/health', (c) => {
  return c.json({
    status: 'ok',
    service: 'auth-worker',
    timestamp: new Date().toISOString(),
  });
});

// Mount Better Auth handler for all authentication endpoints
app.on(['GET', 'POST'], '/api/auth/**', async (c) => {
  try {
    console.log(`[Auth Worker] Handling auth request: ${c.req.method} ${c.req.url}`);
    
    // Create auth instance with D1 database
    const auth = createAuthForD1(c.env);
    
    // Handle the authentication request
    const response = await auth.handler(c.req.raw);
    
    console.log(`[Auth Worker] Auth request completed: ${response.status}`);
    
    return response;
  } catch (error) {
    console.error('[Auth Worker] Error handling auth request:', error);
    
    return c.json({
      error: 'Internal server error',
      message: 'Authentication request failed',
    }, 500);
  }
});

// API info endpoint
app.get('/api', (c) => {
  return c.json({
    service: 'Better Auth API',
    endpoints: {
      signIn: 'POST /api/auth/sign-in',
      signUp: 'POST /api/auth/sign-up',
      signOut: 'POST /api/auth/sign-out',
      session: 'GET /api/auth/session',
      user: 'GET /api/auth/user',
    },
    documentation: 'https://better-auth.com/docs',
  });
});

// Catch-all for undefined routes
app.notFound((c) => {
  return c.json({
    error: 'Not Found',
    message: 'The requested endpoint does not exist',
    availableEndpoints: [
      'GET /',
      'GET /health',
      'GET /api',
      'GET|POST /api/auth/**',
    ],
  }, 404);
});

// Global error handler
app.onError((err, c) => {
  console.error('[Auth Worker] Unhandled error:', err);
  
  return c.json({
    error: 'Internal Server Error',
    message: 'An unexpected error occurred',
    timestamp: new Date().toISOString(),
  }, 500);
});

// Export the Hono app as the default export for Cloudflare Workers
export default app;
