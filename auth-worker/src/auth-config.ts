/**
 * Better Auth configuration for Cloudflare D1 Auth Worker
 * 
 * This module creates a Better Auth configuration specifically for D1 (SQLite)
 * while reusing the configuration logic from the main application.
 */

import { betterAuth } from 'better-auth';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { drizzle } from 'drizzle-orm/d1';
import { user, session, account, verification } from './schema';

/**
 * Cloudflare Worker environment interface
 */
interface CloudflareEnv {
  AUTH_DB: D1Database;
  BETTER_AUTH_SECRET?: string;
  BETTER_AUTH_URL?: string;
  BETTER_AUTH_TRUSTED_ORIGINS?: string;
  AUTH_WORKER_URL?: string;
}

/**
 * Get shared authentication configuration
 * This reuses the same logic as the main application
 */
function getSharedAuthConfig(env: CloudflareEnv) {
  const secret = env.BETTER_AUTH_SECRET;
  const baseURL = env.BETTER_AUTH_URL || env.AUTH_WORKER_URL || 'http://localhost:8787';

  // Get custom trusted origins from environment variable
  const customOrigins = env.BETTER_AUTH_TRUSTED_ORIGINS;
  const trustedOrigins = customOrigins 
    ? customOrigins.split(',').map((origin: string) => origin.trim()).filter(Boolean) 
    : [];

  // Filter out origins that are already hardcoded to avoid unnecessary duplicates
  const hardcodedOrigins = [
    'http://localhost:5173',
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:8787',
    'http://127.0.0.1:8787',
  ];
  const filteredTrustedOrigins = trustedOrigins.filter(origin => !hardcodedOrigins.includes(origin));

  console.log(`[Auth Worker] Environment: Cloudflare D1`);
  console.log(`[Auth Worker] Base URL: ${baseURL}`);
  console.log(`[Auth Worker] Secret set: ${!!secret}`);
  console.log(`[Auth Worker] Custom trusted origins: ${customOrigins || 'none'}`);

  if (!secret) {
    // For development, use a default secret
    console.warn('BETTER_AUTH_SECRET not set, using default secret for development');
    const defaultSecret = 'dev-secret-key-change-this-in-production-min-32-chars';
    return {
      secret: defaultSecret,
      baseURL,
      trustedOrigins: filteredTrustedOrigins,
    };
  }

  return {
    secret,
    baseURL,
    trustedOrigins: filteredTrustedOrigins,
  };
}

/**
 * Create Better Auth instance for Cloudflare D1
 */
export function createAuthForD1(env: CloudflareEnv) {
  const config = getSharedAuthConfig(env);
  
  console.log(`[Auth Worker] Creating auth instance for Cloudflare D1`);

  // Create D1 database connection
  const db = drizzle(env.AUTH_DB);

  const authConfig = {
    database: drizzleAdapter(db, {
      provider: 'sqlite', // SQLite for D1
      schema: {
        user,
        session,
        account,
        verification,
      },
    }),
    secret: config.secret,
    baseURL: config.baseURL,
    trustedOrigins: (() => {
      const origins = [
        // Development origins
        'http://localhost:5173',
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:8787',
        'http://127.0.0.1:8787',
        'https://kwaci-learning-dev.bm.workers.dev',
        // Include baseURL if it's different from the above
        ...(config.baseURL && !config.baseURL.includes('localhost') ? [config.baseURL] : []),
        // Include any custom trusted origins from environment (filtered to avoid duplicates)
        ...(config.trustedOrigins || []),
      ];

      // Remove duplicates
      const uniqueOrigins = [...new Set(origins)];

      console.log(`[Auth Worker] Configured trusted origins: ${uniqueOrigins.join(', ')}`);

      return uniqueOrigins;
    })(),

    // Email and password authentication
    emailAndPassword: {
      enabled: true,
      requireEmailVerification: false, // Can be enabled later
    },

    // Session configuration
    session: {
      expiresIn: 60 * 60 * 24 * 7, // 7 days
      updateAge: 60 * 60 * 24, // 1 day
      cookieCache: {
        enabled: true,
        maxAge: 5 * 60, // 5 minutes
      },
    },

    // Advanced configuration
    advanced: {
      crossSubDomainCookies: {
        enabled: false, // Enable if using subdomains
      },
      defaultCookieAttributes: {
        sameSite: 'lax' as const,
        secure: true, // Always secure in production
        httpOnly: true,
      },
    },
  };

  console.log(`[Auth Worker] Auth instance created successfully`);

  return betterAuth(authConfig);
}

/**
 * Export the auth configuration for reuse
 */
export { getSharedAuthConfig };
export type { CloudflareEnv };
