# Kwaci Learning

A starter Remix.js application with TypeScript, Tailwind CSS, and shadcn/ui.

## Tech Stack

- **Remix.js** - Full-stack web framework
- **TypeScript** - Type-safe JavaScript
- **Tailwind CSS v4** - Latest utility-first CSS framework with improved performance
- **shadcn/ui** - Beautiful UI components
- **Vite** - Fast build tool
- **Bun** - JavaScript runtime and package manager

## Getting Started

1. Install dependencies:
   ```bash
   bun install
   ```

2. Start the development server:
   ```bash
   bun run dev
   ```

3. Open [http://localhost:5173](http://localhost:5173) in your browser.

## Available Scripts

- `bun run dev` - Start development server
- `bun run build` - Build for production
- `bun run start` - Start production server
- `bun run typecheck` - Run TypeScript type checking
- `bun run lint` - Run ESLint

## Project Structure

```
app/
├── components/ui/     # shadcn/ui components
├── lib/               # Utility functions
├── routes/            # Remix routes
├── entry.client.tsx   # Client entry point
├── entry.server.tsx   # Server entry point
├── root.tsx           # Root component
└── tailwind.css       # Tailwind CSS styles
```

## Adding shadcn/ui Components

The project is configured with shadcn/ui. You can add components manually or use the CLI once it's available for Remix projects.

## Tailwind CSS v4 Setup

This project uses Tailwind CSS v4 with the new `@import "tailwindcss"` syntax in the CSS file. Key benefits:
- 5x faster full builds
- 100x faster incremental builds  
- Zero configuration needed
- First-party Vite plugin integration
- No PostCSS configuration required

The styles are automatically applied when you run `bun run dev` or `bun run build`.

## Features

### Learning Platform
- **Learning Content Management** - Create, view, and manage learning materials
- **Interactive Quizzes** - Generate and take quizzes with AI-powered questions
- **Progress Tracking** - Monitor learning progress and quiz performance
- **Bookmarking System** - Save and organize learning content
- **AI Assistant** - Ask questions about learning content with context-aware responses

### User Interface
- **Responsive Dashboard** - Modern dashboard with sidebar navigation
- **Reusable Sidebar Components** - Configurable left and right sidebars
- **Dark/Light Theme Support** - Theme switching with system preference detection
- **Mobile-Friendly** - Responsive design that works on all devices

### Authentication & Security
- **User Authentication** - Secure login and registration system
- **Session Management** - Persistent user sessions
- **Protected Routes** - Route-level authentication guards

## Database Setup

This project uses Drizzle ORM with SQLite for development. The database schema includes:

- Users and authentication
- Learning content and progress
- Quiz system with attempts and results
- Bookmarks and user preferences

To set up the database:

1. Copy the environment file:
   ```bash
   cp .env.example .env
   ```

2. Run database migrations:
   ```bash
   bun run db:migrate
   ```

3. (Optional) Seed the database:
   ```bash
   bun run db:seed
   ```

## Component Architecture

### Sidebar Components

The project includes reusable sidebar components:

- **`SidebarRight`** - Generic reusable right sidebar component
- **`SidebarRightLearning`** - Learning-specific wrapper for the right sidebar
- **`SidebarLeft`** - Left navigation sidebar with main menu

Example usage of the generic sidebar:

```tsx
import { SidebarRight } from '~/components/sidebar-right';

const tabs = [
  {
    id: 'settings',
    label: 'Settings',
    content: <SettingsPanel />
  },
  {
    id: 'users',
    label: 'Users',
    content: <UsersList />
  }
];

<SidebarRight
  isOpen={isOpen}
  isPinned={isPinned}
  title="Configuration"
  tabs={tabs}
  onToggle={() => setIsOpen(!isOpen)}
  onTogglePin={() => setIsPinned(!isPinned)}
  onClose={() => setIsOpen(false)}
/>
```

### State Management

The application uses Zustand for state management:

- **UI Store** - Manages sidebar states, learning content, and UI preferences
- **Theme Store** - Handles theme switching and persistence

## AI Integration

### RAG (Retrieval-Augmented Generation)
- Context-aware AI responses based on learning content
- Vector embeddings for content similarity
- Intelligent question answering

### Quiz Generation
- AI-powered quiz question generation
- Multiple question types support
- Difficulty level adaptation

## Development

### Code Quality
- TypeScript for type safety
- ESLint for code linting
- Prettier for code formatting
- Consistent component patterns

### Testing
- Component testing setup
- API route testing
- Database testing utilities

## Deployment

The application is configured for deployment on Cloudflare Pages with:

- Cloudflare Workers for serverless functions
- Cloudflare D1 for production database
- Environment variable configuration

To deploy:

1. Build the application:
   ```bash
   bun run build
   ```

2. Deploy to Cloudflare Pages:
   ```bash
   bunx wrangler pages deploy
   ```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes and commit: `git commit -m 'Add new feature'`
4. Push to the branch: `git push origin feature/new-feature`
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.