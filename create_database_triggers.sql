-- Database Triggers for Automatic Leaderboard Refresh
-- This script creates triggers that automatically refresh the materialized view
-- whenever data that affects leaderboard points is modified

-- Function to refresh the materialized view (reusable)
CREATE OR REPLACE FUNCTION refresh_leaderboard_mv_trigger()
<PERSON><PERSON><PERSON><PERSON> trigger AS $$
BEGIN
  -- Use REFRESH MATERIALIZED VIEW CONCURRENTLY for non-blocking updates
  -- This allows queries to continue while the view is being refreshed
  REFRESH MATERIALIZED VIEW CONCURRENTLY user_points_leaderboard_mv;
  
  -- Return the appropriate record based on trigger type
  IF TG_OP = 'DELETE' THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Trigger for quiz_attempt table (when quiz attempts are completed)
CREATE OR REPLACE TRIGGER trigger_refresh_leaderboard_on_quiz_attempt
  AFTER INSERT OR UPDATE OR DELETE ON quiz_attempt
  FOR EACH ROW
  WHEN (pg_trigger_depth() = 0) -- Prevent recursive triggers
  EXECUTE FUNCTION refresh_leaderboard_mv_trigger();

-- Trigger for learning_progress table (when learning content is completed)
CREATE OR REPLACE TRIGGER trigger_refresh_leaderboard_on_progress
  AFTER INSERT OR UPDATE OR DELETE ON learning_progress
  FOR EACH ROW
  WHEN (pg_trigger_depth() = 0) -- Prevent recursive triggers
  EXECUTE FUNCTION refresh_leaderboard_mv_trigger();

-- Trigger for learning_content_analytics table (when new learning sessions are recorded)
CREATE OR REPLACE TRIGGER trigger_refresh_leaderboard_on_analytics
  AFTER INSERT OR UPDATE OR DELETE ON learning_content_analytics
  FOR EACH ROW
  WHEN (pg_trigger_depth() = 0) -- Prevent recursive triggers
  EXECUTE FUNCTION refresh_leaderboard_mv_trigger();

-- Optional: Trigger for user table (when user data changes)
CREATE OR REPLACE TRIGGER trigger_refresh_leaderboard_on_user
  AFTER UPDATE ON "user"
  FOR EACH ROW
  WHEN (pg_trigger_depth() = 0 AND (OLD.name IS DISTINCT FROM NEW.name OR OLD.email IS DISTINCT FROM NEW.email))
  EXECUTE FUNCTION refresh_leaderboard_mv_trigger();

-- Create a function to disable/enable triggers for maintenance
CREATE OR REPLACE FUNCTION manage_leaderboard_triggers(enable_triggers boolean)
RETURNS void AS $$
BEGIN
  IF enable_triggers THEN
    -- Enable all leaderboard triggers
    ALTER TABLE quiz_attempt ENABLE TRIGGER trigger_refresh_leaderboard_on_quiz_attempt;
    ALTER TABLE learning_progress ENABLE TRIGGER trigger_refresh_leaderboard_on_progress;
    ALTER TABLE learning_content_analytics ENABLE TRIGGER trigger_refresh_leaderboard_on_analytics;
    ALTER TABLE "user" ENABLE TRIGGER trigger_refresh_leaderboard_on_user;
    RAISE NOTICE 'Leaderboard triggers enabled';
  ELSE
    -- Disable all leaderboard triggers
    ALTER TABLE quiz_attempt DISABLE TRIGGER trigger_refresh_leaderboard_on_quiz_attempt;
    ALTER TABLE learning_progress DISABLE TRIGGER trigger_refresh_leaderboard_on_progress;
    ALTER TABLE learning_content_analytics DISABLE TRIGGER trigger_refresh_leaderboard_on_analytics;
    ALTER TABLE "user" DISABLE TRIGGER trigger_refresh_leaderboard_on_user;
    RAISE NOTICE 'Leaderboard triggers disabled';
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to check trigger status
CREATE OR REPLACE FUNCTION check_leaderboard_triggers()
RETURNS TABLE(table_name text, trigger_name text, status text) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.event_object_table::text,
    t.trigger_name::text,
    'ENABLED'::text  -- PostgreSQL triggers are enabled by default when created
  FROM information_schema.triggers t
  WHERE t.trigger_name LIKE 'trigger_refresh_leaderboard_%'
  ORDER BY t.event_object_table, t.trigger_name;
END;
$$ LANGUAGE plpgsql;

-- Initial refresh to ensure the materialized view is up to date
REFRESH MATERIALIZED VIEW user_points_leaderboard_mv;

-- Display trigger status
SELECT 'Database triggers created successfully!' as status;
SELECT * FROM check_leaderboard_triggers();

-- Usage examples:
-- To disable triggers during bulk operations:
-- SELECT manage_leaderboard_triggers(false);

-- To re-enable triggers:
-- SELECT manage_leaderboard_triggers(true);

-- To check trigger status:
-- SELECT * FROM check_leaderboard_triggers();

-- To manually refresh the materialized view:
-- REFRESH MATERIALIZED VIEW CONCURRENTLY user_points_leaderboard_mv;

-- Notes:
-- 1. These triggers will automatically refresh the materialized view whenever
--    data that affects leaderboard points is modified
-- 2. The CONCURRENTLY option allows queries to continue during refresh
-- 3. The pg_trigger_depth() check prevents recursive triggers
-- 4. Triggers can be disabled during bulk operations for performance
-- 5. The materialized view must exist before creating these triggers
--    (run create_leaderboard_materialized_view.sql first)