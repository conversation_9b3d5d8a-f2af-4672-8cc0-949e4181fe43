# 🚀 Cloudflare Hyperdrive Setup Guide

This guide will help you set up Cloudflare Hyperdrive to optimize your Supabase PostgreSQL connections in Cloudflare Workers.

## 📋 Prerequisites

1. **Cloudflare Workers account** with Hyperdrive access
2. **Supabase PostgreSQL database** with connection string
3. **Wrangler CLI** installed and authenticated

## 🔧 Step 1: Create Hyperdrive Configuration

### 1.1 Get Your Supabase Connection String

From your Supabase dashboard, get your PostgreSQL connection string:
```
postgresql://postgres:[YOUR-PASSWORD]@[YOUR-PROJECT-REF].supabase.co:5432/postgres
```

### 1.2 Create Hyperdrive Configuration

Run this command to create a Hyperdrive configuration:

```bash
# Create Hyperdrive configuration
wrangler hyperdrive create kwaci-learning-hyperdrive \
  --connection-string="postgresql://postgres:[YOUR-PASSWORD]@[YOUR-PROJECT-REF].supabase.co:5432/postgres"
```

### 1.3 Get the Hyperdrive ID

After creation, you'll get a Hyperdrive ID. Copy this ID.

## 🔧 Step 2: Update wrangler.json

Replace `YOUR_HYPERDRIVE_ID_HERE` in your `wrangler.json` with the actual Hyperdrive ID:

```json
{
  "hyperdrive": [
    {
      "binding": "HYPERDRIVE",
      "id": "your-actual-hyperdrive-id-here"
    }
  ]
}
```

## 🔧 Step 3: Deploy and Test

### 3.1 Deploy to Cloudflare Workers

```bash
bun run deploy:dev
```

### 3.2 Test the Connection

The application will automatically detect and use Hyperdrive when available. You should see logs like:

```
🚀 Hyperdrive binding detected - using Hyperdrive connection
🚀 Using Cloudflare Hyperdrive for database connection
```

## 🎯 Benefits of Hyperdrive

✅ **Connection Pooling** - Hyperdrive manages connection pools automatically  
✅ **Reduced Latency** - Optimized routing to your database  
✅ **Better Performance** - Eliminates cold start connection issues  
✅ **Automatic Scaling** - Handles connection scaling based on load  
✅ **Cost Optimization** - Reduces database connection overhead  

## 🔍 Troubleshooting

### Issue: "Hyperdrive not found"
- Verify the Hyperdrive ID in wrangler.json
- Ensure you're deploying to the correct environment

### Issue: "Connection failed"
- Check your Supabase connection string
- Verify database credentials
- Ensure Supabase allows connections from Cloudflare IPs

### Issue: "Still getting 500 errors"
- Check Cloudflare Workers logs for detailed error messages
- Verify the Hyperdrive configuration is active
- Test the connection health endpoint

## 📊 Monitoring

You can monitor your Hyperdrive performance in the Cloudflare dashboard:
1. Go to Workers & Pages
2. Select your worker
3. Click on "Hyperdrive" tab
4. View connection metrics and performance

## 🎉 Expected Results

After successful setup, you should see:
- ✅ No more 500 Internal Server Errors
- ✅ Consistent 401 responses for unauthenticated requests
- ✅ Faster database query performance
- ✅ Reliable connections even with special characters in quiz IDs

## 🔄 Fallback Behavior

The implementation includes automatic fallback:
1. **Primary**: Use Hyperdrive if available
2. **Fallback**: Use direct DATABASE_URL connection
3. **Build Phase**: Use placeholder connection

This ensures your application works in all environments.
