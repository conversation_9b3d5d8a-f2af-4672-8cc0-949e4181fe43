- [ ] delete auth-test routes after not needed
- [ ] use ai-rag.server instead of ai-rag.simple.server
- [ ] delete test.ai.tsx
- [ ] in learning detail page, learning content:
    - [ ] improve learning material prompt to include a specific terms that user can clicked to learn more
        - [ ] provide proper schema structure so can be rendered properly in the UI
    - [ ] improve learning material prompt to include proper history and unique facts related to the learning material topic
        - [ ] provide proper schema structure so can be rendered properly in the UI
- [ ] create AI service to provide learning plan for topic the user provide
- [ ] review schema file (remove schema that not needed)
- [x] fix bug when changing theme (the browser refreshing on itself before actually applying the theme)
- [ ] create ai service (agent) that will scan text with special format (code, math equation, etc) and add special schema to it, so the UI can render it better with proper component. this will run before the actual save to the database part and should not break current implementation.
- [ ] create ai service (agent) that will provide code example in integrated IDE when the learning material type is code
- [ ] create ai service (agent) that will provide math example in integrated IDE when the learning material type is math
- [ ] in kwaci primer type, knowledge check step, for every question render an input so the user can type their answer then ai will answer it for them
- [ ] in kwaci primer type, want to go deeper into step, render another box below the list to explain briefly each list item and a button to generate learning material based on it. need to update ai service for kwaci primer generation to include the box content and need to update schema so we knew that this learning material are children of this kwaci primer.
- [ ] implement learning path feature
    - [ ] create ai service (agent) that will provide learning path for topic the user provide
    - [ ] the ai service will create learning path with learning content scope is technical and smaller (look for https://roadmap.sh/ that is provide roadmap that is similar to learning path but a more broader scope). the learning path we provide will be more smaller and more techinical.
    - [ ] create schema for learning path
    - [ ] create ui for learning path
- [ ] implement public view for learning material
    - [ ] do we need to create separate schema for it or we can use the same schema that we use for private view
- [x] in quiz generation there are explanation, make sure explanation/feedback isnt just short like "incorrect" but provide another insight to the user that explain what is the correct answer is
- [ ] add quiz question feedback per question item
- [ ] make the result of quiz description to be easier to read and understand
- [ ] make bookmark in quiz detail works
- [ ] provide AI powered evaluation for free text answer type question

- [ ] fix bug when moving to notes tab, the conversations dissapear
- [ ] add "chat about this step" button in each step
- [ ] chat interface should be scoped per learning step without removing current implementation
- [ ] add memory from previous conversation. currently dont have that. maybe using langchain for this (research first)
- [ ] add navigation list that will display all user questions. that will auto scroll to the chat item that contain that user question when its clicked