import { createRe<PERSON><PERSON><PERSON><PERSON> } from "react-router";
import { runWithContext, type CloudflareEnv } from "../app/lib/context.server";
import { initializeMaterializedViewOptimization, shutdownMaterializedViewOptimization } from "../app/lib/startup/materialized-view-setup";
import { initializeRagServices, shutdownRagServices } from "../app/lib/startup/rag-setup";
import { warmupDatabaseConnection } from "../app/lib/db-retry.server";

type Env = CloudflareEnv;

// Import both server bundles - API routes and regular page routes
// @ts-ignore - This will be resolved after build
import * as apiBuild from "../build/server/api_routes/index.js";
// @ts-ignore - This will be resolved after build
import * as rootBuild from "../build/server/root/index.js";

// Combine both bundles to create a complete route manifest
const combinedBuild = {
  // Use the build configuration from the root bundle
  ...rootBuild,
  // Merge routes from both bundles
  routes: {
    ...rootBuild.routes,
    ...apiBuild.routes,
  },
};

// @ts-ignore - This will be resolved after build
const requestHandler = createRequestHandler(combinedBuild, "production");

// Track initialization state
let isInitialized = false;
let ragInitialized = false;
let dbWarmedUp = false;

export default {
  async fetch(request: Request, env: Env, ctx: any) {
    // Debug environment variables at the worker level
    if (request.url.includes('/api/chat') && request.method === 'POST') {
      console.log('WORKER LEVEL DEBUG - Chat API request:', {
        hasOpenRouterKey: !!env.OPENROUTER_API_KEY,
        openRouterKeyLength: env.OPENROUTER_API_KEY ? env.OPENROUTER_API_KEY.length : 0,
        hasVoyageKey: !!env.VOYAGE_API_KEY,
        envKeys: Object.keys(env),
        requestUrl: request.url,
        requestMethod: request.method,
      });
    }

    // Initialize materialized view optimization on first request
    if (!isInitialized) {
      try {
        await runWithContext(
          { env, ctx, request },
          () => initializeMaterializedViewOptimization()
        );
        isInitialized = true;
        console.log('✅ Materialized view optimization system initialized');
      } catch (error) {
        console.error('❌ Failed to initialize materialized view optimization:', error);
        // Continue without optimization rather than failing completely
      }
    }

    // Initialize RAG services on first request
    if (!ragInitialized) {
      try {
        await runWithContext(
          { env, ctx, request },
          () => initializeRagServices(env)
        );
        ragInitialized = true;
        console.log('✅ RAG services initialized');
      } catch (error) {
        console.error('❌ Failed to initialize RAG services:', error);
        // Continue without RAG rather than failing completely
      }
    }

    // Warm up database connection on first request
    if (!dbWarmedUp) {
      try {
        await runWithContext(
          { env, ctx, request },
          () => warmupDatabaseConnection()
        );
        dbWarmedUp = true;
        console.log('✅ Database connection warmed up');
      } catch (error) {
        console.error('❌ Failed to warm up database connection:', error);
        // Continue without warmup rather than failing completely
      }
    }

    // Run the entire request within the AsyncLocalStorage context
    return runWithContext(
      { env, ctx, request },
      () => requestHandler(request, {
        cloudflare: { env, ctx },
      })
    );
  },

  // Optional: Handle worker shutdown (commented out due to type issues)
  // async scheduled(event: any, env: Env, ctx: any) {
  //   // This can be used for periodic maintenance or health checks
  //   ctx.waitUntil(
  //     runWithContext(
  //       { env, ctx, request: new Request('http://localhost') },
  //       async () => {
  //         // Perform periodic health checks or maintenance
  //         console.log('🔄 Scheduled maintenance check for materialized view optimization');
  //       }
  //     )
  //   );
  // },
};
