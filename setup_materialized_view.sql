-- Setup script to create the user_points_leaderboard_mv materialized view
-- Run this script in your database to create the materialized view

-- Drop the materialized view if it exists (for clean setup)
DROP MATERIALIZED VIEW IF EXISTS user_points_leaderboard_mv;

-- Create materialized view for user leaderboard with aggregated points
CREATE MATERIALIZED VIEW user_points_leaderboard_mv AS
WITH user_quiz_points AS (
  SELECT 
    qa."user_id" as user_id,
    COALESCE(SUM(
      CASE 
        WHEN (qa.score->>'percentage')::numeric >= 90 THEN 100
        WHEN (qa.score->>'percentage')::numeric >= 80 THEN 80
        WHEN (qa.score->>'percentage')::numeric >= 70 THEN 60
        WHEN (qa.score->>'percentage')::numeric >= 60 THEN 40
        ELSE 20
      END
    ), 0) as quiz_points
  FROM "quiz_attempt" qa
  WHERE qa."completed_at" IS NOT NULL
    AND qa.score IS NOT NULL
    AND qa.score->>'percentage' IS NOT NULL
  GROUP BY qa."user_id"
),
user_progress_points AS (
  SELECT 
    lp."user_id" as user_id,
    COALESCE(COUNT(*) * 50, 0) as progress_points
  FROM "learning_progress" lp
  WHERE lp."is_completed" = true
  GROUP BY lp."user_id"
),
user_analytics_points AS (
  SELECT 
    lca."user_id" as user_id,
    COALESCE(COUNT(DISTINCT lca."session_id") * 10, 0) as analytics_points
  FROM "learning_content_analytics" lca
  WHERE lca."session_id" IS NOT NULL
  GROUP BY lca."user_id"
),
user_total_points AS (
  SELECT 
    u.id as user_id,
    u.name as user_name,
    u.email as user_email,
    COALESCE(uqp.quiz_points, 0) + 
    COALESCE(upp.progress_points, 0) + 
    COALESCE(uap.analytics_points, 0) as total_points,
    COALESCE(uqp.quiz_points, 0) as quiz_points,
    COALESCE(upp.progress_points, 0) as progress_points,
    COALESCE(uap.analytics_points, 0) as analytics_points
  FROM "user" u
  LEFT JOIN user_quiz_points uqp ON u.id = uqp.user_id
  LEFT JOIN user_progress_points upp ON u.id = upp.user_id
  LEFT JOIN user_analytics_points uap ON u.id = uap.user_id
)
SELECT 
  user_id,
  user_name,
  user_email,
  total_points,
  quiz_points,
  progress_points,
  analytics_points,
  RANK() OVER (ORDER BY total_points DESC) as rank,
  NOW() as last_updated
FROM user_total_points
ORDER BY total_points DESC;

-- Create indexes on the materialized view for better query performance
CREATE INDEX idx_user_points_leaderboard_mv_total_points 
ON user_points_leaderboard_mv (total_points DESC);

CREATE INDEX idx_user_points_leaderboard_mv_user_id 
ON user_points_leaderboard_mv (user_id);

-- Refresh the materialized view to populate it with data
REFRESH MATERIALIZED VIEW user_points_leaderboard_mv;

-- Create a function to refresh the materialized view
CREATE OR REPLACE FUNCTION refresh_leaderboard_mv()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW user_points_leaderboard_mv;
END;
$$ LANGUAGE plpgsql;

SELECT 'Materialized view user_points_leaderboard_mv created successfully!' as status;