import { db } from './app/db/connection.ts';
import { sql } from 'drizzle-orm';

async function runSqlFix() {
  try {
    console.log('Step 1: Refreshing materialized view normally...');
    await db.execute(sql`REFRESH MATERIALIZED VIEW user_points_leaderboard_mv`);
    console.log('✅ Materialized view refreshed successfully');
    
    console.log('\nStep 2: Creating unique index for concurrent refreshes...');
    await db.execute(sql`
      CREATE UNIQUE INDEX IF NOT EXISTS idx_user_points_leaderboard_mv_user_id_unique 
      ON user_points_leaderboard_mv (user_id)
    `);
    console.log('✅ Unique index created successfully');
    
    console.log('\nStep 3: Testing concurrent refresh...');
    await db.execute(sql`REFRESH MATERIALIZED VIEW CONCURRENTLY user_points_leaderboard_mv`);
    console.log('✅ Concurrent refresh test successful');
    
    console.log('\n🎉 Materialized view fix completed successfully!');
    console.log('The quiz attempt insertion should now work without errors.');
    
  } catch (error) {
    console.error('❌ Error executing SQL fix:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      detail: error.detail
    });
    process.exit(1);
  }
}

runSqlFix();