// Points Breakdown Analysis Script
// Analyzes and displays user points breakdown in a nice format
import { db } from './app/db/index';
import { learningContentAnalytics, learningProgress } from './app/db/schema/analytics';
import { learningContent } from './app/db/schema/learning-content';
import { quizAttempt } from './app/db/schema/quiz';
import { desc, eq, and, count, sql } from './app/db/index';
import type { LearningContentAnalytics } from './app/db/schema/analytics';

// ANSI color codes for nice terminal formatting
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  yellow: '\x1b[33m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  red: '\x1b[31m'
};

function formatHeader(text: string): string {
  const line = '═'.repeat(60);
  return `${colors.cyan}${line}\n${colors.bright}${text.toUpperCase()}${colors.reset}\n${colors.cyan}${line}${colors.reset}`;
}

function formatSection(title: string, points: number, average: number, count: number): string {
  return `
${colors.bright}${colors.blue}${title}${colors.reset}
${colors.green}├─ Total Points: ${points} pts${colors.reset}
${colors.yellow}├─ Average per item: ${average} points${colors.reset}
${colors.magenta}└─ Items completed: ${count}${colors.reset}`;
}

function formatSubItem(label: string, value: string | number): string {
  return `${colors.cyan}   • ${label}: ${colors.reset}${value}`;
}

async function analyzePointsBreakdown() {
  console.log(formatHeader('📊 Points Breakdown Analysis'));
  
  try {
    // 1. QUIZ COMPLETIONS ANALYSIS
    console.log(`\n${colors.bright}${colors.blue}🎯 QUIZ COMPLETIONS${colors.reset}`);
    console.log('─'.repeat(40));
    
    // Get completed quiz attempts with scores
    const completedQuizzes = await db
      .select({
        id: quizAttempt.id,
        score: quizAttempt.score,
        completedAt: quizAttempt.completedAt
      })
      .from(quizAttempt)
      .where(eq(quizAttempt.isCompleted, true))
      .orderBy(desc(quizAttempt.completedAt));
    
    let totalQuizPoints = 0;
    let quizCount = completedQuizzes.length;
    const scoreDistribution: Record<string, number> = {
      '90%+': 0,
      '80-89%': 0,
      '70-79%': 0,
      '60-69%': 0,
      '<60%': 0
    };
    
    completedQuizzes.forEach((quiz: any) => {
       if (quiz.score && typeof quiz.score === 'object' && 'percentage' in quiz.score) {
         const percentage = quiz.score.percentage as number;
         
         // Calculate leaderboard points based on percentage (from LEADERBOARD_POINTS_SYSTEM.md)
         let leaderboardPoints = 20; // default
         if (percentage >= 90) {
           leaderboardPoints = 100;
           scoreDistribution['90%+']++;
         } else if (percentage >= 80) {
           leaderboardPoints = 80;
           scoreDistribution['80-89%']++;
         } else if (percentage >= 70) {
           leaderboardPoints = 60;
           scoreDistribution['70-79%']++;
         } else if (percentage >= 60) {
           leaderboardPoints = 40;
           scoreDistribution['60-69%']++;
         } else {
           scoreDistribution['<60%']++;
         }
         
         totalQuizPoints += leaderboardPoints;
       }
     });
    
    const avgQuizPoints = quizCount > 0 ? Math.round(totalQuizPoints / quizCount) : 0;
    
    console.log(formatSection('Quiz Completions', totalQuizPoints, avgQuizPoints, quizCount));
    console.log(formatSubItem('Score Distribution', ''));
    Object.entries(scoreDistribution).forEach(([range, count]) => {
      if (count > 0) {
        console.log(`${colors.cyan}     └─ ${range}: ${colors.reset}${count} quizzes`);
      }
    });
    
    // Show detailed breakdown calculation
    console.log(formatSubItem('Breakdown Calculation', ''));
    const pointsBreakdown: { range: string; count: number; pointsPerQuiz: number; total: number }[] = [];
    
    Object.entries(scoreDistribution).forEach(([range, count]) => {
      if (count > 0) {
        let pointsPerQuiz = 20; // default
        if (range === '90%+') pointsPerQuiz = 100;
        else if (range === '80-89%') pointsPerQuiz = 80;
        else if (range === '70-79%') pointsPerQuiz = 60;
        else if (range === '60-69%') pointsPerQuiz = 40;
        
        const total = count * pointsPerQuiz;
        pointsBreakdown.push({ range, count, pointsPerQuiz, total });
        console.log(`${colors.cyan}     └─ ${count} quiz${count === 1 ? '' : 'es'} with ${range} scores (${pointsPerQuiz} pts each = ${total} pts)${colors.reset}`);
      }
    });
    
    const calculatedTotal = pointsBreakdown.reduce((sum, item) => sum + item.total, 0);
    const breakdownSum = pointsBreakdown.map(item => item.total).join(' + ');
    console.log(`${colors.cyan}     └─ Total: ${breakdownSum} = ${calculatedTotal} pts${colors.reset}`);
    console.log(`${colors.cyan}     └─ Average: ${calculatedTotal} ÷ ${quizCount} = ${(calculatedTotal / quizCount).toFixed(3)} (rounded to ${avgQuizPoints})${colors.reset}`);
    
    // 2. LEARNING PROGRESS ANALYSIS
    console.log(`\n\n${colors.bright}${colors.green}📚 LEARNING PROGRESS${colors.reset}`);
    console.log('─'.repeat(40));
    
    // Calculate dynamic learning points based on level and duration
    const learningPointsData = await db
      .select({
        contentId: learningContent.id,
        title: learningContent.title,
        learningLevel: learningContent.learningLevel,
        estimatedReadingTime: learningContent.estimatedReadingTime,
        basePoints: sql<number>`
          CASE 
            WHEN ${learningContent.learningLevel} = 'beginner' THEN 30
            WHEN ${learningContent.learningLevel} = 'intermediate' THEN 50
            WHEN ${learningContent.learningLevel} = 'advanced' THEN 70
            ELSE 50
          END
        `.as('basePoints'),
        durationBonus: sql<number>`
          CASE 
            WHEN ${learningContent.estimatedReadingTime} >= 15 THEN 15
            WHEN ${learningContent.estimatedReadingTime} >= 10 THEN 10
            WHEN ${learningContent.estimatedReadingTime} >= 5 THEN 5
            ELSE 0
          END
        `.as('durationBonus'),
        points: sql<number>`
          CASE 
            WHEN ${learningContent.learningLevel} = 'beginner' THEN 30
            WHEN ${learningContent.learningLevel} = 'intermediate' THEN 50
            WHEN ${learningContent.learningLevel} = 'advanced' THEN 70
            ELSE 50
          END +
          CASE 
            WHEN ${learningContent.estimatedReadingTime} >= 15 THEN 15
            WHEN ${learningContent.estimatedReadingTime} >= 10 THEN 10
            WHEN ${learningContent.estimatedReadingTime} >= 5 THEN 5
            ELSE 0
          END
        `.as('points')
      })
      .from(learningProgress)
      .innerJoin(learningContent, eq(learningProgress.contentId, learningContent.id))
      .where(eq(learningProgress.isCompleted, true));
    
    const learningCount = learningPointsData.length;
    const totalLearningPoints = learningPointsData.reduce((sum: number, item: any) => sum + item.points, 0);
    const avgLearningPoints = learningCount > 0 ? Math.round(totalLearningPoints / learningCount) : 0;
    
    console.log(formatSection('Learning Progress', totalLearningPoints, avgLearningPoints, learningCount));
    
    // Show detailed duration bonus breakdown
    console.log(formatSubItem('Duration Bonus Breakdown', ''));
    const durationTiers = {
      '15+ min': { bonus: 15, count: 0, items: [] as any[] },
      '10-14 min': { bonus: 10, count: 0, items: [] as any[] },
      '5-9 min': { bonus: 5, count: 0, items: [] as any[] },
      '<5 min': { bonus: 0, count: 0, items: [] as any[] }
    };

    learningPointsData.forEach((item: any) => {
      const duration = item.estimatedReadingTime;
      if (duration >= 15) {
        durationTiers['15+ min'].count++;
        durationTiers['15+ min'].items.push(item);
      } else if (duration >= 10) {
        durationTiers['10-14 min'].count++;
        durationTiers['10-14 min'].items.push(item);
      } else if (duration >= 5) {
        durationTiers['5-9 min'].count++;
        durationTiers['5-9 min'].items.push(item);
      } else {
        durationTiers['<5 min'].count++;
        durationTiers['<5 min'].items.push(item);
      }
    });

    Object.entries(durationTiers).forEach(([tier, data]) => {
      if (data.count > 0) {
        const totalBonus = data.count * data.bonus;
        console.log(`${colors.cyan}     └─ ${tier}: ${data.count} items × ${data.bonus} pts = ${totalBonus} pts${colors.reset}`);
        
        // Show first few items as examples
        const exampleItems = data.items.slice(0, 3);
        exampleItems.forEach((item: any, index: number) => {
          const truncatedTitle = item.title ? (item.title.length > 40 ? item.title.substring(0, 40) + '...' : item.title) : `Content ${item.contentId}`;
          console.log(`${colors.cyan}        ${index + 1}. "${truncatedTitle}" (${item.estimatedReadingTime}min, ${item.learningLevel}) = ${item.basePoints} + ${item.durationBonus} = ${item.points} pts${colors.reset}`);
        });
        
        if (data.items.length > 3) {
          console.log(`${colors.cyan}        ... and ${data.items.length - 3} more items${colors.reset}`);
        }
      }
    });

    // Show breakdown by difficulty level
    const levelBreakdown = learningPointsData.reduce((acc: any, item: any) => {
      const level = item.learningLevel;
      if (!acc[level]) acc[level] = { count: 0, points: 0, basePoints: 0, bonusPoints: 0 };
      acc[level].count++;
      acc[level].points += item.points;
      acc[level].basePoints += item.basePoints;
      acc[level].bonusPoints += item.durationBonus;
      return acc;
    }, {} as Record<string, { count: number; points: number; basePoints: number; bonusPoints: number }>);

    if (Object.keys(levelBreakdown).length > 0) {
      console.log(formatSubItem('Breakdown by Level', ''));
      Object.entries(levelBreakdown).forEach(([level, data]: [string, any]) => {
        const avgPoints = Math.round(data.points / data.count);
        console.log(`${colors.cyan}     ${level}: ${data.count} items, ${data.points} pts (avg: ${avgPoints})${colors.reset}`);
        console.log(`${colors.cyan}       └─ Base: ${data.basePoints} pts + Duration Bonus: ${data.bonusPoints} pts = ${data.points} pts${colors.reset}`);
      });
    }
    
    // Get recent learning completions
    const recentLearning = await db
      .select({
        id: learningProgress.id,
        contentId: learningProgress.contentId,
        completionPercentage: learningProgress.completionPercentage,
        totalTimeSpent: learningProgress.totalTimeSpent,
        updatedAt: learningProgress.updatedAt
      })
      .from(learningProgress)
      .where(eq(learningProgress.isCompleted, true))
      .orderBy(desc(learningProgress.updatedAt))
      .limit(5);
    
    if (recentLearning.length > 0) {
      console.log(formatSubItem('Recent Completions', ''));
      recentLearning.forEach((item: any, index: number) => {
         const timeSpentMin = Math.round((item.totalTimeSpent || 0) / 60);
         console.log(`${colors.cyan}     ${index + 1}. Content ID: ${item.contentId} (${timeSpentMin}min)${colors.reset}`);
       });
    }
    
    // 3. LEARNING SESSIONS ANALYSIS
    console.log(`\n\n${colors.bright}${colors.blue}⚡ LEARNING SESSIONS${colors.reset}`);
    console.log('─'.repeat(40));
    
    // Count unique learning sessions from analytics events
    const uniqueSessions = await db
      .selectDistinct({
        sessionId: learningContentAnalytics.sessionId
      })
      .from(learningContentAnalytics)
      .where(sql`${learningContentAnalytics.sessionId} IS NOT NULL`);
    
    const sessionCount = uniqueSessions.length;
    const totalSessionPoints = sessionCount * 10; // 10 points per unique session
    const fixedSessionPoints = 10; // Fixed at 10 points per session (not an average)
    
    console.log(formatSection('Learning Sessions', totalSessionPoints, fixedSessionPoints, sessionCount));
    
    // Get session activity breakdown
    const sessionActivity = await db
      .select({
        eventType: learningContentAnalytics.eventType,
        count: count()
      })
      .from(learningContentAnalytics)
      .where(sql`${learningContentAnalytics.sessionId} IS NOT NULL`)
      .groupBy(learningContentAnalytics.eventType)
      .orderBy(desc(count()));
    
    if (sessionActivity.length > 0) {
      console.log(formatSubItem('Activity Breakdown', ''));
      sessionActivity.forEach((activity: any) => {
         console.log(`${colors.cyan}     └─ ${activity.eventType}: ${colors.reset}${activity.count} events`);
       });
    }
    
    // 4. TOTAL SUMMARY
    console.log(`\n\n${colors.bright}${colors.green}💰 TOTAL POINTS SUMMARY${colors.reset}`);
    console.log('═'.repeat(40));
    
    const grandTotal = totalQuizPoints + totalLearningPoints + totalSessionPoints;
    
    console.log(`${colors.bright}${colors.green}Total Points: ${grandTotal} pts${colors.reset}`);
    console.log(`${colors.cyan}├─ Quiz Completions: ${totalQuizPoints} pts (${((totalQuizPoints/grandTotal)*100).toFixed(1)}%)${colors.reset}`);
    console.log(`${colors.cyan}├─ Learning Progress: ${totalLearningPoints} pts (${((totalLearningPoints/grandTotal)*100).toFixed(1)}%)${colors.reset}`);
    console.log(`${colors.cyan}└─ Learning Sessions: ${totalSessionPoints} pts (${((totalSessionPoints/grandTotal)*100).toFixed(1)}%)${colors.reset}`);
    
    // 5. DATA SOURCES EXPLANATION
    console.log(`\n\n${colors.bright}${colors.yellow}📋 DATA SOURCES${colors.reset}`);
    console.log('─'.repeat(40));
    console.log(`${colors.cyan}Quiz Points:${colors.reset} quiz_attempt table (isCompleted=true, score.percentage)`);
    console.log(`${colors.cyan}Learning Points:${colors.reset} learning_progress table (isCompleted=true)`);
    console.log(`${colors.cyan}Session Points:${colors.reset} learning_content_analytics table (unique sessionId)`);
    console.log(`\n${colors.yellow}Point Calculation Rules:${colors.reset}`);
    console.log(`${colors.cyan}• Quiz: 100pts (90%+), 80pts (80-89%), 60pts (70-79%), 40pts (60-69%), 20pts (<60%)${colors.reset}`);
    console.log(`${colors.cyan}• Learning: Base points (30/50/70 for beginner/intermediate/advanced) + duration bonus (5pts per 5min, capped at 15pts)${colors.reset}`);
    console.log(`${colors.cyan}• Sessions: 10pts per unique learning session${colors.reset}`);
    
  } catch (error) {
    console.error(`${colors.red}Error analyzing points breakdown:${colors.reset}`, error);
  }
}

analyzePointsBreakdown().then(() => {
  console.log(`\n${colors.green}✅ Analysis completed successfully!${colors.reset}`);
  process.exit(0);
}).catch(error => {
  console.error(`${colors.red}❌ Analysis failed:${colors.reset}`, error);
  process.exit(1);
});