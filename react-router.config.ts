import type { Config } from "@react-router/dev/config";

export default {
  ssr: true,
  future: {
    unstable_viteEnvironmentApi: true,
  },
  serverBuildFile: "index.js",
  serverModuleFormat: "esm",
  // Bundle API routes separately to ensure they're included in server build
  serverBundles: ({ branch }) => {
    const isApiRoute = branch.some(route => route.file.includes('api.'));
    return isApiRoute ? 'api_routes' : 'root';
  },
} satisfies Config;
