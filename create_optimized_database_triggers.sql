-- Optimized Database Triggers for Leaderboard Refresh with Debouncing/Throttling
-- This script creates triggers that use a more efficient approach to refresh the materialized view
-- by implementing debouncing and throttling at the database level

-- Create a table to track refresh requests and timing
CREATE TABLE IF NOT EXISTS materialized_view_refresh_queue (
  id SERIAL PRIMARY KEY,
  view_name VARCHAR(255) NOT NULL,
  requested_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  processed_at TIMESTAMP WITH TIME ZONE,
  status VARCHAR(50) DEFAULT 'pending' -- pending, processing, completed, failed
);

-- Create an index for efficient querying
CREATE INDEX IF NOT EXISTS idx_mv_refresh_queue_status_requested 
  ON materialized_view_refresh_queue(status, requested_at);

-- Function to queue a materialized view refresh (debounced approach)
CREATE OR REPLACE FUNCTION queue_leaderboard_mv_refresh()
R<PERSON><PERSON><PERSON> trigger AS $$
DECLARE
  last_request_time TIMESTAMP WITH TIME ZONE;
  debounce_interval INTERVAL := '5 seconds'; -- Configurable debounce time
BEGIN
  -- Check if there's a recent pending request within the debounce interval
  SELECT requested_at INTO last_request_time
  FROM materialized_view_refresh_queue
  WHERE view_name = 'user_points_leaderboard_mv'
    AND status = 'pending'
    AND requested_at > NOW() - debounce_interval
  ORDER BY requested_at DESC
  LIMIT 1;

  -- Only queue a new request if no recent pending request exists
  IF last_request_time IS NULL THEN
    INSERT INTO materialized_view_refresh_queue (view_name)
    VALUES ('user_points_leaderboard_mv');
    
    -- Notify the application that a refresh is needed
    PERFORM pg_notify('materialized_view_refresh', 'user_points_leaderboard_mv');
  END IF;

  -- Return the appropriate record based on trigger type
  IF TG_OP = 'DELETE' THEN
    RETURN OLD;
  ELSE
    RETURN NEW;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to process queued refresh requests (called by application)
CREATE OR REPLACE FUNCTION process_queued_mv_refreshes()
RETURNS INTEGER AS $$
DECLARE
  refresh_record RECORD;
  processed_count INTEGER := 0;
  throttle_interval INTERVAL := '10 seconds'; -- Minimum time between actual refreshes
  last_refresh_time TIMESTAMP WITH TIME ZONE;
BEGIN
  -- Check when the last successful refresh occurred
  SELECT processed_at INTO last_refresh_time
  FROM materialized_view_refresh_queue
  WHERE view_name = 'user_points_leaderboard_mv'
    AND status = 'completed'
  ORDER BY processed_at DESC
  LIMIT 1;

  -- Only proceed if enough time has passed since last refresh (throttling)
  IF last_refresh_time IS NULL OR last_refresh_time < NOW() - throttle_interval THEN
    -- Get the oldest pending refresh request
    SELECT id INTO refresh_record
    FROM materialized_view_refresh_queue
    WHERE view_name = 'user_points_leaderboard_mv'
      AND status = 'pending'
    ORDER BY requested_at ASC
    LIMIT 1;

    IF refresh_record.id IS NOT NULL THEN
      -- Mark as processing
      UPDATE materialized_view_refresh_queue
      SET status = 'processing', processed_at = NOW()
      WHERE id = refresh_record.id;

      BEGIN
        -- Perform the actual refresh
        REFRESH MATERIALIZED VIEW CONCURRENTLY user_points_leaderboard_mv;
        
        -- Mark as completed
        UPDATE materialized_view_refresh_queue
        SET status = 'completed'
        WHERE id = refresh_record.id;
        
        -- Clean up old completed/failed records (keep last 100)
        DELETE FROM materialized_view_refresh_queue
        WHERE view_name = 'user_points_leaderboard_mv'
          AND status IN ('completed', 'failed')
          AND id NOT IN (
            SELECT id FROM materialized_view_refresh_queue
            WHERE view_name = 'user_points_leaderboard_mv'
              AND status IN ('completed', 'failed')
            ORDER BY processed_at DESC
            LIMIT 100
          );
        
        processed_count := 1;
        
      EXCEPTION WHEN OTHERS THEN
        -- Mark as failed
        UPDATE materialized_view_refresh_queue
        SET status = 'failed'
        WHERE id = refresh_record.id;
        
        -- Re-raise the exception for logging
        RAISE;
      END;
    END IF;
  END IF;

  RETURN processed_count;
END;
$$ LANGUAGE plpgsql;

-- Replace the immediate refresh triggers with queued refresh triggers

-- Trigger for quiz_attempt table
DROP TRIGGER IF EXISTS trigger_refresh_leaderboard_on_quiz_attempt ON quiz_attempt;
CREATE TRIGGER trigger_queue_leaderboard_refresh_on_quiz_attempt
  AFTER INSERT OR UPDATE OR DELETE ON quiz_attempt
  FOR EACH ROW
  WHEN (pg_trigger_depth() = 0) -- Prevent recursive triggers
  EXECUTE FUNCTION queue_leaderboard_mv_refresh();

-- Trigger for learning_progress table
DROP TRIGGER IF EXISTS trigger_refresh_leaderboard_on_progress ON learning_progress;
CREATE TRIGGER trigger_queue_leaderboard_refresh_on_progress
  AFTER INSERT OR UPDATE OR DELETE ON learning_progress
  FOR EACH ROW
  WHEN (pg_trigger_depth() = 0) -- Prevent recursive triggers
  EXECUTE FUNCTION queue_leaderboard_mv_refresh();

-- Trigger for learning_content_analytics table
DROP TRIGGER IF EXISTS trigger_refresh_leaderboard_on_analytics ON learning_content_analytics;
CREATE TRIGGER trigger_queue_leaderboard_refresh_on_analytics
  AFTER INSERT OR UPDATE OR DELETE ON learning_content_analytics
  FOR EACH ROW
  WHEN (pg_trigger_depth() = 0) -- Prevent recursive triggers
  EXECUTE FUNCTION queue_leaderboard_mv_refresh();

-- Trigger for user table
DROP TRIGGER IF EXISTS trigger_refresh_leaderboard_on_user ON "user";
CREATE TRIGGER trigger_queue_leaderboard_refresh_on_user
  AFTER UPDATE ON "user"
  FOR EACH ROW
  WHEN (pg_trigger_depth() = 0 AND (OLD.name IS DISTINCT FROM NEW.name OR OLD.email IS DISTINCT FROM NEW.email))
  EXECUTE FUNCTION queue_leaderboard_mv_refresh();

-- Function to get refresh queue status (for monitoring)
CREATE OR REPLACE FUNCTION get_mv_refresh_queue_status()
RETURNS TABLE(
  pending_count BIGINT,
  processing_count BIGINT,
  last_completed_at TIMESTAMP WITH TIME ZONE,
  last_failed_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) FILTER (WHERE status = 'pending') as pending_count,
    COUNT(*) FILTER (WHERE status = 'processing') as processing_count,
    MAX(processed_at) FILTER (WHERE status = 'completed') as last_completed_at,
    MAX(processed_at) FILTER (WHERE status = 'failed') as last_failed_at
  FROM materialized_view_refresh_queue
  WHERE view_name = 'user_points_leaderboard_mv';
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON materialized_view_refresh_queue TO PUBLIC;
GRANT USAGE, SELECT ON SEQUENCE materialized_view_refresh_queue_id_seq TO PUBLIC;