use this command `curl -s -b /tmp/cookies.txt` before curl to act as a logged in user. or if something happens you need to use this command to login `curl -s -X POST http://localhost:5173/api/auth/sign-in/email -H "Content-Type: application/json" -c cookies.txt -d '{"email": "<EMAIL>", "password": "12341234"}' -w "\nHTTP Status: %{http_code}\n"`
we are using react-router 7. so use this before creating new route or anything related