# Database (Supabase)
DATABASE_URL="postgresql://postgres:[password]@[project-ref].supabase.co:5432/postgres"

# Supabase Configuration
SUPABASE_URL="https://[project-ref].supabase.co"
SUPABASE_ANON_KEY="your-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-supabase-service-role-key"

# Vite Environment Variables (for client-side access)
VITE_SUPABASE_URL="https://[project-ref].supabase.co"
VITE_SUPABASE_ANON_KEY="your-supabase-anon-key"

# Legacy Authentication (to be removed after migration)
# BETTER_AUTH_SECRET="your-secret-key-here"
# BETTER_AUTH_URL="http://localhost:5173"
# BETTER_AUTH_TRUSTED_ORIGINS="http://localhost:5173,http://localhost:3000,http://localhost:3001,https://your-production-domain.com"

# AI Services
OPENROUTER_API_KEY="your-openrouter-key"
LANGCHAIN_API_KEY="your-langchain-key"

# Vector Search (RAG)
VOYAGE_API_KEY="your-voyage-ai-key"
VECTORIZE_INDEX_NAME="learning-content-embeddings"

# Application
NODE_ENV="development"

# Quiz Encryption
QUIZ_ENCRYPTION_KEY="d8e015a2f4b9c6e731849d2k5m7n0p3q"