# Leaderboard Points System Documentation

This document provides a comprehensive overview of how points are calculated and assigned in the leaderboard system.

## Overview

The leaderboard uses a **materialized view** (`user_points_leaderboard_mv`) to efficiently calculate and rank users based on their learning activities. Points are awarded across three main categories:

1. **Quiz Points** - Based on quiz performance
2. **Progress Points** - Based on completed learning content
3. **Analytics Points** - Based on learning session activity

### UI Display vs Backend Storage

**Important Note**: There is a discrepancy between what users see in the UI and how points are stored in the backend:

- **Learning Content**: UI displays dynamic averages calculated from actual performance (Math.round(progressPoints / contentCount)) but backend stores 50 points per completion
- **Quiz Completion**: UI displays dynamic averages calculated from actual performance but backend stores variable points (20-100) based on performance
- **Analytics Sessions**: UI and backend are consistent at 10 points per unique session

This design allows for user-friendly display values while maintaining flexible backend calculations.

### Dynamic Point Calculations in UI

**Learning Content Averages (`avgContentPoints`)**:
- Calculated dynamically as `Math.round(progressPoints / contentCount)` in the UI
- **Updates only when learning content is fully completed** (`input.completed = true`)
- **Does NOT update during step progress saves** - intermediate progress saves do not trigger average recalculation
- The materialized view refresh only occurs on full content completion, ensuring accurate average calculations
- Users see actual averages based on their performance, not fixed values

## Point Categories

### 1. Quiz Points (`quiz_points`)

#### Individual Question Scoring

Each quiz question has its own point value defined in the `points` field of the question object. The scoring process works as follows:

**Question Definition** (from `quiz.questions` JSON array):
```typescript
{
  id: string;
  type: 'multipleChoice' | 'trueFalse' | 'fillInBlank' | 'matching' | 'freeText' | 'ordering' | 'flashcard';
  points: number; // Individual point value for this question
  // ... other question properties
}
```

**Scoring Logic** (from `api.quiz-attempts.complete.tsx`):
- **Correct Answers**: Earn full `question.points` value
- **Incorrect Answers**: Earn 0 points
- **Free Text Questions**: Can earn partial credit (60% of question points) for substantial answers (>10 characters)

**Point Calculation Process**:
1. Each question is evaluated individually
2. Points are accumulated: `earnedPoints += questionPoints`
3. Final percentage: `(earnedPoints / totalPoints) * 100`
4. Quiz total points = sum of all individual question points

#### Leaderboard Quiz Points

The materialized view converts quiz performance percentages into leaderboard points:

```sql
CASE 
  WHEN (qa.score->>'percentage')::numeric >= 90 THEN 100
  WHEN (qa.score->>'percentage')::numeric >= 80 THEN 80
  WHEN (qa.score->>'percentage')::numeric >= 70 THEN 60
  WHEN (qa.score->>'percentage')::numeric >= 60 THEN 40
  ELSE 20
END
```

**Point Tiers**:
- **90%+ score**: 100 leaderboard points
- **80-89% score**: 80 leaderboard points
- **70-79% score**: 60 leaderboard points
- **60-69% score**: 40 leaderboard points
- **<60% score**: 20 leaderboard points

**Quiz Completion Count**: The UI calculates completed quizzes by dividing total quiz points by 60 (average points per quiz) and displays "50 points per completed quiz" for user understanding, though actual points vary by performance tier.

### 2. Progress Points (`progress_points`)

**Backend Storage**: 50 points per completed learning content item
**UI Display**: Dynamic averages calculated from actual performance

```sql
COUNT(*) * 50 as progress_points
FROM "learning_progress" lp
WHERE lp."is_completed" = true
```

**Implementation Details:**
- Progress tracking: `app/routes/api.progress.tsx` (sets `isCompleted: true` when `completed: true`)
- Progress storage: `app/db/services/analytics.ts` (`updateLearningProgress` function)
- Backend calculation: `create_leaderboard_materialized_view.sql` (COUNT(*) * 50 where `is_completed = true`)
- UI display: `PointBreakdown.tsx` (Math.round(progressPoints / contentCount))

**Implementation**: The backend stores 50 points per completion (as defined in `create_leaderboard_materialized_view.sql`), while the UI calculates and displays dynamic averages using `Math.round(progressPoints / contentCount)` in `app/components/dashboard/PointBreakdown.tsx`.

**Criteria**: Learning content is considered completed when `is_completed = true` in the `learning_progress` table.

### 3. Analytics Points (`analytics_points`)

**Calculation**: 10 points per unique learning session

```sql
COUNT(DISTINCT a."session_id") * 10 as analytics_points
FROM "analytics" a
```

**Criteria**: Each unique `session_id` in the analytics table counts as one learning session.

#### Session Management and Point Increment

**What Makes Session Points Increment**:
Session points increment when analytics events with a `sessionId` and `userId` of type 'view' or 'start' are tracked. This happens when:

1. **Learning Content Viewed**: When a user views learning content, a `content_viewed` event is mapped to 'view' type
2. **Quiz Started**: When a user starts a quiz, a `quiz_started` event is mapped to 'start' type
3. **New Session Creation**: Each unique session generates 10 points when first recorded

**Session Lifecycle**:
- **Session Creation**: Generated using `generateSessionId()` and stored in `sessionStorage` under key `kwaci_learning_session_id`
- **Session Persistence**: Sessions persist in browser's `sessionStorage` until one of the following occurs:
  - Browser tab/window is closed
  - Browser application is closed
  - Browser crashes
  - Session is manually cleared

**When Browser Sessions End**:
- **Tab Closure**: Session ends when the specific browser tab is closed
- **Browser Closure**: Session ends when the entire browser application is closed
- **Browser Crash**: Session ends if the browser crashes or is force-quit
- **User Logout**: Sessions are automatically cleared when users log out
- **Note**: Each browser tab maintains its own separate session storage

**Manual Session Clearing**:
Sessions can be manually cleared through several methods:

1. **User Logout**: Sessions are automatically cleared when users log out using `clearSessionId()` function
2. **Programmatic Clearing**: Using `clearSessionId()` function in application code
3. **Browser Developer Tools**: Manually removing `kwaci_learning_session_id` from sessionStorage
4. **Browser Settings**: Clearing site data or session storage through browser settings

**Important Notes**:
- **Logout Behavior**: `clearSessionId()` is called during logout to ensure proper session reset and prevent session carryover
- **Cross-Tab Independence**: Each browser tab maintains its own session, so closing one tab doesn't affect sessions in other tabs
- **Server-Side Tracking**: Session events are tracked server-side in the analytics table for leaderboard point calculation
- **Point Update Timing**: Analytics points update when new unique sessions are recorded, not during session clearing

## Total Points Calculation

The final leaderboard score combines all three categories:

```sql
COALESCE(uqp.quiz_points, 0) + 
COALESCE(upp.progress_points, 0) + 
COALESCE(uap.analytics_points, 0) as total_points
```

## Implementation Details

### Database Schema

**Quiz Questions** (`quiz.questions` JSON):
- Each question has individual `points` value
- Questions can have different point values based on difficulty
- Total quiz points = sum of all question points

**Quiz Attempts** (`quiz_attempt.score` JSON):
```typescript
{
  totalPoints: number;     // Sum of all question points
  earnedPoints: number;    // Sum of points from correct answers
  percentage: number;      // (earnedPoints / totalPoints) * 100
  correctAnswers: number;  // Count of correct answers
  totalQuestions: number;  // Total number of questions
}
```

**Question Results** (`quiz_attempt.questionResults` JSON):
```typescript
[{
  questionId: string;
  isCorrect: boolean;
  pointsEarned: number;    // Individual question points earned
  feedback?: string;
}]
```

### Materialized View Structure

The `user_points_leaderboard_mv` contains:
- `user_id`, `user_name`, `user_email`
- `total_points` (sum of all categories)
- `quiz_points`, `progress_points`, `analytics_points` (individual categories)
- `rank` (calculated via `RANK() OVER (ORDER BY total_points DESC)`)
- `last_updated` (timestamp of last refresh)

### Automatic Refresh Triggers

The materialized view automatically refreshes when:

1. **Quiz Completion** (`api.quiz-attempts.complete.tsx`):
   ```typescript
   await db.execute(`REFRESH MATERIALIZED VIEW CONCURRENTLY user_points_leaderboard_mv;`);
   ```

2. **Learning Progress Completion** (`api.progress.tsx`):
   ```typescript
   if (input.isCompleted) {
     await db.execute(`REFRESH MATERIALIZED VIEW CONCURRENTLY user_points_leaderboard_mv;`);
   }
   ```

3. **New Learning Sessions** (`analytics.ts`):
   - Triggered when new learning sessions are recorded (view/start events with sessionId)
   - Ensures analytics points are updated for new sessions
   - Only refreshes for events that affect session counting
   - Sessions are managed client-side using `sessionStorage` and tracked server-side for points

### Performance Optimizations

- **Materialized View**: Pre-calculated rankings for fast queries
- **Concurrent Refresh**: Non-blocking updates using `REFRESH MATERIALIZED VIEW CONCURRENTLY`
- **Indexes**: Optimized for common query patterns
  - `idx_user_points_leaderboard_mv_total_points` (for ranking)
  - `idx_user_points_leaderboard_mv_user_id` (for user lookups)

## Question Type Scoring Details

### Objective Question Types

**Multiple Choice**:
- Correct answer index match → Full points
- Incorrect → 0 points

**True/False**:
- Exact boolean match → Full points
- Incorrect → 0 points

**Fill in the Blank**:
- Answer matches any acceptable answer (case-insensitive) → Full points
- No match → 0 points

**Matching**:
- All pairs correctly matched → Full points
- Any incorrect pair → 0 points

**Ordering**:
- Exact sequence match → Full points
- Any incorrect order → 0 points

**Flashcard**:
- User indicates "correct" → Full points
- User indicates "incorrect" → 0 points

### Subjective Question Types

**Free Text**:
- Answer length > 10 characters → 60% of question points
- Answer length ≤ 10 characters → 0 points
- *Note: Future AI integration planned for better evaluation*

## Example Scenarios

### Scenario 1: Mixed Difficulty Quiz

**Quiz Setup**:
- Question 1 (Easy): 5 points
- Question 2 (Medium): 10 points  
- Question 3 (Hard): 15 points
- Total: 30 points

**User Performance**:
- Question 1: Correct → 5 points
- Question 2: Incorrect → 0 points
- Question 3: Correct → 15 points
- Earned: 20 points
- Percentage: 66.7%

**Leaderboard Points**: 40 points (60-69% tier)

### Scenario 2: Complete Learning Journey

**User Activity**:
- Completed 3 learning content items → 150 progress points (stored as 150, displayed as 3 × 25 = 75 points)
- Completed 2 quizzes with 85% average → 160 quiz points (displayed as 2 × 50 = 100 points for user understanding)
- Had 5 unique learning sessions → 50 analytics points
- **Total**: 360 leaderboard points (actual stored total)

**UI Display Calculation**:
- Learning Progress: 15 completions × 25 points = 375 points (displayed)
- Quiz Completions: 12 completions × 50 points = 600 points (displayed)
- Analytics Sessions: Variable based on unique sessions × 10 points

**Note**: The UI shows user-friendly point values (25 for learning content, 50 for quiz completion) while the backend stores different values for calculation efficiency.

## Files and Components

### Core Files
- `create_leaderboard_materialized_view.sql` - Materialized view definition
- `app/db/schema/quiz.ts` - Quiz and attempt schemas
- `app/routes/api.quiz-attempts.complete.tsx` - Quiz scoring logic
- `app/routes/api.progress.tsx` - Progress tracking
- `app/routes/api.leaderboard.tsx` - Leaderboard API

### UI Components
- `app/components/progress/leaderboard.tsx` - Leaderboard display
- `app/components/quiz/QuizResults.tsx` - Quiz results with points
- `app/routes/dashboard.progress.tsx` - Progress dashboard

This system ensures fair point distribution across different learning activities while maintaining high performance through the materialized view approach.