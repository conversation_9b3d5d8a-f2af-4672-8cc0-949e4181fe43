---
type: "manual"
---

test the UI interface with:
1. bun run build
2. bunx wrangler dev --env develop --experimental-vectorize-bind-to-prod

this is faster because we run locally and dont need to upload to worker. repeat this cycle if you change something in the code

login using
<EMAIL>
12341234

bunx wrangler dev --local=false --env develop scripts/test-rag-worker.ts --experimental-vectorize-bind-to-prod