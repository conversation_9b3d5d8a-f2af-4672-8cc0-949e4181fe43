# Duplicate View Name Error - Solution

## Problem
When running `bunx drizzle-kit generate`, the following error occurred:
```
Warning: We've found duplicated view name across public schema. Please rename your view
```

## Root Cause
The error was caused by a conflict between:
1. A manually created materialized view `user_points_leaderboard_mv` in the database
2. A Drizzle schema definition using `pgMaterializedView()` for the same view

Dr<PERSON><PERSON> was detecting the existing materialized view in the database and conflicting with the schema definition, even when using `.existing()` configuration.

## Solution
Changed the schema definition in `app/db/schema/leaderboard.ts` from:
```typescript
// BEFORE (causing conflict)
export const userPointsLeaderboardMV = pgMaterializedView('user_points_leaderboard_mv', {
  // ... columns
}).existing();
```

To:
```typescript
// AFTER (resolved conflict)
export const userPointsLeaderboardMV = pgTable('user_points_leaderboard_mv', {
  // ... columns
});
```

## Key Changes
1. **Changed from `pgMaterializedView` to `pgTable`**: This tells <PERSON><PERSON><PERSON> to treat the materialized view as a regular table for schema purposes
2. **Removed `.existing()` configuration**: No longer needed since we're not using `pgMaterializedView`
3. **Maintained all column definitions**: The TypeScript types and query functionality remain unchanged

## Benefits
- ✅ `drizzle-kit generate` now works without errors
- ✅ `drizzle-kit push` works without conflicts
- ✅ All existing queries and TypeScript types continue to work
- ✅ The materialized view remains managed outside of Drizzle migrations (as intended)
- ✅ No impact on application functionality

## Additional Fix
Also successfully added the missing `bookmarked_questions` column to the `quiz_progress` table:
```sql
ALTER TABLE quiz_progress ADD COLUMN bookmarked_questions json DEFAULT '[]'::json;
```

## Verification
- `bunx drizzle-kit generate` now shows "No schema changes, nothing to migrate 😴"
- All 30 tables are properly recognized including `user_points_leaderboard_mv`
- No duplicate view name warnings
- Database schema is in sync with Drizzle definitions

## Notes
- The materialized view continues to be managed via manual SQL scripts as intended
- This approach allows Drizzle to work with the view without trying to manage its lifecycle
- The solution maintains the performance benefits of the materialized view while resolving the migration conflicts