#!/usr/bin/env tsx

/**
 * Production Content Reindexing Script
 *
 * This script provides comprehensive content reindexing capabilities for production use.
 * It can reindex individual content items or batch reindex all content.
 */

import { createDatabaseConnectionFromEnv } from '../app/db/connection';
import { learningContent } from '../app/db/schema';
import { eq } from 'drizzle-orm';
import { log } from '../app/lib/logger';

interface ReindexOptions {
  contentId?: string;
  workerUrl?: string;
  force?: boolean;
  batchSize?: number;
  production?: boolean;
}

/**
 * Reindex content in production or development
 */
async function reindexContent(options: ReindexOptions = {}) {
  const {
    contentId,
    workerUrl = options.production ? 'https://your-worker.your-subdomain.workers.dev' : 'http://localhost:8787',
    force = true,
    batchSize = 10,
    production = false
  } = options;
  
  console.log('🔄 Re-indexing content with fixed chunking service');
  console.log('='.repeat(60));
  console.log(`Content ID: ${contentId}`);
  console.log(`Worker URL: ${workerUrl}`);
  console.log(`Force Re-index: ${force}`);
  
  try {
    if (!contentId) {
      console.log('❌ Content ID is required');
      return;
    }
    
    // Step 1: Verify content exists in database
    console.log('\n📋 Step 1: Verifying content in database...');
    const { db } = createDatabaseConnectionFromEnv();
    
    const content = await db
      .select()
      .from(learningContent)
      .where(eq(learningContent.id, contentId))
      .limit(1);
    
    if (!content.length) {
      console.log('❌ Content not found in database');
      return;
    }
    
    const contentItem = content[0];
    console.log(`✅ Found content: "${contentItem.title}"`);
    console.log(`   Steps: ${contentItem.steps.length}`);
    console.log(`   Learning Level: ${contentItem.learningLevel}`);
    
    // Step 2: Check worker health
    console.log('\n🏥 Step 2: Checking worker health...');
    try {
      const healthResponse = await fetch(`${workerUrl}/health`);
      const healthData = await healthResponse.json();
      
      if (healthData.status === 'healthy') {
        console.log('✅ Worker is healthy');
        console.log(`   Vectorize Binding: ${healthData.vectorizeBinding ? '✅' : '❌'}`);
        console.log(`   Voyage API Key: ${healthData.voyageApiKey ? '✅' : '❌'}`);
      } else {
        console.log('❌ Worker is not healthy');
        return;
      }
    } catch (error) {
      console.log('❌ Cannot connect to worker:', error instanceof Error ? error.message : String(error));
      console.log('💡 Make sure the test RAG worker is running:');
      console.log('   bun run test-rag-worker');
      return;
    }
    
    // Step 3: Re-index the content
    console.log('\n🔧 Step 3: Re-indexing content...');
    console.log('This will use the fixed chunking service to properly extract text.');
    
    try {
      const indexResponse = await fetch(`${workerUrl}/test-index?content_id=${contentId}&limit=1`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });
      
      const indexData = await indexResponse.json();
      
      if (indexData.success) {
        console.log('✅ Content re-indexing completed');
        console.log(`   Total Processed: ${indexData.stats?.totalProcessed || 0}`);
        console.log(`   Successful: ${indexData.stats?.successful || 0}`);
        console.log(`   Failed: ${indexData.stats?.failed || 0}`);
        console.log(`   Processing Time: ${indexData.stats?.processingTimeMs || 'N/A'}ms`);
        
        if (indexData.results && indexData.results.length > 0) {
          const result = indexData.results[0];
          console.log(`   Chunks Indexed: ${result.chunksIndexed || 0}`);
          console.log(`   Status: ${result.status}`);
        }
      } else {
        console.log('❌ Content re-indexing failed');
        console.log(`   Error: ${indexData.error}`);
        return;
      }
    } catch (error) {
      console.log('❌ Re-indexing request failed:', error instanceof Error ? error.message : String(error));
      return;
    }
    
    // Step 4: Test the fix with a search
    console.log('\n🔍 Step 4: Testing search with re-indexed content...');
    
    try {
      const searchResponse = await fetch(`${workerUrl}/test-search`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          query: 'dark matter',
          contentId: contentId,
          limit: 5
        }),
      });
      
      const searchData = await searchResponse.json();
      
      if (searchData.success) {
        console.log('✅ Search test completed');
        console.log(`   Results Found: ${searchData.stats?.resultsCount || 0}`);
        console.log(`   Processing Time: ${searchData.stats?.processingTimeMs || 'N/A'}ms`);
        
        if (searchData.results && searchData.results.length > 0) {
          console.log('\n📋 Sample Results:');
          searchData.results.slice(0, 3).forEach((result: any, index: number) => {
            console.log(`\n   ${index + 1}. Score: ${result.score?.toFixed(3) || 'N/A'}`);
            console.log(`      Content: ${result.chunkText?.substring(0, 100) || 'N/A'}...`);
            console.log(`      Source: ${result.contentId || 'N/A'}`);
            
            // Check if content is still JSON/metadata
            const content = result.chunkText || '';
            if (content.includes('{"data":') || content.includes('"type":')) {
              console.log(`      ⚠️  WARNING: Still contains JSON/metadata`);
            } else {
              console.log(`      ✅ Contains readable text`);
            }
          });
        }
      } else {
        console.log('❌ Search test failed');
        console.log(`   Error: ${searchData.error}`);
      }
    } catch (error) {
      console.log('❌ Search test request failed:', error instanceof Error ? error.message : String(error));
    }
    
    // Step 5: Test chat functionality
    console.log('\n💬 Step 5: Testing chat with re-indexed content...');
    
    try {
      const chatResponse = await fetch(`${workerUrl}/test-rag-chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: 'What is dark matter?',
          contentId: contentId,
          maxResults: 3
        }),
      });
      
      const chatData = await chatResponse.json();
      
      if (chatData.success) {
        console.log('✅ Chat test completed');
        console.log(`   Sources Found: ${chatData.stats?.sourcesFound || 0}`);
        console.log(`   Context Length: ${chatData.stats?.contextLength || 0}`);
        console.log(`   Processing Time: ${chatData.stats?.processingTimeMs || 'N/A'}ms`);
        
        const response = chatData.response;
        if (response && response.sources && response.sources.length > 0) {
          console.log('\n📚 Chat Sources:');
          response.sources.slice(0, 2).forEach((source: any, index: number) => {
            console.log(`\n   ${index + 1}. Title: ${source.title || 'N/A'}`);
            console.log(`      Content: ${source.chunkText?.substring(0, 100) || 'N/A'}...`);
            
            // Check if content is readable
            const content = source.chunkText || '';
            if (content.includes('{"data":') || content.includes('"type":')) {
              console.log(`      ⚠️  Still contains JSON/metadata - may need manual re-indexing`);
            } else {
              console.log(`      ✅ Contains readable text - fix successful!`);
            }
          });
        }
      } else {
        console.log('❌ Chat test failed');
        console.log(`   Error: ${chatData.error}`);
      }
    } catch (error) {
      console.log('❌ Chat test request failed:', error instanceof Error ? error.message : String(error));
    }
    
    console.log('\n🎯 Summary:');
    console.log('='.repeat(40));
    console.log('✅ Content re-indexing process completed');
    console.log('✅ Fixed chunking service has been applied');
    console.log('');
    console.log('If results still show JSON/metadata content:');
    console.log('1. The Vectorize index may need time to update');
    console.log('2. Try running the re-indexing again');
    console.log('3. Check that the chunking service fix is properly deployed');
    
  } catch (error) {
    console.error('❌ Re-indexing failed:', error);
  }
}

/**
 * Batch reindex all content in production
 */
async function batchReindexAllContent(options: ReindexOptions = {}) {
  const { batchSize = 10, production = false } = options;

  try {
    log.info('🚀 Starting batch reindexing of all content');

    // Get all content from database
    const { db } = createDatabaseConnectionFromEnv();
    const allContent = await db.select({
      id: learningContent.id,
      title: learningContent.title,
      createdAt: learningContent.createdAt
    }).from(learningContent);

    log.info(`📊 Found ${allContent.length} content items to reindex`);

    let successCount = 0;
    let failureCount = 0;

    // Process in batches
    for (let i = 0; i < allContent.length; i += batchSize) {
      const batch = allContent.slice(i, i + batchSize);
      log.info(`🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(allContent.length / batchSize)}`);

      // Process batch items in parallel
      const batchPromises = batch.map(async (content: any) => {
        try {
          await reindexContent({
            contentId: content.id,
            production,
            force: true
          });
          successCount++;
          log.success(`✅ Reindexed: ${content.title}`);
        } catch (error) {
          failureCount++;
          log.error(`❌ Failed to reindex: ${content.title}`, error);
        }
      });

      await Promise.all(batchPromises);

      // Add delay between batches to avoid overwhelming the system
      if (i + batchSize < allContent.length) {
        log.info('⏳ Waiting 2 seconds before next batch...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    log.success('🎉 Batch reindexing completed', {
      total: allContent.length,
      successful: successCount,
      failed: failureCount
    });

  } catch (error) {
    log.error('❌ Batch reindexing failed:', error);
  }
}

// CLI interface
if (require.main === module) {
  const args = process.argv.slice(2);
  const options: ReindexOptions = {};

  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--content-id':
        options.contentId = args[++i];
        break;
      case '--production':
        options.production = true;
        break;
      case '--batch-size':
        options.batchSize = parseInt(args[++i]);
        break;
      case '--batch-all':
        // Run batch reindexing
        batchReindexAllContent(options).catch(console.error);
        process.exit(0);
        break;
      case '--help':
        console.log(`
Usage: bun run scripts/reindex-content.ts [options]

Options:
  --content-id <id>     Reindex specific content ID
  --production          Use production worker URL
  --batch-size <num>    Batch size for batch operations (default: 10)
  --batch-all           Reindex all content in batches
  --help               Show this help

Examples:
  # Reindex specific content locally
  bun run scripts/reindex-content.ts --content-id "abc-123"

  # Reindex specific content in production
  bun run scripts/reindex-content.ts --content-id "abc-123" --production

  # Batch reindex all content locally
  bun run scripts/reindex-content.ts --batch-all

  # Batch reindex all content in production
  bun run scripts/reindex-content.ts --batch-all --production --batch-size 5
        `);
        process.exit(0);
        break;
    }
  }

  // Default: reindex single content
  reindexContent(options).catch(console.error);
}
