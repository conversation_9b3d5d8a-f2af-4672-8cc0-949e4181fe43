#!/usr/bin/env tsx

/**
 * Test script to verify RAG environment configuration and basic functionality
 * 
 * Usage:
 *   bun run scripts/test-rag-health.ts
 */

import { checkRagHealth, initializeRagServices } from '../app/lib/startup/rag-setup';
import { log } from '../app/lib/logger';

async function testRagHealth() {
  console.log('🔍 Testing RAG Health Configuration...');
  console.log('=' .repeat(50));

  try {
    // 1. Check current health status
    console.log('\n1. Checking current RAG health status...');
    const healthStatus = await checkRagHealth();
    
    console.log('Health Status:', {
      isHealthy: healthStatus.isHealthy,
      services: healthStatus.services,
      configuration: healthStatus.configuration,
      errors: healthStatus.errors
    });

    // 2. Try to initialize services if not healthy
    if (!healthStatus.isHealthy) {
      console.log('\n2. Attempting to initialize RAG services...');
      
      try {
        const services = await initializeRagServices({
          VOYAGE_API_KEY: process.env.VOYAGE_API_KEY,
          VECTORIZE_INDEX_NAME: process.env.VECTORIZE_INDEX_NAME
        });
        
        console.log('✅ RAG services initialized successfully!');
        console.log('Services:', {
          embeddingService: !!services.embeddingService,
          vectorIndexingService: !!services.vectorIndexingService,
          ragSearchService: !!services.ragSearchService
        });
        
        // 3. Re-check health after initialization
        console.log('\n3. Re-checking health after initialization...');
        const newHealthStatus = await checkRagHealth();
        console.log('New Health Status:', {
          isHealthy: newHealthStatus.isHealthy,
          services: newHealthStatus.services,
          configuration: newHealthStatus.configuration,
          errors: newHealthStatus.errors
        });
        
      } catch (initError) {
        console.error('❌ Failed to initialize RAG services:', initError);
      }
    } else {
      console.log('✅ RAG services are already healthy!');
    }

    // 4. Test environment variables
    console.log('\n4. Environment Variables Check:');
    console.log('VOYAGE_API_KEY:', process.env.VOYAGE_API_KEY ? '✅ Set' : '❌ Missing');
    console.log('VECTORIZE_INDEX_NAME:', process.env.VECTORIZE_INDEX_NAME ? '✅ Set' : '❌ Missing');
    console.log('OPENROUTER_API_KEY:', process.env.OPENROUTER_API_KEY ? '✅ Set' : '❌ Missing');

  } catch (error) {
    console.error('❌ RAG health test failed:', error);
    process.exit(1);
  }
}

// Run the test
testRagHealth().then(() => {
  console.log('\n🎉 RAG health test completed!');
}).catch((error) => {
  console.error('💥 RAG health test failed:', error);
  process.exit(1);
});