#!/usr/bin/env node

/**
 * Test script for auth integration
 * 
 * This script tests both local and remote authentication modes
 * to ensure the integration is working correctly.
 */

const { spawn } = require('child_process');
const fetch = require('node-fetch');

const MAIN_APP_URL = 'http://localhost:5173';
const AUTH_WORKER_URL = 'http://localhost:8787';

async function checkService(url, name) {
  try {
    const response = await fetch(url);
    if (response.ok) {
      console.log(`✅ ${name} is running at ${url}`);
      return true;
    } else {
      console.log(`❌ ${name} returned ${response.status} at ${url}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${name} is not accessible at ${url}: ${error.message}`);
    return false;
  }
}

async function testAuthWorker() {
  console.log('\n🔍 Testing Auth Worker...');
  
  // Check health
  const healthOk = await checkService(`${AUTH_WORKER_URL}/health`, 'Auth Worker Health');
  if (!healthOk) return false;

  // Check auth debug endpoint
  try {
    const response = await fetch(`${AUTH_WORKER_URL}/debug/auth`);
    const data = await response.json();
    
    if (data.status === 'ok' && data.hasHandler) {
      console.log('✅ Auth Worker auth instance is working');
    } else {
      console.log('❌ Auth Worker auth instance has issues:', data);
      return false;
    }
  } catch (error) {
    console.log('❌ Auth Worker debug endpoint failed:', error.message);
    return false;
  }

  // Test session endpoint
  try {
    const response = await fetch(`${AUTH_WORKER_URL}/api/auth/get-session`);
    // 401 is expected for no session
    if (response.status === 401 || response.status === 200) {
      console.log('✅ Auth Worker session endpoint is working');
    } else {
      console.log(`❌ Auth Worker session endpoint returned ${response.status}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Auth Worker session endpoint failed:', error.message);
    return false;
  }

  return true;
}

async function testMainApp() {
  console.log('\n🔍 Testing Main App...');
  
  const mainAppOk = await checkService(MAIN_APP_URL, 'Main App');
  return mainAppOk;
}

async function runTests() {
  console.log('🚀 Starting Auth Integration Tests');
  console.log('=====================================');

  // Test auth worker
  const authWorkerOk = await testAuthWorker();
  
  // Test main app
  const mainAppOk = await testMainApp();

  console.log('\n📊 Test Results:');
  console.log('=====================================');
  console.log(`Auth Worker: ${authWorkerOk ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Main App: ${mainAppOk ? '✅ PASS' : '❌ FAIL'}`);

  if (authWorkerOk && mainAppOk) {
    console.log('\n🎉 All tests passed! Integration is working correctly.');
    console.log('\n📋 Next steps:');
    console.log('1. Set USE_AUTH_WORKER=true in your .env file');
    console.log('2. Restart your main application');
    console.log('3. Test authentication features in your app');
    console.log('4. Check logs for "[Better Auth] Using remote auth worker"');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the services and try again.');
    console.log('\n🔧 Troubleshooting:');
    
    if (!authWorkerOk) {
      console.log('- Start auth worker: cd auth-worker && bun run dev');
      console.log('- Check auth worker logs for errors');
      console.log('- Ensure D1 database is migrated: bun run db:migrate:dev');
    }
    
    if (!mainAppOk) {
      console.log('- Start main app: bun run dev');
      console.log('- Check main app logs for errors');
      console.log('- Ensure database is accessible');
    }
  }

  process.exit(authWorkerOk && mainAppOk ? 0 : 1);
}

// Handle command line arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log('Auth Integration Test Script');
  console.log('');
  console.log('Usage:');
  console.log('  node scripts/test-auth-integration.js');
  console.log('');
  console.log('This script tests both the auth worker and main app to ensure');
  console.log('the integration is working correctly.');
  console.log('');
  console.log('Prerequisites:');
  console.log('1. Auth worker running at http://localhost:8787');
  console.log('2. Main app running at http://localhost:5173');
  console.log('');
  console.log('To start both services:');
  console.log('  bun run dev:with-auth');
  process.exit(0);
}

// Run the tests
runTests().catch(error => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});
