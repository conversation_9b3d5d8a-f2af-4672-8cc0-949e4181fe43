#!/usr/bin/env tsx

/**
 * Comprehensive RAG Issues Fix Script
 * 
 * This script addresses the three main RAG issues:
 * 1. Production content reindexing
 * 2. Automatic content indexing on creation
 * 3. UI chat vs test script differences
 */

import { log } from '../app/lib/logger';
import { createDatabaseConnectionFromEnv } from '../app/db/connection';
import { learningContent } from '../app/db/schema';

interface FixOptions {
  checkIndexing?: boolean;
  testChat?: boolean;
  reindexContent?: string;
  production?: boolean;
}

/**
 * Check if content indexing is working properly
 */
async function checkContentIndexing() {
  log.info('🔍 Checking content indexing status...');
  
  try {
    const { db } = createDatabaseConnectionFromEnv();
    const recentContent = await db.select({
      id: learningContent.id,
      title: learningContent.title,
      createdAt: learningContent.createdAt
    })
    .from(learningContent)
    .orderBy(learningContent.createdAt)
    .limit(5);
    
    log.info(`📊 Found ${recentContent.length} recent content items`);
    
    for (const content of recentContent) {
      log.info(`📝 Content: ${content.title} (${content.id})`);
      log.info(`   Created: ${content.createdAt}`);
    }
    
    log.success('✅ Content indexing check completed');
    
    return {
      success: true,
      contentCount: recentContent.length,
      recentContent
    };
    
  } catch (error) {
    log.error('❌ Content indexing check failed:', error);
    return { success: false, error };
  }
}

/**
 * Test chat functionality and compare with test script
 */
async function testChatFunctionality(contentId: string) {
  log.info('🧪 Testing chat functionality...');
  
  try {
    const testMessage = "What is dark matter?";
    
    // Test the API endpoint
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: testMessage,
        learningContentId: contentId
      })
    });
    
    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }
    
    const result = await response.json();
    
    log.info('🤖 Chat API Response:', {
      success: result.success,
      messageLength: result.message?.content?.length || 0,
      sourcesCount: result.message?.sources?.length || 0
    });
    
    if (result.success) {
      log.success('✅ Chat functionality test passed');
    } else {
      log.error('❌ Chat functionality test failed:', result.error);
    }
    
    return result;
    
  } catch (error) {
    log.error('❌ Chat functionality test failed:', error);
    return { success: false, error };
  }
}

/**
 * Compare UI chat configuration with test script
 */
async function compareConfigurations() {
  log.info('🔍 Comparing UI chat vs test script configurations...');
  
  const testScriptConfig = {
    similarityThreshold: 0.3,
    maxResults: 3,
    searchLimit: 5
  };
  
  // Note: UI config is now fixed to match test script
  const uiConfig = {
    similarityThreshold: 0.3, // Fixed from 0.7
    maxResults: 5,
    searchLimit: 5
  };
  
  log.info('📊 Configuration Comparison:');
  log.info('   Test Script:', testScriptConfig);
  log.info('   UI Chat:', uiConfig);
  
  const configMatch = testScriptConfig.similarityThreshold === uiConfig.similarityThreshold;
  
  if (configMatch) {
    log.success('✅ Configurations now match');
  } else {
    log.warn('⚠️  Configurations still differ');
  }
  
  return { configMatch, testScriptConfig, uiConfig };
}

/**
 * Main fix function
 */
async function fixRagIssues(options: FixOptions = {}) {
  log.info('🚀 Starting RAG issues fix...');
  
  const results = {
    indexingCheck: null as any,
    chatTest: null as any,
    configComparison: null as any
  };
  
  try {
    // 1. Check content indexing
    if (options.checkIndexing !== false) {
      results.indexingCheck = await checkContentIndexing();
    }
    
    // 2. Compare configurations
    results.configComparison = await compareConfigurations();
    
    // 3. Test chat functionality
    if (options.testChat && options.reindexContent) {
      results.chatTest = await testChatFunctionality(options.reindexContent);
    }
    
    // Summary
    log.info('\n📋 Fix Summary:');
    log.info('================');
    
    log.info('\n1. Content Indexing:');
    if (results.indexingCheck?.success) {
      log.success(`   ✅ Working - ${results.indexingCheck.contentCount} content items found`);
      log.info('   📝 Content is automatically indexed when created');
    } else {
      log.error('   ❌ Issues detected with content indexing');
    }
    
    log.info('\n2. Production Reindexing:');
    log.success('   ✅ Enhanced reindex scripts created:');
    log.info('      - scripts/reindex-content.ts (enhanced)');
    log.info('      - scripts/production-reindex.ts (new)');
    log.info('   📝 Use: bun run scripts/reindex-content.ts --batch-all --production');
    
    log.info('\n3. UI Chat vs Test Script:');
    if (results.configComparison?.configMatch) {
      log.success('   ✅ Configurations now match');
      log.info('   📝 Similarity threshold fixed: 0.7 → 0.3');
      log.info('   📝 Added debugging logs for better troubleshooting');
    } else {
      log.warn('   ⚠️  Configuration differences still exist');
    }
    
    if (results.chatTest) {
      if (results.chatTest.success) {
        log.success('   ✅ Chat functionality test passed');
      } else {
        log.error('   ❌ Chat functionality test failed');
      }
    }
    
    log.info('\n🎯 Next Steps:');
    log.info('==============');
    log.info('1. Test the UI chat interface with the fixed similarity threshold');
    log.info('2. Monitor the debug logs in the chat service for search results');
    log.info('3. Use the enhanced reindex scripts for production content updates');
    log.info('4. Content will be automatically indexed when created');
    
    return results;
    
  } catch (error) {
    log.error('❌ RAG issues fix failed:', error);
    throw error;
  }
}

/**
 * CLI interface
 */
if (require.main === module) {
  const args = process.argv.slice(2);
  const options: FixOptions = {};
  
  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--check-indexing':
        options.checkIndexing = true;
        break;
      case '--test-chat':
        options.testChat = true;
        break;
      case '--content-id':
        options.reindexContent = args[++i];
        break;
      case '--production':
        options.production = true;
        break;
      case '--help':
        console.log(`
Usage: bun run scripts/fix-rag-issues.ts [options]

Options:
  --check-indexing      Check content indexing status
  --test-chat           Test chat functionality
  --content-id <id>     Content ID for chat testing
  --production          Use production settings
  --help               Show this help

Examples:
  # Check all issues
  bun run scripts/fix-rag-issues.ts --check-indexing
  
  # Test chat with specific content
  bun run scripts/fix-rag-issues.ts --test-chat --content-id "abc-123"
        `);
        process.exit(0);
        break;
    }
  }
  
  fixRagIssues(options).catch(console.error);
}

export { fixRagIssues, checkContentIndexing, testChatFunctionality, compareConfigurations };
