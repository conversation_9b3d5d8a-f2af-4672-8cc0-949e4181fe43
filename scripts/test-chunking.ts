#!/usr/bin/env tsx

/**
 * Test script to verify chunking service works with actual content
 */

import { createDatabaseConnection } from '../app/db/connection';
import { learningContent } from '../app/db/schema';
import { eq } from 'drizzle-orm';
import { LearningContentChunkingService } from '../app/lib/rag/services/chunking-service';

async function testChunking() {
  const contentId = 'b98195e2-7bfe-4ce5-8158-0fcdf0f74b20';
  
  console.log('🧪 Testing chunking service with content:', contentId);
  
  try {
    const { db } = createDatabaseConnection({
      connectionString: process.env.DATABASE_URL!
    });
    
    // Get the specific content
    const content = await db
      .select()
      .from(learningContent)
      .where(eq(learningContent.id, contentId))
      .limit(1);
    
    if (!content.length) {
      console.log('❌ No content found with ID:', contentId);
      return;
    }
    
    const contentItem = content[0];
    
    console.log('\n📋 Content Overview:');
    console.log(`Title: ${contentItem.title}`);
    console.log(`Steps: ${contentItem.steps.length}`);
    
    // Transform database content to RAG LearningContent format
    const ragContent = {
      id: contentItem.id,
      title: contentItem.title,
      description: contentItem.description,
      steps: contentItem.steps.map(step => ({
        id: step.id,
        title: step.title,
        icon: step.icon || '', // Ensure icon is always a string
        blocks: step.blocks
      })),
      learningLevel: contentItem.learningLevel,
      estimatedReadingTime: contentItem.estimatedReadingTime,
      isPublic: contentItem.isPublic,
      tags: contentItem.tags || [],
      userId: contentItem.userId,
      createdAt: contentItem.createdAt,
      updatedAt: contentItem.updatedAt,
      aiMetadata: contentItem.aiMetadata || undefined
    };
    
    // Initialize chunking service
    const chunkingService = new LearningContentChunkingService();
    
    console.log('\n🔧 Testing chunking...');
    const chunks = await chunkingService.chunkContent(ragContent);
    
    console.log('\n📊 Chunking Results:');
    console.log('='.repeat(50));
    console.log(`Total Chunks: ${chunks.length}`);
    
    let totalTextLength = 0;
    chunks.forEach((chunk, index) => {
      console.log(`\nChunk ${index + 1}:`);
      console.log(`  ID: ${chunk.id}`);
      console.log(`  Text Length: ${chunk.text.length} chars`);
      console.log(`  Text Preview: ${chunk.text.substring(0, 150)}${chunk.text.length > 150 ? '...' : ''}`);
      console.log(`  Metadata: Step="${chunk.metadata.stepTitle}" | Strategy="${chunk.metadata.chunkingStrategy}"`);
      
      totalTextLength += chunk.text.length;
    });
    
    console.log('\n📈 Summary:');
    console.log('='.repeat(30));
    console.log(`Total Chunks: ${chunks.length}`);
    console.log(`Total Text Length: ${totalTextLength} chars`);
    console.log(`Average Chunk Size: ${Math.round(totalTextLength / chunks.length)} chars`);
    
    if (chunks.length === 0) {
      console.log('❌ No chunks created - chunking failed');
    } else if (totalTextLength < 100) {
      console.log('⚠️  Very little text extracted - may have issues');
    } else {
      console.log('✅ Chunking appears successful');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

testChunking().catch(console.error);
