#!/usr/bin/env tsx

/**
 * Test RAG Worker for Cloudflare Vectorize
 * 
 * This worker tests the complete RAG pipeline including:
 * - Content indexing to Vectorize
 * - Vector search functionality
 * - RAG-powered chat responses
 * 
 * Usage:
 *   wrangler dev --local=false scripts/test-rag-worker.ts
 */

import { createDatabaseConnectionForWorkers } from '../app/db/connection';
import { learningContent } from '../app/db/schema';
import { eq } from 'drizzle-orm';
import { createLearningContentIndexingService } from '../app/lib/rag/services/learning-content-indexing-service';
import { createRagSearchService } from '../app/lib/rag/services/rag-search-service';
import { createVectorIndexingService } from '../app/lib/rag/services/vector-indexing-service';
import { createEmbeddingService } from '../app/lib/rag/services/embedding-service';
import type { CloudflareEnv } from '../app/lib/context.server';

// Define CloudflareWorkerEnv for this worker
interface CloudflareWorkerEnv extends CloudflareEnv {
  VECTORIZE_INDEX: VectorizeIndex;
}

// ExecutionContext interface
interface ExecutionContext {
  waitUntil(promise: Promise<any>): void;
  passThroughOnException(): void;
}

export default {
  async fetch(request: Request, env: CloudflareWorkerEnv, ctx: ExecutionContext): Promise<Response> {
    const url = new URL(request.url);
    
    // CORS headers for all responses
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    };
    
    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }
    
    if (url.pathname === '/test-index') {
      return handleTestIndex(request, env, ctx, corsHeaders);
    }
    
    if (url.pathname === '/test-search') {
      return handleTestSearch(request, env, ctx, corsHeaders);
    }
    
    if (url.pathname === '/test-rag-chat') {
      return handleTestRagChat(request, env, ctx, corsHeaders);
    }
    
    if (url.pathname === '/health') {
      return handleHealth(env, corsHeaders);
    }
    
    return new Response('RAG Test Worker\n\nEndpoints:\n- POST /test-index\n- POST /test-search\n- POST /test-rag-chat\n- GET /health', {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'text/plain' }
    });
  }
};

/**
 * Test content indexing to Vectorize
 */
async function handleTestIndex(request: Request, env: CloudflareWorkerEnv, ctx: ExecutionContext, corsHeaders: Record<string, string>): Promise<Response> {
  const startTime = Date.now();
  
  try {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '3', 10);
    const contentId = url.searchParams.get('content_id');
    
    console.log('🧪 Testing content indexing', {
      limit,
      contentId,
      hasVectorizeBinding: !!env.VECTORIZE_INDEX,
      hasVoyageKey: !!env.VOYAGE_API_KEY,
    });

    // Initialize database connection
    const { db } = createDatabaseConnectionForWorkers(env);
    
    // Get content to test
    let query = db.select().from(learningContent).limit(limit);
    if (contentId) {
      query = query.where(eq(learningContent.id, contentId));
    }
    
    const contentItems = await query;
    
    if (contentItems.length === 0) {
      return Response.json({
        success: false,
        error: 'No content items found for testing',
        vectorizeIndex: 'learning-content-embeddings (from binding)',
      }, { headers: corsHeaders });
    }

    // Initialize indexing service with real Vectorize binding
    const indexingService = createLearningContentIndexingService(env.VECTORIZE_INDEX, env);
    
    console.log('🔧 Initialized indexing service with real Vectorize binding');
    
    // Test indexing for each content item
    const results = [];
    let successful = 0;
    let failed = 0;
    
    for (const content of contentItems) {
      console.log(`📝 Testing indexing: ${content.title}`, {
        contentId: content.id,
        stepsCount: content.steps.length,
      });
      
      try {
        // Check if already indexed
        const isIndexed = await indexingService.isContentIndexed(content.id);
        
        // Index the content (force re-index for testing)
        const indexingResult = await indexingService.indexContent(content);
        
        if (indexingResult.success) {
          successful++;
          console.log(`✅ Successfully indexed: ${content.title}`, {
            chunksIndexed: indexingResult.chunksIndexed,
            wasAlreadyIndexed: isIndexed,
          });
          
          results.push({
            contentId: content.id,
            title: content.title,
            status: 'success',
            chunksIndexed: indexingResult.chunksIndexed,
            processingTime: indexingResult.processingTimeMs,
            wasAlreadyIndexed: isIndexed,
          });
        } else {
          failed++;
          console.error(`❌ Failed to index: ${content.title}`, {
            error: indexingResult.error,
          });
          
          results.push({
            contentId: content.id,
            title: content.title,
            status: 'failed',
            error: indexingResult.error,
          });
        }
      } catch (error) {
        failed++;
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`💥 Exception while indexing: ${content.title}`, {
          error: errorMessage,
        });
        
        results.push({
          contentId: content.id,
          title: content.title,
          status: 'error',
          error: errorMessage,
        });
      }
    }
    
    const processingTime = Date.now() - startTime;
    
    console.log('📊 Index testing completed', {
      totalProcessed: contentItems.length,
      successful,
      failed,
      processingTimeMs: processingTime,
    });
    
    return Response.json({
      success: true,
      testType: 'indexing',
      vectorizeIndex: 'learning-content-embeddings (from binding)',
      stats: {
        totalProcessed: contentItems.length,
        successful,
        failed,
        processingTimeMs: processingTime,
      },
      results,
    }, { headers: corsHeaders });
    
  } catch (error) {
    console.error('💥 Index testing failed:', error);
    
    return Response.json({
      success: false,
      testType: 'indexing',
      error: error instanceof Error ? error.message : 'Unknown error',
      vectorizeIndex: 'learning-content-embeddings (from binding)',
      processingTimeMs: Date.now() - startTime,
    }, { status: 500, headers: corsHeaders });
  }
}

/**
 * Test vector search functionality
 */
async function handleTestSearch(request: Request, env: CloudflareWorkerEnv, ctx: ExecutionContext, corsHeaders: Record<string, string>): Promise<Response> {
  const startTime = Date.now();
  
  try {
    const body = await request.json() as {
      query: string;
      contentId?: string;
      limit?: number;
    };
    
    const { query, contentId, limit = 5 } = body;
    
    if (!query) {
      return Response.json({
        success: false,
        error: 'Query parameter is required',
      }, { status: 400, headers: corsHeaders });
    }
    
    console.log('🔍 Testing vector search', {
      query,
      contentId,
      limit,
      hasVectorizeBinding: !!env.VECTORIZE_INDEX,
    });

    // Initialize services
    const vectorIndexingService = createVectorIndexingService(env.VECTORIZE_INDEX, env);
    const ragSearchService = createRagSearchService(vectorIndexingService);
    
    // Test direct Vectorize API first
    console.log('🧪 Testing direct Vectorize API...');
    try {
      // Generate embedding for the query using our embedding service
      const embeddingService = createEmbeddingService(env);
      const queryEmbedding = await embeddingService.generateEmbedding(query, 'query');
      console.log('✅ Query embedding generated:', { dimensions: queryEmbedding.embedding.length });
      
      // Test direct Vectorize query
      const directResults = await env.VECTORIZE_INDEX.query(queryEmbedding.embedding, {
        topK: limit,
        returnMetadata: true
      });
      console.log('🔍 Direct Vectorize results:', {
         count: directResults.count,
         matches: directResults.matches.map((m: any) => ({ id: m.id, score: m.score }))
       });
    } catch (error) {
      console.error('❌ Direct Vectorize test failed:', error);
    }

    // Perform search with no threshold to see raw results
    const rawSearchResponse = contentId 
      ? await ragSearchService.searchContentScoped(query, [contentId], {
          limit,
          similarityThreshold: 0, // No filtering
        })
      : await ragSearchService.searchContent(query, {
          limit,
          similarityThreshold: 0, // No filtering
        });
    
    console.log('🔍 Raw search results:', {
      totalRawResults: rawSearchResponse.results.length,
      rawScores: rawSearchResponse.results.map(r => r.score),
    });
    
    // Now apply threshold filtering
    const searchResponse = contentId 
      ? await ragSearchService.searchContentScoped(query, [contentId], {
          limit,
          similarityThreshold: 0.3,
        })
      : await ragSearchService.searchContent(query, {
          limit,
          similarityThreshold: 0.3,
        });
    
    const searchResults = searchResponse.results;
    
    const processingTime = Date.now() - startTime;
    
    console.log('🎯 Search completed', {
      resultsCount: searchResults.length,
      processingTimeMs: processingTime,
      filteredScores: searchResults.map(r => r.score),
    });
    
    return Response.json({
      success: true,
      testType: 'search',
      vectorizeIndex: 'learning-content-embeddings (from binding)',
      query,
      stats: {
        resultsCount: searchResults.length,
        processingTimeMs: processingTime,
      },
      results: searchResults.map(result => ({
        contentId: result.metadata.learningContentId,
        chunkText: result.content.substring(0, 200) + '...',
        score: result.score,
        metadata: result.metadata,
      })),
    }, { headers: corsHeaders });
    
  } catch (error) {
    console.error('💥 Search testing failed:', error);
    
    return Response.json({
      success: false,
      testType: 'search',
      error: error instanceof Error ? error.message : 'Unknown error',
      vectorizeIndex: 'learning-content-embeddings (from binding)',
      processingTimeMs: Date.now() - startTime,
    }, { status: 500, headers: corsHeaders });
  }
}

/**
 * Test RAG-powered chat responses
 */
async function handleTestRagChat(request: Request, env: CloudflareWorkerEnv, ctx: ExecutionContext, corsHeaders: Record<string, string>): Promise<Response> {
  const startTime = Date.now();
  
  try {
    const body = await request.json() as {
      message: string;
      contentId?: string;
      maxResults?: number;
    };
    
    const { message, contentId, maxResults = 3 } = body;
    
    if (!message) {
      return Response.json({
        success: false,
        error: 'Message parameter is required',
      }, { status: 400, headers: corsHeaders });
    }
    
    console.log('💬 Testing RAG chat', {
      message,
      contentId,
      maxResults,
      hasVectorizeBinding: !!env.VECTORIZE_INDEX,
    });

    // Initialize services
    const vectorIndexingService = createVectorIndexingService(env.VECTORIZE_INDEX, env);
    const ragSearchService = createRagSearchService(vectorIndexingService);
    
    // Search for relevant content
    const searchResponse = contentId 
      ? await ragSearchService.searchContentScoped(message, [contentId], {
          limit: maxResults,
          similarityThreshold: 0.3,
        })
      : await ragSearchService.searchContent(message, {
          limit: maxResults,
          similarityThreshold: 0.3,
        });
    
    const relevantContent = searchResponse.results;
    
    // Generate context from search results
    const context = relevantContent.map(result => 
      `Content: ${result.content}\nSource: ${result.metadata.learningContentId || 'Unknown'}`
    ).join('\n\n');
    
    // Simulate RAG response (in real implementation, this would call an LLM)
    const ragResponse = {
      message: `Based on your learning content, here's what I found:\n\n${context ? context : 'No relevant content found for your query.'}`,
      sources: relevantContent.map(result => ({
          contentId: result.metadata.learningContentId,
          title: result.metadata.stepTitle || 'Unknown',
          score: result.score,
          chunkText: result.content.substring(0, 100) + '...',
        })),
      contextUsed: context.length > 0,
    };
    
    const processingTime = Date.now() - startTime;
    
    console.log('🤖 RAG chat completed', {
      sourcesFound: relevantContent.length,
      contextLength: context.length,
      processingTimeMs: processingTime,
    });
    
    return Response.json({
      success: true,
      testType: 'rag-chat',
      vectorizeIndex: 'learning-content-embeddings (from binding)',
      userMessage: message,
      stats: {
        sourcesFound: relevantContent.length,
        contextLength: context.length,
        processingTimeMs: processingTime,
      },
      response: ragResponse,
    }, { headers: corsHeaders });
    
  } catch (error) {
    console.error('💥 RAG chat testing failed:', error);
    
    return Response.json({
      success: false,
      testType: 'rag-chat',
      error: error instanceof Error ? error.message : 'Unknown error',
      vectorizeIndex: 'learning-content-embeddings (from binding)',
      processingTimeMs: Date.now() - startTime,
    }, { status: 500, headers: corsHeaders });
  }
}

/**
 * Health check endpoint
 */
async function handleHealth(env: CloudflareWorkerEnv, corsHeaders: Record<string, string>): Promise<Response> {
  return Response.json({
    status: 'healthy',
    testWorker: true,
    vectorizeBinding: !!env.VECTORIZE_INDEX,
    voyageApiKey: !!env.VOYAGE_API_KEY,
    hyperdrive: !!env.HYPERDRIVE,
    timestamp: new Date().toISOString(),
    vectorizeIndex: 'learning-content-embeddings (from binding)',
    availableEndpoints: [
      'POST /test-index - Test content indexing',
      'POST /test-search - Test vector search',
      'POST /test-rag-chat - Test RAG chat responses',
      'GET /health - Health check',
    ],
  }, { headers: corsHeaders });
}