#!/usr/bin/env tsx

/**
 * RAG Chat Testing Script
 * 
 * This script tests the RAG-powered chat functionality by:
 * 1. Testing vector search on existing content
 * 2. Testing chat service with mock interactions
 * 3. Validating response quality and source attribution
 */

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { eq } from 'drizzle-orm';
import { learningContent } from '../app/db/schema/learning-content';
import * as schema from '../app/db/schema';
import { createRagChatService, createRagSearchService, createVectorIndexingService } from '../app/lib/rag';
import { log } from '../app/lib/logger';

interface TestResult {
  test: string;
  success: boolean;
  details: any;
  error?: string;
}

/**
 * Test RAG search functionality
 */
async function testRagSearch(): Promise<TestResult> {
  try {
    log.info('🔍 Testing RAG search functionality...');

    // Initialize services (will use mock implementations in development)
    const vectorIndexingService = createVectorIndexingService(undefined as any, undefined);
    const ragSearchService = createRagSearchService(vectorIndexingService);

    // Test basic search
    const searchResults = await ragSearchService.searchContent('dark matter physics', {
      limit: 3,
      similarityThreshold: 0.5,
      enrichWithSourceContent: true,
    });

    return {
      test: 'RAG Search',
      success: true,
      details: {
        query: 'dark matter physics',
        resultsCount: searchResults.results.length,
        processingTime: searchResults.processingTimeMs,
        hasResults: searchResults.results.length > 0,
        results: searchResults.results.map(r => ({
          score: r.score,
          contentPreview: r.content.substring(0, 100) + '...',
          source: r.sourceContent?.title || 'Unknown',
        })),
      },
    };
  } catch (error) {
    return {
      test: 'RAG Search',
      success: false,
      details: {},
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Test RAG chat service
 */
async function testRagChat(): Promise<TestResult> {
  try {
    log.info('💬 Testing RAG chat service...');

    // Initialize database connection
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    const client = postgres(connectionString);
    const db = drizzle(client, { schema });

    // Get a test learning content item
    const contentItems = await db.select().from(learningContent).limit(1);
    
    if (contentItems.length === 0) {
      throw new Error('No learning content found for testing');
    }

    const testContent = contentItems[0];
    log.info('Using test content:', { id: testContent.id, title: testContent.title });

    // Initialize chat service (will use mock implementations)
    const chatService = createRagChatService(db);

    // Test questions to ask about the content
    const testQuestions = [
      'What is this content about?',
      'Can you explain the main concepts?',
      'What are the key takeaways?',
    ];

    const results = [];

    for (const question of testQuestions) {
      log.info(`Asking: "${question}"`);
      
      const chatResponse = await chatService.chat({
        message: question,
        learningContentId: testContent.id,
        userId: 'test-user-123',
      });

      results.push({
        question,
        success: chatResponse.success,
        responseLength: chatResponse.message?.content.length || 0,
        sourcesCount: chatResponse.message?.sources?.length || 0,
        conversationId: chatResponse.conversationId,
        processingTime: chatResponse.processingTime,
        error: chatResponse.error,
      });

      // Add small delay between requests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    await client.end();

    return {
      test: 'RAG Chat Service',
      success: results.every(r => r.success),
      details: {
        testContentId: testContent.id,
        testContentTitle: testContent.title,
        questionsAsked: testQuestions.length,
        results,
      },
    };
  } catch (error) {
    return {
      test: 'RAG Chat Service',
      success: false,
      details: {},
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Test conversation context management
 */
async function testConversationContext(): Promise<TestResult> {
  try {
    log.info('🗨️ Testing conversation context management...');

    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    const client = postgres(connectionString);
    const db = drizzle(client, { schema });

    // Get test content
    const contentItems = await db.select().from(learningContent).limit(1);
    if (contentItems.length === 0) {
      throw new Error('No learning content found');
    }

    const testContent = contentItems[0];
    const chatService = createRagChatService(db);

    // Start a conversation
    log.info('Starting conversation...');
    const firstResponse = await chatService.chat({
      message: 'Hello, can you tell me about this content?',
      learningContentId: testContent.id,
      userId: 'test-user-context',
    });

    if (!firstResponse.success || !firstResponse.conversationId) {
      throw new Error('Failed to start conversation');
    }

    const conversationId = firstResponse.conversationId;

    // Continue conversation with context
    log.info('Continuing conversation with context...');
    const followUpResponse = await chatService.chat({
      message: 'Can you elaborate on that?',
      conversationId,
      learningContentId: testContent.id,
      userId: 'test-user-context',
    });

    // Get conversation history
    const conversation = await chatService.getConversation(conversationId, 'test-user-context');

    await client.end();

    return {
      test: 'Conversation Context',
      success: firstResponse.success && followUpResponse.success && !!conversation,
      details: {
        conversationId,
        firstResponseLength: firstResponse.message?.content.length || 0,
        followUpResponseLength: followUpResponse.message?.content.length || 0,
        messageCount: conversation?.messages.length || 0,
        conversationTitle: conversation?.title,
      },
    };
  } catch (error) {
    return {
      test: 'Conversation Context',
      success: false,
      details: {},
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Test response quality and source attribution
 */
async function testResponseQuality(): Promise<TestResult> {
  try {
    log.info('📊 Testing response quality and source attribution...');

    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    const client = postgres(connectionString);
    const db = drizzle(client, { schema });

    const contentItems = await db.select().from(learningContent).limit(1);
    if (contentItems.length === 0) {
      throw new Error('No learning content found');
    }

    const testContent = contentItems[0];
    const chatService = createRagChatService(db);

    // Test specific questions that should have good source attribution
    const detailedQuestion = 'What are the specific steps or components mentioned in this content?';
    
    const response = await chatService.chat({
      message: detailedQuestion,
      learningContentId: testContent.id,
      userId: 'test-user-quality',
    });

    await client.end();

    // Quality checks
    const hasResponse = !!response.message?.content;
    const hasGoodLength = (response.message?.content.length || 0) > 50;
    const hasSources = (response.message?.sources?.length || 0) > 0;
    const hasReasonableProcessingTime = (response.processingTime || 0) < 10000; // Under 10 seconds

    return {
      test: 'Response Quality',
      success: response.success && hasResponse && hasGoodLength,
      details: {
        question: detailedQuestion,
        responseSuccess: response.success,
        responseLength: response.message?.content.length || 0,
        sourcesCount: response.message?.sources?.length || 0,
        processingTime: response.processingTime,
        qualityChecks: {
          hasResponse,
          hasGoodLength,
          hasSources,
          hasReasonableProcessingTime,
        },
        sources: response.message?.sources?.map(s => ({
          stepTitle: s.stepTitle,
          score: s.score,
        })) || [],
      },
    };
  } catch (error) {
    return {
      test: 'Response Quality',
      success: false,
      details: {},
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Run all tests
 */
async function runTests(): Promise<void> {
  const startTime = Date.now();
  
  log.info('🚀 Starting RAG Chat System Tests');
  log.info('==================================');

  const tests = [
    testRagSearch,
    testRagChat,
    testConversationContext,
    testResponseQuality,
  ];

  const results: TestResult[] = [];

  for (const test of tests) {
    try {
      const result = await test();
      results.push(result);
      
      if (result.success) {
        log.success(`✅ ${result.test}: PASSED`);
      } else {
        log.error(`❌ ${result.test}: FAILED - ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      log.error(`💥 ${test.name} crashed:`, error);
      results.push({
        test: test.name,
        success: false,
        details: {},
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  const totalTime = Date.now() - startTime;
  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;

  // Print summary
  log.info('\n📊 TEST SUMMARY');
  log.info('================');
  log.info(`⏱️  Total time: ${(totalTime / 1000).toFixed(2)}s`);
  log.info(`✅ Passed: ${passed}`);
  log.info(`❌ Failed: ${failed}`);
  log.info(`🎯 Success rate: ${((passed / results.length) * 100).toFixed(1)}%`);

  // Print detailed results
  log.info('\n📋 DETAILED RESULTS');
  log.info('===================');
  
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    log.info(`${status} ${result.test}`);
    
    if (result.success) {
      log.info(`   Details: ${JSON.stringify(result.details, null, 2)}`);
    } else {
      log.error(`   Error: ${result.error}`);
      if (Object.keys(result.details).length > 0) {
        log.info(`   Details: ${JSON.stringify(result.details, null, 2)}`);
      }
    }
    log.info('');
  });

  if (failed === 0) {
    log.success('🎉 All RAG Chat tests passed! The system is ready for use.');
  } else {
    log.warn(`⚠️  ${failed} test(s) failed. Check the errors above for details.`);
  }

  // Note about mock services
  log.info('\n💡 NOTE: These tests use mock implementations in development.');
  log.info('   To test with real services, ensure VOYAGE_API_KEY and VECTORIZE_INDEX_NAME are set.');
}

/**
 * Main execution
 */
async function main(): Promise<void> {
  try {
    await runTests();
  } catch (error) {
    log.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}