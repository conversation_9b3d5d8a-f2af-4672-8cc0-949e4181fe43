#!/usr/bin/env tsx

/**
 * Debug script to test environment variables in deployed Cloudflare Worker
 */

interface DebugOptions {
  workerUrl?: string;
  environment?: 'develop' | 'production';
}

async function debugEnvironmentVariables(options: DebugOptions = {}) {
  const {
    workerUrl = 'https://kwaci-learning-dev.bm.workers.dev',
    environment = 'develop'
  } = options;
  
  console.log('🔍 Debugging Environment Variables in Deployed Worker');
  console.log('='.repeat(60));
  console.log(`Worker URL: ${workerUrl}`);
  console.log(`Environment: ${environment}`);
  
  try {
    // Test 1: Health check endpoint
    console.log('\n🏥 Step 1: Testing Health Check...');
    try {
      const healthResponse = await fetch(`${workerUrl}/api/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      const healthData = await healthResponse.json();
      console.log('✅ Health check response:', JSON.stringify(healthData, null, 2));
    } catch (error) {
      console.log('❌ Health check failed:', error instanceof Error ? error.message : String(error));
    }
    
    // Test 2: Create a debug endpoint test
    console.log('\n🔧 Step 2: Testing Environment Variable Access...');
    console.log('This will test if the worker can access OPENROUTER_API_KEY');
    
    // Create a test chat request to trigger the environment variable access
    const testChatPayload = {
      message: "Test message",
      learningContentId: "b98195e2-7bfe-4ce5-8158-0fcdf0f74b20"
    };
    
    try {
      const chatResponse = await fetch(`${workerUrl}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token', // This will fail auth but should show env var error first
        },
        body: JSON.stringify(testChatPayload),
      });
      
      const chatData = await chatResponse.json();
      console.log('📊 Chat response status:', chatResponse.status);
      console.log('📊 Chat response:', JSON.stringify(chatData, null, 2));
      
      // Analyze the error message
      if (chatData.error) {
        if (chatData.error.includes('OpenRouter API key is missing')) {
          console.log('❌ ISSUE CONFIRMED: OpenRouter API key is not accessible in the deployed worker');
          console.log('');
          console.log('🔧 SOLUTIONS:');
          console.log('1. Check if OPENROUTER_API_KEY is set as a secret in Cloudflare Workers dashboard');
          console.log('2. Verify the secret is bound to the correct environment (develop/production)');
          console.log('3. Ensure the secret name matches exactly: OPENROUTER_API_KEY');
          console.log('4. Check wrangler.json configuration for environment-specific settings');
        } else if (chatData.error.includes('auth') || chatData.error.includes('token')) {
          console.log('✅ Environment variable access appears to be working');
          console.log('   (Got authentication error instead of API key error)');
        } else {
          console.log('⚠️  Got unexpected error:', chatData.error);
        }
      }
    } catch (error) {
      console.log('❌ Chat request failed:', error instanceof Error ? error.message : String(error));
    }
    
    // Test 3: Provide debugging instructions
    console.log('\n📋 Step 3: Debugging Instructions');
    console.log('='.repeat(40));
    console.log('');
    console.log('To fix the OpenRouter API key issue:');
    console.log('');
    console.log('1. 🔑 Set the secret in Cloudflare Workers:');
    console.log('   wrangler secret put OPENROUTER_API_KEY --env develop');
    console.log('   # Enter your API key when prompted: 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');
    console.log('');
    console.log('2. 📋 Verify the secret is set:');
    console.log('   wrangler secret list --env develop');
    console.log('');
    console.log('3. 🚀 Deploy the updated worker:');
    console.log('   wrangler deploy --env develop');
    console.log('');
    console.log('4. 🧪 Test the fix:');
    console.log('   bun run scripts/debug-env-vars.ts');
    console.log('');
    console.log('Alternative method using Cloudflare Dashboard:');
    console.log('1. Go to Cloudflare Workers dashboard');
    console.log('2. Select your worker (kwaci-learning-dev)');
    console.log('3. Go to Settings > Environment Variables');
    console.log('4. Add secret: OPENROUTER_API_KEY = 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');
    console.log('5. Deploy the worker');
    
    // Test 4: Check wrangler configuration
    console.log('\n⚙️  Step 4: Wrangler Configuration Check');
    console.log('='.repeat(40));
    console.log('');
    console.log('Current wrangler.json environment configuration:');
    console.log('- Environment: develop');
    console.log('- Worker name: kwaci-learning-dev');
    console.log('- Vectorize binding: VECTORIZE_INDEX');
    console.log('- Hyperdrive binding: HYPERDRIVE');
    console.log('');
    console.log('⚠️  Note: Secrets are not visible in wrangler.json');
    console.log('   They must be set separately using wrangler secret commands');
    
  } catch (error) {
    console.error('❌ Debug process failed:', error);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const workerUrl = args.find(arg => arg.startsWith('--url='))?.split('=')[1];
const environment = args.find(arg => arg.startsWith('--env='))?.split('=')[1] as 'develop' | 'production';

debugEnvironmentVariables({ workerUrl, environment }).catch(console.error);
