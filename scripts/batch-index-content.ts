#!/usr/bin/env tsx

/**
 * Batch indexing script for existing learning content
 * 
 * This script indexes all existing learning content in the database
 * to populate the vector store for RAG functionality.
 * 
 * Usage:
 *   npm run batch-index
 *   or
 *   npx tsx scripts/batch-index-content.ts
 * 
 * Options:
 *   --dry-run         Show what would be indexed without actually doing it
 *   --limit N         Limit the number of content items to process (default: all)
 *   --user-id ID      Index content only for specific user ID
 *   --content-id ID   Index specific content item by ID
 *   --force           Force re-index even if content is already indexed
 */

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { eq, and } from 'drizzle-orm';
import { learningContent } from '../app/db/schema/learning-content';
import { createLearningContentIndexingService } from '../app/lib/rag';
import { log } from '../app/lib/logger';

interface BatchIndexOptions {
  dryRun?: boolean;
  limit?: number;
  userId?: string;
  contentId?: string;
  force?: boolean;
}

interface IndexingStats {
  totalContent: number;
  successfullyIndexed: number;
  alreadyIndexed: number;
  failed: number;
  errors: Array<{ contentId: string; error: string }>;
  startTime: number;
  endTime: number;
}

/**
 * Main batch indexing function
 */
async function batchIndexContent(options: BatchIndexOptions = {}): Promise<void> {
  const stats: IndexingStats = {
    totalContent: 0,
    successfullyIndexed: 0,
    alreadyIndexed: 0,
    failed: 0,
    errors: [],
    startTime: Date.now(),
    endTime: 0,
  };

  try {
    // Initialize database connection
    const connectionString = process.env.DATABASE_URL;
    if (!connectionString) {
      throw new Error('DATABASE_URL environment variable is required');
    }

    const client = postgres(connectionString);
    const db = drizzle(client);

    log.info('🚀 Starting batch content indexing', {
      dryRun: options.dryRun,
      limit: options.limit,
      userId: options.userId,
      contentId: options.contentId,
      force: options.force,
    });

    // Build query filters
    const filters = [];
    if (options.userId) {
      filters.push(eq(learningContent.userId, options.userId));
    }
    if (options.contentId) {
      filters.push(eq(learningContent.id, options.contentId));
    }

    // Fetch content to index
    let query = db.select().from(learningContent);
    if (filters.length > 0) {
      query = query.where(and(...filters)) as any;
    }
    if (options.limit) {
      query = query.limit(options.limit) as any;
    }

    const contentItems = await query;
    stats.totalContent = contentItems.length;

    log.info(`📊 Found ${stats.totalContent} content items to process`);

    if (options.dryRun) {
      log.info('🔍 DRY RUN - Content that would be indexed:');
      contentItems.forEach((content, index) => {
        log.info(`  ${index + 1}. ${content.title} (ID: ${content.id}) - ${content.steps.length} steps`);
      });
      return;
    }

    if (stats.totalContent === 0) {
      log.info('✅ No content items found to index');
      return;
    }

    // Initialize indexing service
    // Check for Vectorize configuration
    const vectorizeIndexName = process.env.VECTORIZE_INDEX_NAME;
    const voyageApiKey = process.env.VOYAGE_API_KEY;
    
    log.info('🔧 Initializing indexing service...', {
      hasVectorizeIndexName: !!vectorizeIndexName,
      vectorizeIndexName: vectorizeIndexName || 'Not configured',
      hasVoyageApiKey: !!voyageApiKey,
      environment: process.env.NODE_ENV || 'development',
    });

    // Note: Node.js scripts don't have access to Cloudflare Workers Vectorize binding
    // So this will always use the mock implementation unless running in Workers context
    const indexingService = createLearningContentIndexingService();

    // Check what implementation we're using
    const implementationType = vectorizeIndexName && voyageApiKey ? 
      'Mock (Node.js environment - no Vectorize binding access)' : 
      'Mock (missing environment variables)';

    log.info('🔧 Indexing service initialized', {
      implementationType,
      note: 'To use real Vectorize indexing, run this in Cloudflare Workers context',
      mockBehavior: 'Will simulate indexing without storing in actual vector database',
    });

    // Process content items
    for (let i = 0; i < contentItems.length; i++) {
      const content = contentItems[i];
      const progress = `${i + 1}/${stats.totalContent}`;
      
      log.info(`📝 [${progress}] Processing: ${content.title}`, {
        contentId: content.id,
        userId: content.userId,
        stepsCount: content.steps.length,
        contentType: content.contentType,
      });

      try {
        // Check if already indexed (unless force flag is set)
        if (!options.force) {
          const isIndexed = await indexingService.isContentIndexed(content.id);
          if (isIndexed) {
            log.info(`⏭️  [${progress}] Content already indexed, skipping: ${content.title}`);
            stats.alreadyIndexed++;
            continue;
          }
        }

        // Index the content (fix icon property to be required)
        const contentWithRequiredIcons = {
          ...content,
          steps: content.steps.map(step => ({
            ...step,
            icon: step.icon || '📝' // Provide default icon if missing
          }))
        };
        const indexingResult = await indexingService.indexContent(contentWithRequiredIcons as any);

        if (indexingResult.success) {
          stats.successfullyIndexed++;
          log.success(`✅ [${progress}] Successfully indexed: ${content.title}`, {
            contentId: content.id,
            chunksIndexed: indexingResult.chunksIndexed,
            processingTime: `${indexingResult.processingTimeMs}ms`,
          });
        } else {
          stats.failed++;
          const error = indexingResult.error || 'Unknown error';
          stats.errors.push({ contentId: content.id, error });
          log.error(`❌ [${progress}] Failed to index: ${content.title}`, {
            contentId: content.id,
            error,
          });
        }
      } catch (error) {
        stats.failed++;
        const errorMessage = error instanceof Error ? error.message : String(error);
        stats.errors.push({ contentId: content.id, error: errorMessage });
        log.error(`💥 [${progress}] Exception while indexing: ${content.title}`, {
          contentId: content.id,
          error: errorMessage,
          stack: error instanceof Error ? error.stack : undefined,
        });
      }

      // Add small delay between items to avoid overwhelming the services
      if (i < contentItems.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    stats.endTime = Date.now();
    await client.end();

    // Print final statistics
    printFinalStats(stats);

  } catch (error) {
    stats.endTime = Date.now();
    log.error('💥 Batch indexing failed with critical error', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    
    printFinalStats(stats);
    process.exit(1);
  }
}

/**
 * Print final indexing statistics
 */
function printFinalStats(stats: IndexingStats): void {
  const duration = stats.endTime - stats.startTime;
  const durationSeconds = (duration / 1000).toFixed(2);

  log.info('\n📊 BATCH INDEXING COMPLETE');
  log.info('============================');
  log.info(`⏱️  Total time: ${durationSeconds}s`);
  log.info(`📚 Total content processed: ${stats.totalContent}`);
  log.info(`✅ Successfully indexed: ${stats.successfullyIndexed}`);
  log.info(`⏭️  Already indexed (skipped): ${stats.alreadyIndexed}`);
  log.info(`❌ Failed: ${stats.failed}`);

  if (stats.errors.length > 0) {
    log.info('\n🚨 ERRORS:');
    stats.errors.forEach((error, index) => {
      log.error(`  ${index + 1}. Content ID: ${error.contentId}`);
      log.error(`     Error: ${error.error}`);
    });
  }

  const successRate = stats.totalContent > 0 
    ? ((stats.successfullyIndexed / stats.totalContent) * 100).toFixed(1)
    : '0.0';
  
  log.info(`\n🎯 Success rate: ${successRate}%`);

  if (stats.failed > 0) {
    log.warn(`\n⚠️  ${stats.failed} content items failed to index. Check the errors above.`);
  } else if (stats.successfullyIndexed > 0) {
    log.success('\n🎉 All content successfully indexed!');
  } else if (stats.alreadyIndexed === stats.totalContent) {
    log.info('\n✨ All content was already indexed (use --force to re-index)');
  }
}

/**
 * Parse command line arguments
 */
function parseArguments(): BatchIndexOptions {
  const args = process.argv.slice(2);
  const options: BatchIndexOptions = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--limit':
        const limit = parseInt(args[++i], 10);
        if (isNaN(limit) || limit <= 0) {
          throw new Error('--limit must be a positive number');
        }
        options.limit = limit;
        break;
      case '--user-id':
        options.userId = args[++i];
        if (!options.userId) {
          throw new Error('--user-id requires a value');
        }
        break;
      case '--content-id':
        options.contentId = args[++i];
        if (!options.contentId) {
          throw new Error('--content-id requires a value');
        }
        break;
      case '--force':
        options.force = true;
        break;
      case '--help':
        printHelp();
        process.exit(0);
        break;
      default:
        throw new Error(`Unknown argument: ${arg}`);
    }
  }

  return options;
}

/**
 * Print help information
 */
function printHelp(): void {
  console.log(`
📚 Batch Content Indexing Script

USAGE:
  npm run batch-index [options]
  npx tsx scripts/batch-index-content.ts [options]

OPTIONS:
  --dry-run           Show what would be indexed without actually doing it
  --limit N           Limit the number of content items to process (default: all)
  --user-id ID        Index content only for specific user ID
  --content-id ID     Index specific content item by ID
  --force             Force re-index even if content is already indexed
  --help              Show this help message

EXAMPLES:
  # Index all content (dry run first to see what will be processed)
  npm run batch-index -- --dry-run
  npm run batch-index

  # Index only first 10 items
  npm run batch-index -- --limit 10

  # Index content for specific user
  npm run batch-index -- --user-id abc123

  # Re-index specific content item (force)
  npm run batch-index -- --content-id content123 --force

  # Index limited content for specific user (dry run)
  npm run batch-index -- --user-id abc123 --limit 5 --dry-run

ENVIRONMENT VARIABLES:
  DATABASE_URL        Required: PostgreSQL connection string
  VOYAGE_API_KEY      Optional: For embeddings (falls back to mock in development)
  VECTORIZE_INDEX_NAME Optional: For vector storage (falls back to mock in development)

VECTORIZE INDEX CONFIGURATION:
  This script runs in Node.js and cannot access Cloudflare Workers Vectorize bindings.
  It will always use mock implementations that simulate the indexing process.
  
  For REAL indexing to Vectorize:
  - The indexing happens automatically when content is created via the web app
  - Or use the API endpoints which run in Cloudflare Workers context
  - Configured indexes: learning-content-embeddings-dev, learning-content-embeddings-prod

NOTE: In development, this script uses mock implementations that simulate indexing
without actually storing vectors in Cloudflare Vectorize. Real indexing occurs
through the web application which runs in Cloudflare Workers.
`);
}

/**
 * Main execution
 */
async function main(): Promise<void> {
  try {
    const options = parseArguments();
    await batchIndexContent(options);
  } catch (error) {
    if (error instanceof Error) {
      log.error(`❌ ${error.message}`);
    } else {
      log.error('❌ Unknown error occurred');
    }
    process.exit(1);
  }
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

export { batchIndexContent, type BatchIndexOptions };