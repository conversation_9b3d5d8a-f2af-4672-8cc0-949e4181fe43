#!/usr/bin/env tsx

/**
 * Test script to verify OpenRouter API key fix in deployed worker
 */

import { createOpenRouter } from '@openrouter/ai-sdk-provider';

async function testOpenRouterFix() {
  const workerUrl = 'https://kwaci-learning-dev.bm.workers.dev';
  const mockApiKey = 'test-api-key';
  
  console.log('🧪 Testing OpenRouter API Key Fix');
  console.log('='.repeat(50));
  console.log(`Worker URL: ${workerUrl}`);
  
  // Test with API key
  console.log('\n🔧 Testing with API key...');
  const providerWithKey = createOpenRouter({
    apiKey: mockApiKey,
  });
  const modelWithKey = providerWithKey('meta-llama/llama-3.1-8b-instruct');
  
  // Test with the actual chat endpoint that was failing
  const testPayload = {
    message: "What is dark matter?",
    learningContentId: "b98195e2-7bfe-4ce5-8158-0fcdf0f74b20"
  };
  
  console.log('\n📝 Test Payload:');
  console.log(JSON.stringify(testPayload, null, 2));
  
  try {
    console.log('\n🚀 Making request to chat endpoint...');
    console.log('Model configured with API key:', !!modelWithKey);
    
    const response = await fetch(`${workerUrl}/api/chat`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Using a test auth header - this should fail auth but show if OpenRouter key error is fixed
        'Authorization': 'Bearer test-token-for-debugging'
      },
      body: JSON.stringify(testPayload),
    });
    
    console.log(`📊 Response Status: ${response.status}`);
    console.log(`📊 Response Headers:`, Object.fromEntries(response.headers.entries()));
    
    const responseData = await response.json();
    console.log('\n📋 Response Data:');
    console.log(JSON.stringify(responseData, null, 2));
    
    // Analyze the response
    console.log('\n🔍 Analysis:');
    console.log('='.repeat(30));
    
    if (responseData.error) {
      if (responseData.error.includes('OpenRouter API key is missing')) {
        console.log('❌ ISSUE STILL EXISTS: OpenRouter API key is not accessible');
        console.log('');
        console.log('🔧 Next Steps:');
        console.log('1. Verify the secret is properly set:');
        console.log('   wrangler secret list --env develop');
        console.log('2. Check if the environment variable name is correct');
        console.log('3. Ensure the worker is accessing env variables correctly');
        console.log('4. Verify createOpenRouter is used instead of openrouter function');
      } else if (responseData.error.includes('auth') || responseData.error.includes('token') || responseData.error.includes('Unauthorized')) {
        console.log('✅ SUCCESS: OpenRouter API key issue is FIXED!');
        console.log('   The error is now about authentication, not the API key');
        console.log('   This means the worker can access the OPENROUTER_API_KEY environment variable');
        console.log('   And createOpenRouter provider is working correctly');
      } else if (responseData.error.includes('Internal server error')) {
        console.log('⚠️  Got internal server error - need to check worker logs');
        console.log('   This could be a different issue than the API key');
      } else {
        console.log('⚠️  Got unexpected error:', responseData.error);
        console.log('   This might indicate the API key issue is fixed but there\'s another problem');
      }
    } else if (responseData.success) {
      console.log('✅ COMPLETE SUCCESS: Chat endpoint is working!');
      console.log('   OpenRouter API key issue is definitely fixed');
      console.log('   createOpenRouter provider is working correctly');
    } else {
      console.log('⚠️  Unexpected response format');
    }
    
  } catch (error) {
    console.error('❌ Request failed:', error instanceof Error ? error.message : String(error));
    console.log('\nThis could indicate:');
    console.log('1. Network connectivity issues');
    console.log('2. Worker deployment issues');
    console.log('3. Invalid request format');
  }
  
  console.log('\n📋 Summary:');
  console.log('='.repeat(30));
  console.log('The fix involved updating the RAG chat service to:');
  console.log('1. Store the env parameter in the constructor');
  console.log('2. Access OPENROUTER_API_KEY from env in generateResponse method');
  console.log('3. Use createOpenRouter() provider instead of openrouter() function');
  console.log('4. Pass the API key to createOpenRouter() and get model from provider');
  console.log('');
  console.log('If the test shows authentication errors instead of API key errors,');
  console.log('then the OpenRouter API key issue is successfully resolved!');
}

testOpenRouterFix().catch(console.error);
