#!/usr/bin/env tsx

/**
 * Production Content Reindexing Service
 * 
 * This script provides production-ready content reindexing using direct Cloudflare Workers API.
 * It bypasses the test worker and uses the actual production indexing service.
 */

import { createDatabaseConnectionForWorkers } from '../app/db/connection';
import { learningContent } from '../app/db/schema';
import { eq } from 'drizzle-orm';
import { createLearningContentIndexingService } from '../app/lib/rag/services/learning-content-indexing-service';
import { log } from '../app/lib/logger';

interface ProductionReindexOptions {
  contentId?: string;
  batchSize?: number;
  vectorizeIndex?: any;
  env?: any;
}

/**
 * Reindex content directly using production services
 */
async function productionReindexContent(options: ProductionReindexOptions = {}) {
  const { contentId, batchSize = 10, vectorizeIndex, env } = options;
  
  try {
    log.info('🚀 Starting production content reindexing');
    
    // Validate environment
    if (!vectorizeIndex) {
      throw new Error('VECTORIZE_INDEX is required for production reindexing');
    }
    
    if (!env?.VOYAGE_API_KEY) {
      throw new Error('VOYAGE_API_KEY is required for production reindexing');
    }
    
    // Create database connection
    const { db } = createDatabaseConnectionForWorkers(env);
    
    // Create indexing service
    const indexingService = createLearningContentIndexingService(vectorizeIndex, env);
    
    let contentToReindex;
    
    if (contentId) {
      // Reindex specific content
      log.info(`🎯 Reindexing specific content: ${contentId}`);
      
      const [content] = await db.select().from(learningContent).where(eq(learningContent.id, contentId as string));
      
      if (!content) {
        throw new Error(`Content not found: ${contentId}`);
      }
      
      contentToReindex = [content];
    } else {
      // Reindex all content
      log.info('📊 Reindexing all content');
      contentToReindex = await db.select().from(learningContent);
    }
    
    log.info(`📋 Found ${contentToReindex.length} content items to reindex`);
    
    let successCount = 0;
    let failureCount = 0;
    
    // Process in batches
    for (let i = 0; i < contentToReindex.length; i += batchSize) {
      const batch = contentToReindex.slice(i, i + batchSize);
      log.info(`🔄 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(contentToReindex.length / batchSize)}`);
      
      // Process batch items in parallel
      const batchPromises = batch.map(async (content: any) => {
        try {
          const result = await indexingService.updateContentIndex(content as any);
          
          if (result.success) {
            successCount++;
            log.success(`✅ Reindexed: ${content.title} (${result.chunksIndexed} chunks)`);
          } else {
            failureCount++;
            log.error(`❌ Failed to reindex: ${content.title}`, result.error);
          }
        } catch (error) {
          failureCount++;
          log.error(`❌ Failed to reindex: ${content.title}`, error);
        }
      });
      
      await Promise.all(batchPromises);
      
      // Add delay between batches
      if (i + batchSize < contentToReindex.length) {
        log.info('⏳ Waiting 2 seconds before next batch...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    log.success('🎉 Production reindexing completed', {
      total: contentToReindex.length,
      successful: successCount,
      failed: failureCount
    });
    
    return {
      success: true,
      total: contentToReindex.length,
      successful: successCount,
      failed: failureCount
    };
    
  } catch (error) {
    log.error('❌ Production reindexing failed:', error);
    throw error;
  }
}

/**
 * CLI interface for production reindexing
 */
if (require.main === module) {
  const args = process.argv.slice(2);
  const options: ProductionReindexOptions = {};
  
  // Parse command line arguments
  for (let i = 0; i < args.length; i++) {
    switch (args[i]) {
      case '--content-id':
        options.contentId = args[++i];
        break;
      case '--batch-size':
        options.batchSize = parseInt(args[++i]);
        break;
      case '--help':
        console.log(`
Usage: bun run scripts/production-reindex.ts [options]

Options:
  --content-id <id>     Reindex specific content ID
  --batch-size <num>    Batch size for batch operations (default: 10)
  --help               Show this help

Examples:
  # Reindex specific content
  bun run scripts/production-reindex.ts --content-id "abc-123"
  
  # Reindex all content
  bun run scripts/production-reindex.ts
  
  # Reindex all content with smaller batches
  bun run scripts/production-reindex.ts --batch-size 5

Note: This script requires VECTORIZE_INDEX and VOYAGE_API_KEY environment variables.
      Run this in a Cloudflare Workers environment or with proper bindings.
        `);
        process.exit(0);
        break;
    }
  }
  
  // Note: In production, you would get these from Cloudflare Workers environment
  console.log('⚠️  This script requires Cloudflare Workers environment with VECTORIZE_INDEX binding');
  console.log('   Use this script within a Cloudflare Worker or with proper environment setup');
  
  // For development/testing, you could mock these:
  // options.vectorizeIndex = mockVectorizeIndex;
  // options.env = { VOYAGE_API_KEY: 'your-key' };
  
  // productionReindexContent(options).catch(console.error);
}

export { productionReindexContent };
