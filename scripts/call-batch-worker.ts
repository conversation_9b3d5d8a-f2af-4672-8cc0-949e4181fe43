#!/usr/bin/env tsx

/**
 * <PERSON><PERSON><PERSON> to call the batch index worker endpoint
 * 
 * This script makes HTTP requests to the Cloudflare Workers batch indexing endpoint
 * to perform real Vectorize indexing.
 * 
 * Usage:
 *   bun run call-batch-worker
 *   bun run call-batch-worker --dry-run --limit 5
 *   bun run call-batch-worker --user-id abc123
 */

interface WorkerResponse {
  success: boolean;
  vectorizeIndex?: string;
  stats?: {
    totalProcessed: number;
    successful: number;
    failed: number;
    processingTimeMs: number;
  };
  results?: Array<{
    contentId: string;
    title: string;
    status: string;
    chunksIndexed?: number;
    error?: string;
  }>;
  contentItems?: Array<{
    index: number;
    id: string;
    title: string;
    stepsCount: number;
    userId: string;
  }>;
  error?: string;
  message?: string;
}

interface CallOptions {
  workerUrl: string;
  dryRun: boolean;
  limit: number;
  userId?: string;
}

/**
 * Parse command line arguments
 */
function parseArguments(): CallOptions {
  const args = process.argv.slice(2);
  const options: CallOptions = {
    workerUrl: 'http://localhost:8787', // Default wrangler dev URL
    dryRun: false,
    limit: 10,
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--url':
        options.workerUrl = args[++i];
        if (!options.workerUrl) {
          throw new Error('--url requires a value');
        }
        break;
      case '--dry-run':
        options.dryRun = true;
        break;
      case '--limit':
        const limit = parseInt(args[++i], 10);
        if (isNaN(limit) || limit <= 0) {
          throw new Error('--limit must be a positive number');
        }
        options.limit = limit;
        break;
      case '--user-id':
        options.userId = args[++i];
        if (!options.userId) {
          throw new Error('--user-id requires a value');
        }
        break;
      case '--help':
        printHelp();
        process.exit(0);
        break;
      default:
        throw new Error(`Unknown argument: ${arg}`);
    }
  }

  return options;
}

/**
 * Print help information
 */
function printHelp(): void {
  console.log(`
🔧 Batch Index Worker Caller

USAGE:
  bun run call-batch-worker [options]

OPTIONS:
  --url URL           Worker URL (default: http://localhost:8787)
  --dry-run           Show what would be indexed without actually doing it
  --limit N           Limit the number of content items to process (default: 10)
  --user-id ID        Index content only for specific user ID
  --help              Show this help message

EXAMPLES:
  # Call worker with default settings
  bun run call-batch-worker

  # Dry run with limit
  bun run call-batch-worker --dry-run --limit 5

  # Index for specific user
  bun run call-batch-worker --user-id abc123 --limit 3

  # Call deployed worker
  bun run call-batch-worker --url https://your-worker.your-subdomain.workers.dev

PREREQUISITES:
  1. Run the batch index worker:
     wrangler dev --local=false scripts/batch-index-worker.ts
     
  2. Or deploy it temporarily:
     wrangler deploy scripts/batch-index-worker.ts --name batch-indexer
`);
}

/**
 * Check worker health
 */
async function checkHealth(workerUrl: string): Promise<void> {
  console.log(`🔍 Checking worker health at: ${workerUrl}/health`);
  
  try {
    const response = await fetch(`${workerUrl}/health`);
    const data = await response.json();
    
    console.log('✅ Worker health check:', {
      status: data.status,
      vectorizeBinding: data.vectorizeBinding,
      voyageApiKey: data.voyageApiKey,
      hyperdrive: data.hyperdrive,
      vectorizeIndex: data.vectorizeIndex,
    });
  } catch (error) {
    console.error('❌ Health check failed:', error instanceof Error ? error.message : String(error));
    throw new Error('Worker is not accessible. Make sure it\'s running with: wrangler dev --local=false scripts/batch-index-worker.ts');
  }
}

/**
 * Call the batch index endpoint
 */
async function callBatchIndex(options: CallOptions): Promise<WorkerResponse> {
  const url = new URL(`${options.workerUrl}/batch-index`);
  
  // Add query parameters
  url.searchParams.set('limit', options.limit.toString());
  if (options.dryRun) {
    url.searchParams.set('dry_run', 'true');
  }
  if (options.userId) {
    url.searchParams.set('user_id', options.userId);
  }

  console.log(`🚀 Calling batch index endpoint: ${url.toString()}`);
  console.log('Parameters:', {
    limit: options.limit,
    dryRun: options.dryRun,
    userId: options.userId || 'All users',
  });

  const response = await fetch(url.toString(), {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return await response.json();
}

/**
 * Display results
 */
function displayResults(result: WorkerResponse, isDryRun: boolean): void {
  console.log('\n📊 BATCH INDEXING RESULTS');
  console.log('==========================');
  console.log(`🗂️  Vectorize Index: ${result.vectorizeIndex || 'Unknown'}`);
  
  if (isDryRun) {
    console.log('🔍 DRY RUN RESULTS:');
    if (result.contentItems) {
      result.contentItems.forEach((item) => {
        console.log(`  ${item.index}. ${item.title} (${item.id})`);
        console.log(`     User: ${item.userId}, Steps: ${item.stepsCount}`);
      });
    }
    console.log(`\n📋 Total found: ${result.contentItems?.length || 0}`);
    console.log('💡 Use --dry-run=false to perform actual indexing');
    return;
  }

  if (result.stats) {
    console.log(`⏱️  Processing time: ${(result.stats.processingTimeMs / 1000).toFixed(2)}s`);
    console.log(`📚 Total processed: ${result.stats.totalProcessed}`);
    console.log(`✅ Successful: ${result.stats.successful}`);
    console.log(`❌ Failed: ${result.stats.failed}`);
    
    const successRate = result.stats.totalProcessed > 0 
      ? ((result.stats.successful / result.stats.totalProcessed) * 100).toFixed(1)
      : '0.0';
    console.log(`🎯 Success rate: ${successRate}%`);
  }

  if (result.results) {
    console.log('\n📋 DETAILED RESULTS:');
    result.results.forEach((item, index) => {
      const status = item.status === 'success' ? '✅' : 
                    item.status === 'skipped' ? '⏭️' : '❌';
      
      console.log(`${status} ${item.title} (${item.contentId})`);
      
      if (item.status === 'success' && item.chunksIndexed) {
        console.log(`   Chunks indexed: ${item.chunksIndexed}`);
      } else if (item.status === 'failed' || item.status === 'error') {
        console.log(`   Error: ${item.error}`);
      } else if (item.status === 'skipped') {
        console.log(`   Reason: Already indexed`);
      }
    });
  }

  if (result.message) {
    console.log(`\n💬 ${result.message}`);
  }
}

/**
 * Main execution
 */
async function main(): Promise<void> {
  try {
    const options = parseArguments();
    
    console.log('🔧 Batch Index Worker Caller');
    console.log('==============================');
    
    // Check worker health first
    await checkHealth(options.workerUrl);
    
    // Call batch index
    const result = await callBatchIndex(options);
    
    if (result.success) {
      displayResults(result, options.dryRun);
      
      if (!options.dryRun && result.stats?.successful === result.stats?.totalProcessed) {
        console.log('\n🎉 All content successfully indexed to Vectorize!');
      }
    } else {
      console.error('❌ Batch indexing failed:', result.error);
      process.exit(1);
    }
    
  } catch (error) {
    console.error('💥 Script failed:', error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Execute if run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

export { main, type CallOptions };