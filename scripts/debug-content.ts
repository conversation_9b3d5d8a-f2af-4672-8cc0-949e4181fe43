#!/usr/bin/env tsx

/**
 * Debug script to examine learning content structure
 */

import { createDatabaseConnection } from '../app/db/connection';
import { learningContent } from '../app/db/schema';
import { eq } from 'drizzle-orm';

// Text extraction function (copied from chunking service)
function extractTextFromBlock(block: any): string {
  if (!block.data) return '';

  switch (block.type) {
    case 'paragraph':
      return block.data.text || block.data || '';
    case 'infoBox':
      return `${block.data.title || ''}\n${block.data.content || ''}`;
    case 'bulletList':
    case 'numberedList':
      return Array.isArray(block.data) ? block.data.join('\n') : (block.data.items || []).join('\n');
    case 'grid':
      return Array.isArray(block.data)
        ? block.data.map((item: any) => `${item.title || ''}\n${item.content || ''}`).join('\n\n')
        : (block.data.items || []).map((item: any) => `${item.title || ''}\n${item.description || ''}`).join('\n\n');
    case 'comparison':
      return Array.isArray(block.data)
        ? block.data.map((item: any) => `${item.label || ''}: ${item.before || ''} vs ${item.after || ''}`).join('\n')
        : `${block.data.leftTitle || ''}: ${block.data.leftContent || ''}\n${block.data.rightTitle || ''}: ${block.data.rightContent || ''}`;
    case 'table': {
      const headers = (block.data.headers || []).join(' | ');
      const rows = (block.data.rows || []).map((row: any[]) => row.join(' | ')).join('\n');
      return `${headers}\n${rows}`;
    }
    case 'keyValueGrid':
      return Array.isArray(block.data)
        ? block.data.map((item: any) => `${item.key || ''}: ${item.value || ''}`).join('\n')
        : (block.data.items || []).map((item: any) => `${item.key || ''}: ${item.value || ''}`).join('\n');
    case 'scatterPlot':
      return `Chart: ${JSON.stringify(block.data)}`;
    default:
      return JSON.stringify(block.data);
  }
}

async function debugContent() {
  const contentId = 'b98195e2-7bfe-4ce5-8158-0fcdf0f74b20';

  console.log('🔍 Debugging content structure for:', contentId);

  try {
    const { db } = createDatabaseConnection({
      connectionString: process.env.DATABASE_URL!
    });
    
    // Get the specific content
    const content = await db
      .select()
      .from(learningContent)
      .where(eq(learningContent.id, contentId))
      .limit(1);
    
    if (!content.length) {
      console.log('❌ No content found with ID:', contentId);
      return;
    }
    
    const contentItem = content[0];
    
    console.log('\n📋 Content Overview:');
    console.log('='.repeat(50));
    console.log(`Title: ${contentItem.title}`);
    console.log(`Description: ${contentItem.description}`);
    console.log(`Learning Level: ${contentItem.learningLevel}`);
    console.log(`Content Type: ${contentItem.contentType}`);
    console.log(`Is Public: ${contentItem.isPublic}`);
    console.log(`Steps Count: ${contentItem.steps.length}`);
    
    console.log('\n📝 Steps Analysis:');
    console.log('='.repeat(50));
    
    contentItem.steps.forEach((step, stepIndex) => {
      console.log(`\nStep ${stepIndex + 1}: ${step.title}`);
      console.log(`  ID: ${step.id}`);
      console.log(`  Icon: ${step.icon || 'N/A'}`);
      console.log(`  Section Type: ${step.sectionType || 'N/A'}`);
      console.log(`  Blocks Count: ${step.blocks.length}`);
      
      step.blocks.forEach((block, blockIndex) => {
        console.log(`\n    Block ${blockIndex + 1}:`);
        console.log(`      ID: ${block.id}`);
        console.log(`      Type: ${block.type}`);
        
        // Analyze block data
        console.log(`      Data Structure: ${JSON.stringify(block.data, null, 2)}`);

        // Test text extraction
        const extractedText = extractTextFromBlock(block);
        console.log(`      Extracted Text: "${extractedText}"`);
        console.log(`      Extracted Length: ${extractedText.length} chars`);

        if (block.type === 'text' && block.data?.text) {
          const text = block.data.text;
          console.log(`      Text Length: ${text.length} chars`);
          console.log(`      Text Preview: ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`);

          // Check if it's readable text vs JSON/metadata
          if (text.includes('{"type":') || text.includes('"data":')) {
            console.log(`      ⚠️  WARNING: Text appears to be JSON/metadata`);
          }
        } else if (block.type === 'chart' && block.data) {
          console.log(`      Chart Type: ${block.data.chartType || 'Unknown'}`);
          console.log(`      Chart Data: ${JSON.stringify(block.data).substring(0, 100)}...`);
        }
      });
    });
    
    // Analyze what would be indexed
    console.log('\n🔍 Indexing Analysis:');
    console.log('='.repeat(50));
    
    let totalTextContent = '';
    let textBlocks = 0;
    let chartBlocks = 0;
    let otherBlocks = 0;
    
    contentItem.steps.forEach(step => {
      step.blocks.forEach(block => {
        const extractedText = extractTextFromBlock(block);
        if (extractedText && extractedText.length > 0) {
          textBlocks++;
          totalTextContent += extractedText + ' ';
        }

        if (block.type === 'chart' || block.type === 'scatterPlot') {
          chartBlocks++;
        } else if (block.type !== 'text' && block.type !== 'paragraph') {
          otherBlocks++;
        }
      });
    });
    
    console.log(`Text Blocks: ${textBlocks}`);
    console.log(`Chart Blocks: ${chartBlocks}`);
    console.log(`Other Blocks: ${otherBlocks}`);
    console.log(`Total Text Content Length: ${totalTextContent.length} chars`);
    
    if (totalTextContent.length > 0) {
      console.log(`\nSample Text Content for Indexing:`);
      console.log('-'.repeat(30));
      console.log(totalTextContent.substring(0, 500) + '...');
    }
    
    // Check for potential indexing issues
    console.log('\n⚠️  Potential Issues:');
    console.log('-'.repeat(30));
    
    if (textBlocks === 0) {
      console.log('❌ No text blocks found - nothing to index');
    }
    
    if (chartBlocks > textBlocks) {
      console.log('⚠️  More chart blocks than text blocks - may index chart data instead of text');
    }
    
    if (totalTextContent.includes('{"type":') || totalTextContent.includes('"data":')) {
      console.log('⚠️  Text content contains JSON structures - may be metadata instead of readable text');
    }
    
    if (totalTextContent.length < 100) {
      console.log('⚠️  Very little text content - may not provide good search results');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

debugContent().catch(console.error);
