/**
 * Batch indexing worker script for Cloudflare Workers
 * 
 * This script can access real Vectorize bindings and perform actual indexing.
 * It's designed to be run in Cloudflare Workers environment where Vectorize
 * bindings are available.
 * 
 * Usage:
 *   wrangler dev --local=false scripts/batch-index-worker.ts
 *   or deploy as a temporary worker for batch processing
 */

import { createLearningContentIndexingService } from '../app/lib/rag';
import { createDatabaseConnectionForWorkers } from '../app/db/connection';
import { learningContent } from '../app/db/schema/learning-content';
import { eq } from 'drizzle-orm';

// Cloudflare Workers types
type ExecutionContext = {
  waitUntil(promise: Promise<any>): void;
  passThroughOnException(): void;
};

type VectorizeIndex = any;

interface CloudflareWorkerEnv {
  VECTORIZE_INDEX: VectorizeIndex;
  VOYAGE_API_KEY: string;
  HYPERDRIVE?: any;
  DATABASE_URL?: string;
}

export default {
  async fetch(request: Request, env: CloudflareWorkerEnv, ctx: ExecutionContext): Promise<Response> {
    const url = new URL(request.url);
    
    // Basic route handling
    if (url.pathname === '/batch-index') {
      return handleBatchIndex(request, env, ctx);
    }
    
    if (url.pathname === '/health') {
      return handleHealth(env);
    }
    
    return new Response('Batch Indexing Worker\n\nEndpoints:\n- POST /batch-index\n- GET /health', {
      status: 200,
      headers: { 'Content-Type': 'text/plain' }
    });
  }
};

/**
 * Handle batch indexing requests
 */
async function handleBatchIndex(request: Request, env: CloudflareWorkerEnv, ctx: ExecutionContext): Promise<Response> {
  const startTime = Date.now();
  
  try {
    // Parse query parameters
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit') || '10', 10);
    const dryRun = url.searchParams.get('dry_run') === 'true';
    const userId = url.searchParams.get('user_id') || undefined;
    
    console.log('🚀 Starting batch indexing in Cloudflare Workers', {
      limit,
      dryRun,
      userId,
      hasVectorizeBinding: !!env.VECTORIZE_INDEX,
      hasVoyageKey: !!env.VOYAGE_API_KEY,
    });

    // Initialize database connection
    const { db } = createDatabaseConnectionForWorkers(env);
    
    // Get content to index
    let query = db.select().from(learningContent).limit(limit);
    if (userId) {
      query = query.where(eq(learningContent.userId, userId));
    }
    
    const contentItems = await query;
    
    console.log(`📊 Found ${contentItems.length} content items to process`);
    
    if (dryRun) {
      const preview = contentItems.map((content: any, index: number) => ({
        index: index + 1,
        id: content.id,
        title: content.title,
        stepsCount: content.steps.length,
        userId: content.userId,
      }));
      
      return Response.json({
        success: true,
        dryRun: true,
        contentItems: preview,
        totalFound: contentItems.length,
        vectorizeIndex: 'learning-content-embeddings (from binding)',
        message: 'Dry run completed - no actual indexing performed',
      });
    }
    
    if (contentItems.length === 0) {
      return Response.json({
        success: true,
        message: 'No content items found to index',
        vectorizeIndex: 'learning-content-embeddings (from binding)',
      });
    }

    // Initialize indexing service with real Vectorize binding
    const indexingService = createLearningContentIndexingService(env.VECTORIZE_INDEX, env);
    
    console.log('🔧 Initialized indexing service with real Vectorize binding');
    
    // Process content items
    const results = [];
    let successful = 0;
    let failed = 0;
    
    for (let i = 0; i < contentItems.length; i++) {
      const content = contentItems[i];
      const progress = `${i + 1}/${contentItems.length}`;
      
      console.log(`📝 [${progress}] Processing: ${content.title}`, {
        contentId: content.id,
        stepsCount: content.steps.length,
      });
      
      try {
        // Check if already indexed
        const isIndexed = await indexingService.isContentIndexed(content.id);
        if (isIndexed) {
          console.log(`⏭️ [${progress}] Already indexed, skipping: ${content.title}`);
          results.push({
            contentId: content.id,
            title: content.title,
            status: 'skipped',
            reason: 'already_indexed',
          });
          continue;
        }
        
        // Index the content
        const indexingResult = await indexingService.indexContent(content);
        
        if (indexingResult.success) {
          successful++;
          console.log(`✅ [${progress}] Successfully indexed: ${content.title}`, {
            chunksIndexed: indexingResult.chunksIndexed,
          });
          
          results.push({
            contentId: content.id,
            title: content.title,
            status: 'success',
            chunksIndexed: indexingResult.chunksIndexed,
            processingTime: indexingResult.processingTimeMs,
          });
        } else {
          failed++;
          console.error(`❌ [${progress}] Failed to index: ${content.title}`, {
            error: indexingResult.error,
          });
          
          results.push({
            contentId: content.id,
            title: content.title,
            status: 'failed',
            error: indexingResult.error,
          });
        }
      } catch (error) {
        failed++;
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error(`💥 [${progress}] Exception while indexing: ${content.title}`, {
          error: errorMessage,
        });
        
        results.push({
          contentId: content.id,
          title: content.title,
          status: 'error',
          error: errorMessage,
        });
      }
      
      // Small delay between items
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const processingTime = Date.now() - startTime;
    
    console.log('📊 Batch indexing completed', {
      totalProcessed: contentItems.length,
      successful,
      failed,
      processingTimeMs: processingTime,
    });
    
    return Response.json({
      success: true,
      vectorizeIndex: 'learning-content-embeddings (from binding)',
      stats: {
        totalProcessed: contentItems.length,
        successful,
        failed,
        processingTimeMs: processingTime,
      },
      results,
    });
    
  } catch (error) {
    console.error('💥 Batch indexing worker failed:', error);
    
    return Response.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      vectorizeIndex: 'learning-content-embeddings (from binding)',
      processingTimeMs: Date.now() - startTime,
    }, { status: 500 });
  }
}

/**
 * Health check endpoint
 */
async function handleHealth(env: CloudflareWorkerEnv): Promise<Response> {
  return Response.json({
    status: 'healthy',
    vectorizeBinding: !!env.VECTORIZE_INDEX,
    voyageApiKey: !!env.VOYAGE_API_KEY,
    hyperdrive: !!env.HYPERDRIVE,
    timestamp: new Date().toISOString(),
    vectorizeIndex: 'learning-content-embeddings (from binding)',
  });
}