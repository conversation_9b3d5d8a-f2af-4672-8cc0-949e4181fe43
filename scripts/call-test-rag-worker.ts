#!/usr/bin/env bun
/**
 * Call Test RAG Worker Script
 * 
 * This script calls the test RAG worker to test various RAG functionalities
 * including content indexing, vector search, and RAG-powered chat responses.
 */

import { log } from '../app/lib/logger';

interface WorkerResponse {
  success: boolean;
  error?: string;
  [key: string]: any;
}

interface TestIndexOptions {
  contentId: string;
  forceReindex?: boolean;
}

interface TestSearchOptions {
  query: string;
  contentId?: string;
  limit?: number;
}

interface TestChatOptions {
  message: string;
  contentId?: string;
  maxResults?: number;
}

/**
 * Parse command line arguments
 */
function parseArgs(): {
  action: 'index' | 'search' | 'chat' | 'health';
  url: string;
  options: any;
  help: boolean;
} {
  const args = process.argv.slice(2);
  
  let action: 'index' | 'search' | 'chat' | 'health' = 'health';
  let url = 'http://localhost:8787'; // Default Cloudflare Workers dev URL
  let options: any = {};
  let help = false;
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--help':
      case '-h':
        help = true;
        break;
      case '--url':
        url = args[++i];
        break;
      case '--action':
        action = args[++i] as 'index' | 'search' | 'chat' | 'health';
        break;
      case '--content-id':
        options.contentId = args[++i];
        break;
      case '--query':
        options.query = args[++i];
        break;
      case '--message':
        options.message = args[++i];
        break;
      case '--limit':
        options.limit = parseInt(args[++i], 10);
        break;
      case '--max-results':
        options.maxResults = parseInt(args[++i], 10);
        break;
      case '--force-reindex':
        options.forceReindex = true;
        break;
    }
  }
  
  return { action, url, options, help };
}

/**
 * Print help information
 */
function printHelp() {
  console.log(`
Test RAG Worker Caller

Usage: bun run scripts/call-test-rag-worker.ts [options]

Options:
  --url <url>              Worker URL (default: http://localhost:8787)
  --action <action>        Action to perform: health, index, search, chat
  --content-id <id>        Learning content ID for scoped operations
  --query <text>           Search query for vector search
  --message <text>         Message for RAG chat
  --limit <number>         Maximum search results (default: 5)
  --max-results <number>   Maximum chat context results (default: 3)
  --force-reindex          Force reindexing of content
  --help, -h               Show this help

Examples:
  # Check worker health
  bun run scripts/call-test-rag-worker.ts --action health
  
  # Test content indexing
  bun run scripts/call-test-rag-worker.ts --action index --content-id "content-123"
  
  # Test vector search
  bun run scripts/call-test-rag-worker.ts --action search --query "machine learning"
  
  # Test scoped search
  bun run scripts/call-test-rag-worker.ts --action search --query "neural networks" --content-id "content-123"
  
  # Test RAG chat
  bun run scripts/call-test-rag-worker.ts --action chat --message "Explain deep learning concepts"
  
  # Test RAG chat with content scope
  bun run scripts/call-test-rag-worker.ts --action chat --message "What are the key points?" --content-id "content-123"

Prerequisites:
  1. Deploy or run the test RAG worker (scripts/test-rag-worker.ts)
  2. Ensure Vectorize index is configured
  3. Set VOYAGE_API_KEY environment variable
  4. Configure database connection (HYPERDRIVE)
`);
}

/**
 * Check worker health
 */
async function checkHealth(url: string): Promise<WorkerResponse> {
  try {
    log.info('🏥 Checking worker health...');
    
    const response = await fetch(`${url}/health`);
    const result = await response.json();
    
    if (result.status === 'healthy') {
      log.info('✅ Worker is healthy', {
        vectorizeBinding: result.vectorizeBinding,
        voyageApiKey: result.voyageApiKey,
        hyperdrive: result.hyperdrive,
        vectorizeIndex: result.vectorizeIndex,
      });
    } else {
      log.warn('⚠️ Worker health check returned non-healthy status', result);
    }
    
    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    log.error('❌ Health check failed:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Test content indexing
 */
async function testIndexing(url: string, options: TestIndexOptions): Promise<WorkerResponse> {
  try {
    log.info('📝 Testing content indexing...', options);
    
    const response = await fetch(`${url}/test-index`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(options),
    });
    
    const result = await response.json();
    
    if (result.success) {
      log.info('✅ Content indexing test completed', {
        contentId: result.contentId,
        chunksIndexed: result.chunksIndexed,
        processingTime: result.processingTime,
        alreadyIndexed: result.alreadyIndexed,
      });
    } else {
      log.error('❌ Content indexing test failed:', result.error);
    }
    
    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    log.error('💥 Indexing test failed:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Test vector search with enhanced logging and analysis
 */
async function testSearch(url: string, options: TestSearchOptions): Promise<WorkerResponse> {
  try {
    log.info('🔍 Testing vector search...', options);

    const response = await fetch(`${url}/test-search`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(options),
    });

    const result = await response.json();

    if (result.success) {
      log.info('✅ Vector search test completed', {
        query: result.query,
        resultsCount: result.stats?.resultsCount || 0,
        processingTime: result.stats?.processingTimeMs || 'N/A',
        hasResults: result.results && result.results.length > 0,
      });

      if (result.results && result.results.length > 0) {
        console.log('\n📋 Detailed Search Results Analysis:');
        console.log('='.repeat(80));

        // Analyze content types and quality
        const contentTypes = new Map<string, number>();
        const scoreDistribution = { high: 0, medium: 0, low: 0 };

        result.results.forEach((searchResult: any, index: number) => {
          const score = searchResult.score;
          const content = searchResult.chunkText || '';
          const metadata = searchResult.metadata || {};

          // Categorize score
          if (score >= 0.8) scoreDistribution.high++;
          else if (score >= 0.6) scoreDistribution.medium++;
          else scoreDistribution.low++;

          // Analyze content type
          const contentType = analyzeContentType(content);
          contentTypes.set(contentType, (contentTypes.get(contentType) || 0) + 1);

          console.log(`\n${index + 1}. 📊 Score: ${score.toFixed(3)} | Type: ${contentType}`);
          console.log(`   📝 Content Preview: ${content.substring(0, 150)}${content.length > 150 ? '...' : ''}`);
          console.log(`   🏷️  Metadata: Step="${metadata.stepTitle || 'N/A'}" | Content ID="${searchResult.contentId || 'N/A'}"`);
          console.log(`   📏 Content Length: ${content.length} chars`);

          // Flag potential issues
          if (content.includes('{"type":') || content.includes('"data":')) {
            console.log(`   ⚠️  WARNING: Content appears to be JSON/metadata rather than readable text`);
          }
          if (score < 0.6) {
            console.log(`   ⚠️  WARNING: Low similarity score (${score.toFixed(3)}) - may not be relevant`);
          }
        });

        // Summary analysis
        console.log('\n📊 Search Results Summary:');
        console.log('='.repeat(50));
        console.log(`Total Results: ${result.results.length}`);
        console.log(`Score Distribution: High (≥0.8): ${scoreDistribution.high}, Medium (0.6-0.8): ${scoreDistribution.medium}, Low (<0.6): ${scoreDistribution.low}`);
        console.log('Content Types Found:');
        contentTypes.forEach((count, type) => {
          console.log(`  - ${type}: ${count} results`);
        });

        // Quality assessment
        const qualityIssues = [];
        if (scoreDistribution.low > scoreDistribution.high + scoreDistribution.medium) {
          qualityIssues.push('Most results have low similarity scores');
        }
        if (contentTypes.get('JSON/Metadata') && contentTypes.get('JSON/Metadata')! > result.results.length * 0.5) {
          qualityIssues.push('More than 50% of results contain JSON/metadata instead of readable text');
        }
        if (new Set(result.results.map((r: any) => r.score)).size === 1) {
          qualityIssues.push('All results have identical scores - possible indexing issue');
        }

        if (qualityIssues.length > 0) {
          console.log('\n⚠️  Quality Issues Detected:');
          qualityIssues.forEach(issue => console.log(`   - ${issue}`));
        }
      } else {
        console.log('\n📋 No search results found');
      }
    } else {
      log.error('❌ Vector search test failed:', result.error);
    }

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    log.error('💥 Search test failed:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Analyze content type to identify what kind of data is being retrieved
 */
function analyzeContentType(content: string): string {
  if (!content || content.trim().length === 0) {
    return 'Empty';
  }

  // Check for JSON structures
  if (content.includes('{"type":') || content.includes('"data":') || content.includes('{"chart":')) {
    return 'JSON/Metadata';
  }

  // Check for chart/visualization data
  if (content.includes('chart') || content.includes('graph') || content.includes('visualization')) {
    return 'Chart/Visualization';
  }

  // Check for structured data
  if (content.includes('[{') || (content.includes('{') && content.includes('}') && content.includes(','))) {
    return 'Structured Data';
  }

  // Check for readable text
  const sentences = content.split(/[.!?]+/).filter(s => s.trim().length > 10);
  if (sentences.length >= 2) {
    return 'Readable Text';
  }

  // Check for short text/titles
  if (content.length < 100 && !content.includes('{') && !content.includes('[')) {
    return 'Short Text/Title';
  }

  return 'Unknown';
}

/**
 * Test RAG chat with enhanced debugging and analysis
 */
async function testChat(url: string, options: TestChatOptions): Promise<WorkerResponse> {
  try {
    log.info('💬 Testing RAG chat...', options);

    const response = await fetch(`${url}/test-rag-chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(options),
    });

    const result = await response.json();

    if (result.success) {
      const stats = result.stats || {};
      const chatResponse = result.response || {};

      log.info('✅ RAG chat test completed', {
        message: options.message,
        sourcesFound: stats.sourcesFound || 0,
        contextUsed: chatResponse.contextUsed || false,
        processingTime: stats.processingTimeMs || 'N/A',
        contextLength: stats.contextLength || 0,
      });

      console.log('\n🔍 RAG Chat Analysis:');
      console.log('='.repeat(80));

      // Analyze the search-to-chat pipeline
      console.log(`📝 User Message: "${options.message}"`);
      console.log(`🔍 Sources Found: ${stats.sourcesFound || 0}`);
      console.log(`📏 Context Length: ${stats.contextLength || 0} characters`);
      console.log(`🧠 Context Used: ${chatResponse.contextUsed ? '✅ Yes' : '❌ No'}`);
      console.log(`⏱️  Processing Time: ${stats.processingTimeMs || 'N/A'}ms`);

      // Analyze the response
      const responseMessage = chatResponse.message;
      if (responseMessage) {
        console.log('\n🤖 RAG Response:');
        console.log('-'.repeat(50));
        console.log(responseMessage);
        console.log('-'.repeat(50));

        // Analyze response quality
        if (responseMessage === 'undefined' || !responseMessage) {
          console.log('⚠️  WARNING: Response is undefined or empty');
        } else if (responseMessage.includes('No relevant content found')) {
          console.log('⚠️  WARNING: No relevant content found despite sources being available');
        }
      } else {
        console.log('\n❌ No response message found');
        console.log('⚠️  This indicates a problem in the response generation pipeline');
      }

      // Analyze sources
      const sources = chatResponse.sources || [];
      if (sources.length > 0) {
        console.log('\n📚 Sources Analysis:');
        console.log('-'.repeat(50));

        sources.forEach((source: any, index: number) => {
          console.log(`\n${index + 1}. 📊 Score: ${source.score?.toFixed(3) || 'N/A'}`);
          console.log(`   📖 Title: ${source.title || 'N/A'}`);
          console.log(`   🆔 Content ID: ${source.contentId || 'N/A'}`);
          console.log(`   📝 Content: ${source.chunkText || 'N/A'}`);

          // Analyze source quality
          const content = source.chunkText || '';
          const contentType = analyzeContentType(content);
          console.log(`   🏷️  Content Type: ${contentType}`);

          if (contentType === 'JSON/Metadata') {
            console.log(`   ⚠️  WARNING: Source contains JSON/metadata instead of readable text`);
          }
        });
      } else {
        console.log('\n📚 No sources found');
        if (stats.sourcesFound > 0) {
          console.log('⚠️  WARNING: Stats show sources found but none in response - pipeline issue');
        }
      }

      // Diagnostic summary
      console.log('\n🔧 Diagnostic Summary:');
      console.log('='.repeat(50));

      const diagnostics = [];

      // Check for common issues
      if (stats.sourcesFound === 0) {
        diagnostics.push('❌ No sources found - check similarity threshold and content indexing');
      } else if (sources.length === 0) {
        diagnostics.push('❌ Sources found in search but not passed to chat - check pipeline');
      } else if (!chatResponse.contextUsed) {
        diagnostics.push('❌ Context not used despite sources - check context generation');
      } else if (!responseMessage || responseMessage === 'undefined') {
        diagnostics.push('❌ Response generation failed - check LLM integration');
      } else {
        diagnostics.push('✅ RAG pipeline appears to be working correctly');
      }

      // Check similarity threshold issues
      if (sources.length > 0) {
        const avgScore = sources.reduce((sum: number, s: any) => sum + (s.score || 0), 0) / sources.length;
        if (avgScore < 0.6) {
          diagnostics.push(`⚠️  Average similarity score (${avgScore.toFixed(3)}) is low - consider lowering threshold`);
        }
      }

      diagnostics.forEach(diagnostic => console.log(diagnostic));

      // Recommendations
      if (diagnostics.some(d => d.includes('❌'))) {
        console.log('\n💡 Recommendations:');
        console.log('-'.repeat(30));

        if (stats.sourcesFound === 0) {
          console.log('• Check if content is properly indexed in Vectorize');
          console.log('• Try lowering similarity threshold (currently using 0.3 in test)');
          console.log('• Verify embedding model compatibility');
        }

        if (sources.some((s: any) => analyzeContentType(s.chunkText || '') === 'JSON/Metadata')) {
          console.log('• Content appears to be JSON/metadata - check chunking strategy');
          console.log('• Verify text extraction from learning content blocks');
        }

        if (!responseMessage || responseMessage === 'undefined') {
          console.log('• Check LLM API integration and error handling');
          console.log('• Verify context formatting for AI model');
        }
      }

    } else {
      log.error('❌ RAG chat test failed:', result.error);
      console.log('\n🔧 Error Analysis:');
      console.log(`Error: ${result.error}`);
      console.log(`Test Type: ${result.testType || 'Unknown'}`);
    }

    return result;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    log.error('💥 Chat test failed:', errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Display results summary
 */
function displayResults(action: string, result: WorkerResponse) {
  console.log(`\n📊 ${action.toUpperCase()} Test Results:`);
  console.log('='.repeat(50));
  
  if (result.success) {
    console.log('✅ Status: SUCCESS');
    
    // Display action-specific details
    switch (action) {
      case 'health':
        console.log(`🏥 Worker Status: ${result.status}`);
        console.log(`🔗 Vectorize Binding: ${result.vectorizeBinding ? '✅' : '❌'}`);
        console.log(`🔑 Voyage API Key: ${result.voyageApiKey ? '✅' : '❌'}`);
        console.log(`🗄️ Hyperdrive: ${result.hyperdrive ? '✅' : '❌'}`);
        break;
      case 'index':
        console.log(`📝 Content ID: ${result.contentId}`);
        console.log(`📦 Chunks Indexed: ${result.chunksIndexed || 'N/A'}`);
        console.log(`⏱️ Processing Time: ${result.processingTime || 'N/A'}ms`);
        break;
      case 'search':
        console.log(`🔍 Query: "${result.query}"`);
        console.log(`📊 Results Found: ${result.resultsCount || 0}`);
        console.log(`⏱️ Processing Time: ${result.processingTime || 'N/A'}ms`);
        break;
      case 'chat':
        console.log(`💬 Sources Used: ${result.sources?.length || 0}`);
        console.log(`🧠 Context Used: ${result.contextUsed ? '✅' : '❌'}`);
        console.log(`⏱️ Processing Time: ${result.processingTime || 'N/A'}ms`);
        break;
    }
  } else {
    console.log('❌ Status: FAILED');
    console.log(`💥 Error: ${result.error}`);
  }
  
  console.log('='.repeat(50));
}

/**
 * Main function
 */
async function main() {
  const { action, url, options, help } = parseArgs();
  
  if (help) {
    printHelp();
    return;
  }
  
  log.info('🚀 Starting Test RAG Worker caller', { action, url });

  // Add notice about the chunking service fix
  if (action === 'search' || action === 'chat') {
    console.log('\n🔧 NOTICE: Chunking Service Fixed');
    console.log('='.repeat(50));
    console.log('✅ The chunking service has been updated to properly extract text from:');
    console.log('   - paragraph blocks (direct string data)');
    console.log('   - bulletList/numberedList (direct array data)');
    console.log('   - grid blocks (with "content" instead of "description")');
    console.log('   - comparison blocks (array format with label/before/after)');
    console.log('   - keyValueGrid blocks (direct array data)');
    console.log('   - scatterPlot blocks (meaningful chart descriptions)');
    console.log('');
    console.log('🔄 To see the fix in action, content needs to be re-indexed in production.');
    console.log('   The current Vectorize index may still contain old/incomplete data.');
    console.log('='.repeat(50));
  }

  try {
    let result: WorkerResponse;
    
    switch (action) {
      case 'health':
        result = await checkHealth(url);
        break;
      case 'index':
        if (!options.contentId) {
          log.error('❌ Content ID is required for indexing test');
          console.log('Use --content-id <id> to specify the content to index');
          process.exit(1);
        }
        result = await testIndexing(url, options);
        break;
      case 'search':
        if (!options.query) {
          log.error('❌ Query is required for search test');
          console.log('Use --query "<search terms>" to specify the search query');
          process.exit(1);
        }
        result = await testSearch(url, options);
        break;
      case 'chat':
        if (!options.message) {
          log.error('❌ Message is required for chat test');
          console.log('Use --message "<your message>" to specify the chat message');
          process.exit(1);
        }
        result = await testChat(url, options);
        break;
      default:
        log.error('❌ Invalid action. Use: health, index, search, or chat');
        printHelp();
        process.exit(1);
    }
    
    displayResults(action, result);
    
    if (!result.success) {
      process.exit(1);
    }
    
  } catch (error) {
    log.error('💥 Test RAG Worker caller failed:', error);
    process.exit(1);
  }
}

// Run the script
if (import.meta.main) {
  main().catch(console.error);
}